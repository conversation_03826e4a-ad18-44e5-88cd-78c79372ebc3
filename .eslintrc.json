{"root": true, "env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended", "plugin:import/recommended", "plugin:import/typescript", "plugin:@next/next/recommended", "plugin:prettier/recommended"], "plugins": ["@typescript-eslint", "react", "react-hooks", "jsx-a11y", "import", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}, "project": "./tsconfig.json"}, "settings": {"react": {"version": "detect"}, "import/resolver": {"typescript": {}, "node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}}}, "rules": {"prettier/prettier": "error", "react/react-in-jsx-scope": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "alphabetize": {"order": "asc", "caseInsensitive": true}, "newlines-between": "always"}], "react/jsx-filename-extension": ["warn", {"extensions": [".tsx"]}], "react/prop-types": "off"}}