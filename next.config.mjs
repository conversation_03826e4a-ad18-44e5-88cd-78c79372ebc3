/** @type {import('next').NextConfig} */

const nextConfig = {
	images: {
		remotePatterns: [
			{
				protocol: 'https',
				hostname: 'res.cloudinary.com',
			},
			{
				protocol: 'https',
				hostname: 'images.unsplash.com',
			},
			{
				protocol: 'https',
				hostname: 'picsum.photos',
			},
		],
	},
};

// const nextConfig = {
// 	output: 'export', // Enables static export
// 	basePath: '/your-repo-name', // Change this to your GitHub repo name
// 	images: {
// 		unoptimized: true, // Required for static hosting
// 	},
// };

export default nextConfig;
