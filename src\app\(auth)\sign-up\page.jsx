'use client';
import Image from 'next/image';
import logo from '@/assets/harp.png';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import SignUpVerifyEmailForm from '@/components/sign-up-form';
import VerifyOTPForm from '@/components/verify-otp-form';
import CompleteRegistrationForm from '@/components/complete-registeration-form';
import { useAppSelector } from '@/lib/hooks';
import { DotBackground } from '@/components/ui/dot-background';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function OnboardingPage() {
	const { enterToken, isVerified, authenticatedUser, registrationCompleted } =
		useAppSelector((store) => store.auth);
	console.log(`OnboardingPage - registrationCompleted:`, registrationCompleted);
	console.log(`OnboardingPage - authenticatedUser:`, authenticatedUser);
	const router = useRouter();

	useEffect(() => {
		if (authenticatedUser && registrationCompleted) {
			router.push('/onboarding');
		}
	}, [authenticatedUser, registrationCompleted, router]);

	return (
		<div className="w-full max-w-sm">
			{isVerified ? (
				<CompleteRegistrationForm />
			) : !isVerified && enterToken ? (
				<VerifyOTPForm />
			) : (
				<SignUpVerifyEmailForm />
			)}
		</div>
	);
}
