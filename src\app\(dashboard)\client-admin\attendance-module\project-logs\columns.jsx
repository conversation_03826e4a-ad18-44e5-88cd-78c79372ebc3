'use client';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { ArrowUpDown, User, Briefcase, Clock, TrendingUp } from 'lucide-react';
import { DataTableRowActions } from './row-actions';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export const createColumns = (dispatch, isEditing, setIsEditing) => {
	return [
		{
			id: 'select',
			header: ({ table }) => (
				<Checkbox
					checked={table.getIsAllPageRowsSelected()}
					onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Select all"
				/>
			),
			cell: ({ row }) => (
				<Checkbox
					checked={row.getIsSelected()}
					onCheckedChange={(value) => row.toggleSelected(!!value)}
					aria-label="Select row"
				/>
			),
			enableSorting: false,
			enableHiding: false,
		},
		{
			accessorKey: 'name',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Employee
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const employee = row.original;
				return (
					<div className="flex items-center gap-3">
						<Avatar className="h-10 w-10">
							<AvatarImage src={employee.avatar} />
							<AvatarFallback className="bg-primary/10 text-primary font-semibold">
								{employee.name
									.split(' ')
									.map((n) => n[0])
									.join('')}
							</AvatarFallback>
						</Avatar>
						<div>
							<div className="font-medium">{employee.name}</div>
							<div className="text-sm text-muted-foreground">
								{employee.position}
							</div>
						</div>
					</div>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'department',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Department
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Briefcase className="h-4 w-4 text-muted-foreground" />
					<Badge variant="outline">{row.original.department}</Badge>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'totalHours',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Total Hours
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Clock className="h-4 w-4 text-muted-foreground" />
					<span className="font-semibold">{row.original.totalHours}h</span>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'projectCount',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Projects
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const employee = row.original;
				return (
					<div className="flex flex-col items-center">
						<span className="font-medium">{employee.projectCount}</span>
						<span className="text-xs text-muted-foreground">
							{employee.activeProjects} active
						</span>
					</div>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'avgHoursPerWeek',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Avg/Week
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="text-center">
					<span className="font-medium">{row.original.avgHoursPerWeek}h</span>
				</div>
			),
			enableSorting: true,
		},
		// {
		// 	accessorKey: 'status',
		// 	header: 'Status',
		// 	cell: ({ row }) => (
		// 		<Badge
		// 			variant={row.original.status === 'active' ? 'default' : 'secondary'}
		// 		>
		// 			{row.original.status}
		// 		</Badge>
		// 	),
		// 	enableSorting: true,
		// },
		// {
		// 	accessorKey: 'performance',
		// 	header: ({ column }) => (
		// 		<Button
		// 			variant="ghost"
		// 			onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
		// 			className="p-0 hover:bg-transparent"
		// 		>
		// 			Performance
		// 			<ArrowUpDown className="ml-2 h-4 w-4" />
		// 		</Button>
		// 	),
		// 	cell: ({ row }) => {
		// 		const employee = row.original;
		// 		const overallPerformance = Math.round(
		// 			(employee.performance.efficiency +
		// 				employee.performance.quality +
		// 				employee.performance.collaboration) /
		// 				3
		// 		);
		// 		return (
		// 			<div className="flex items-center gap-2">
		// 				<TrendingUp className="h-4 w-4 text-muted-foreground" />
		// 				<div className="flex flex-col items-center">
		// 					<span className="font-medium">{overallPerformance}%</span>
		// 					<div className="text-xs text-muted-foreground">
		// 						E:{employee.performance.efficiency} Q:
		// 						{employee.performance.quality} C:
		// 						{employee.performance.collaboration}
		// 					</div>
		// 				</div>
		// 			</div>
		// 		);
		// 	},
		// 	enableSorting: true,
		// 	sortingFn: (rowA, rowB) => {
		// 		const perfA = Math.round(
		// 			(rowA.original.performance.efficiency +
		// 				rowA.original.performance.quality +
		// 				rowA.original.performance.collaboration) /
		// 				3
		// 		);
		// 		const perfB = Math.round(
		// 			(rowB.original.performance.efficiency +
		// 				rowB.original.performance.quality +
		// 				rowB.original.performance.collaboration) /
		// 				3
		// 		);
		// 		return perfA - perfB;
		// 	},
		// },
		{
			id: 'actions',
			cell: ({ row }) => <DataTableRowActions row={row} dispatch={dispatch} />,
		},
	];
};
