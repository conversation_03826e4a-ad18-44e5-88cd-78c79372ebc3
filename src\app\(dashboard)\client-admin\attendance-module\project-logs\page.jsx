'use client';

import { useState } from 'react';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
	Clock,
	Users,
	Briefcase,
	TrendingUp,
	Search,
	Filter,
} from 'lucide-react';
import ProjectLogsTable from './table';

// Sample data
const employeesData = [
	{
		id: 1,
		name: '<PERSON>',
		email: '<EMAIL>',
		phone: '+****************',
		avatar: null,
		department: 'Engineering',
		position: 'Senior Developer',
		totalHours: 156,
		projectCount: 3,
		activeProjects: 2,
		completedProjects: 1,
		avgHoursPerWeek: 39,
		lastActive: '2024-01-25',
		status: 'active',
		projects: [
			{
				name: 'Website Redesign',
				hours: 89,
				status: 'active',
				role: 'Lead Developer',
			},
			{
				name: 'Mobile App',
				hours: 45,
				status: 'active',
				role: 'Backend Developer',
			},
			{
				name: 'API Integration',
				hours: 22,
				status: 'completed',
				role: 'Integration Specialist',
			},
		],
		performance: {
			efficiency: 92,
			quality: 88,
			collaboration: 95,
		},
	},
	{
		id: 2,
		name: 'Jane Smith',
		email: '<EMAIL>',
		phone: '+****************',
		avatar: null,
		department: 'Design',
		position: 'UI/UX Designer',
		totalHours: 98,
		projectCount: 2,
		activeProjects: 1,
		completedProjects: 1,
		avgHoursPerWeek: 24.5,
		lastActive: '2024-01-24',
		status: 'active',
		projects: [
			{
				name: 'Website Redesign',
				hours: 67,
				status: 'active',
				role: 'UI Designer',
			},
			{
				name: 'Brand Guidelines',
				hours: 31,
				status: 'completed',
				role: 'Lead Designer',
			},
		],
		performance: {
			efficiency: 89,
			quality: 94,
			collaboration: 91,
		},
	},
	{
		id: 3,
		name: 'Mike Johnson',
		email: '<EMAIL>',
		phone: '+****************',
		avatar: null,
		department: 'Engineering',
		position: 'Full Stack Developer',
		totalHours: 203,
		projectCount: 4,
		activeProjects: 3,
		completedProjects: 1,
		avgHoursPerWeek: 50.75,
		lastActive: '2024-01-25',
		status: 'active',
		projects: [
			{
				name: 'Mobile App',
				hours: 78,
				status: 'active',
				role: 'Mobile Developer',
			},
			{
				name: 'API Integration',
				hours: 56,
				status: 'active',
				role: 'Backend Developer',
			},
			{
				name: 'Database Migration',
				hours: 45,
				status: 'active',
				role: 'Database Specialist',
			},
			{
				name: 'Legacy System',
				hours: 24,
				status: 'completed',
				role: 'Migration Lead',
			},
		],
		performance: {
			efficiency: 87,
			quality: 90,
			collaboration: 85,
		},
	},
	{
		id: 4,
		name: 'Sarah Wilson',
		email: '<EMAIL>',
		phone: '+****************',
		avatar: null,
		department: 'Design',
		position: 'Graphic Designer',
		totalHours: 85,
		projectCount: 3,
		activeProjects: 2,
		completedProjects: 1,
		avgHoursPerWeek: 21.25,
		lastActive: '2024-01-23',
		status: 'active',
		projects: [
			{
				name: 'Website Redesign',
				hours: 34,
				status: 'active',
				role: 'Visual Designer',
			},
			{ name: 'Mobile App', hours: 23, status: 'active', role: 'UI Designer' },
			{
				name: 'Brand Guidelines',
				hours: 28,
				status: 'completed',
				role: 'Brand Designer',
			},
		],
		performance: {
			efficiency: 91,
			quality: 96,
			collaboration: 93,
		},
	},
	{
		id: 5,
		name: 'David Brown',
		email: '<EMAIL>',
		phone: '+****************',
		avatar: null,
		department: 'Marketing',
		position: 'Marketing Specialist',
		totalHours: 67,
		projectCount: 2,
		activeProjects: 1,
		completedProjects: 1,
		avgHoursPerWeek: 16.75,
		lastActive: '2024-01-22',
		status: 'inactive',
		projects: [
			{
				name: 'Website Redesign',
				hours: 45,
				status: 'active',
				role: 'Content Strategist',
			},
			{
				name: 'Brand Guidelines',
				hours: 22,
				status: 'completed',
				role: 'Marketing Lead',
			},
		],
		performance: {
			efficiency: 85,
			quality: 88,
			collaboration: 90,
		},
	},
];

export default function ProjectLogsPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [departmentFilter, setDepartmentFilter] = useState('all');

	// Filter data for summary stats
	const filteredData = employeesData.filter((employee) => {
		if (
			searchTerm &&
			!employee.name.toLowerCase().includes(searchTerm.toLowerCase())
		)
			return false;
		if (departmentFilter !== 'all' && employee.department !== departmentFilter)
			return false;
		return true;
	});

	return (
		<section className="w-full h-full p-4">
			<header className="flex flex-col gap-2">
				<h1 className="text-xl md:text-2xl font-semibold">Project Logs</h1>
				<p className="font-medium text-gray-500">
					Track employee project assignments and performance metrics.
				</p>
				<Separator />
			</header>

			<main className="grid flex-1 items-start gap-4 md:gap-8 w-full mt-4">
				{/* Filters */}
				<div className="flex gap-4 items-center flex-wrap">
					<div className="relative flex-1 min-w-[200px] max-w-sm">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
						<Input
							placeholder="Search employees..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10"
						/>
					</div>

					<Select value={departmentFilter} onValueChange={setDepartmentFilter}>
						<SelectTrigger className="w-48">
							<Filter className="h-4 w-4 mr-2" />
							<SelectValue placeholder="Filter by department" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All Departments</SelectItem>
							<SelectItem value="Engineering">Engineering</SelectItem>
							<SelectItem value="Design">Design</SelectItem>
							<SelectItem value="Marketing">Marketing</SelectItem>
						</SelectContent>
					</Select>

					{/* <Select value={statusFilter} onValueChange={setStatusFilter}>
						<SelectTrigger className="w-48">
							<SelectValue placeholder="Filter by status" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All Status</SelectItem>
							<SelectItem value="active">Active</SelectItem>
							<SelectItem value="inactive">Inactive</SelectItem>
						</SelectContent>
					</Select> */}

					{/* <Button variant="outline" size="sm">
						<Download className="h-4 w-4 mr-2" />
						Export Data
					</Button> */}
				</div>

				{/* Summary Stats */}
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Total Employees
									</p>
									<p className="text-3xl font-bold">{filteredData.length}</p>
								</div>
								<Users className="h-8 w-8 text-muted-foreground" />
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Total Hours
									</p>
									<p className="text-3xl font-bold">
										{filteredData.reduce((sum, emp) => sum + emp.totalHours, 0)}
										h
									</p>
								</div>
								<Clock className="h-8 w-8 text-muted-foreground" />
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Active Projects
									</p>
									<p className="text-3xl font-bold">
										{filteredData.reduce(
											(sum, emp) => sum + emp.activeProjects,
											0
										)}
									</p>
								</div>
								<Briefcase className="h-8 w-8 text-muted-foreground" />
							</div>
						</CardContent>
					</Card>

					{/* <Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Avg Performance
									</p>
									<p className="text-3xl font-bold">
										{filteredData.length > 0
											? Math.round(
													filteredData.reduce(
														(sum, emp) =>
															sum +
															(emp.performance.efficiency +
																emp.performance.quality +
																emp.performance.collaboration) /
																3,
														0
													) / filteredData.length
												)
											: 0}
										%
									</p>
								</div>
								<TrendingUp className="h-8 w-8 text-muted-foreground" />
							</div>
						</CardContent>
					</Card> */}
				</div>

				{/* Data Table */}
				<Card>
					<CardHeader>
						<CardTitle>Employee Project Data</CardTitle>
						<CardDescription>
							Comprehensive view of employee project assignments and performance
						</CardDescription>
					</CardHeader>
					<CardContent>
						<ProjectLogsTable />
					</CardContent>
				</Card>
			</main>
		</section>
	);
}
