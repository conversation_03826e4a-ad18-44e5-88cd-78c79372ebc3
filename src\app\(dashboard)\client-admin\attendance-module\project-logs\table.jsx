'use client';

import { useState, useCallback, useMemo } from 'react';
import {
	getCoreRowModel,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	useReactTable,
} from '@tanstack/react-table';
import { TableActions } from '@/components/table-actions';
import { TablePagination } from '@/components/table-pagination';
import { createColumns } from './columns';
import debounce from 'lodash.debounce';
import { TBody } from '@/components/table-body';
import { THeader } from '@/components/table-header';
import { Table } from '@/components/ui/table';

// Sample data - this would normally come from Redux store or API
const employeesData = [
	{
		id: 1,
		_id: '1', // Add _id for consistency with other tables
		name: '<PERSON>',
		email: '<EMAIL>',
		phone: '+****************',
		avatar: null,
		department: 'Engineering',
		position: 'Senior Developer',
		totalHours: 156,
		projectCount: 3,
		activeProjects: 2,
		completedProjects: 1,
		avgHoursPerWeek: 39,
		lastActive: '2024-01-25',
		status: 'active',
		projects: [
			{
				name: 'Website Redesign',
				hours: 89,
				status: 'active',
				role: 'Lead Developer',
			},
			{
				name: 'Mobile App',
				hours: 45,
				status: 'active',
				role: 'Backend Developer',
			},
			{
				name: 'API Integration',
				hours: 22,
				status: 'completed',
				role: 'Integration Specialist',
			},
		],
		performance: {
			efficiency: 92,
			quality: 88,
			collaboration: 95,
		},
	},
	{
		id: 2,
		_id: '2',
		name: 'Jane Smith',
		email: '<EMAIL>',
		phone: '+****************',
		avatar: null,
		department: 'Design',
		position: 'UI/UX Designer',
		totalHours: 98,
		projectCount: 2,
		activeProjects: 1,
		completedProjects: 1,
		avgHoursPerWeek: 24.5,
		lastActive: '2024-01-24',
		status: 'active',
		projects: [
			{
				name: 'Website Redesign',
				hours: 67,
				status: 'active',
				role: 'UI Designer',
			},
			{
				name: 'Brand Guidelines',
				hours: 31,
				status: 'completed',
				role: 'Lead Designer',
			},
		],
		performance: {
			efficiency: 89,
			quality: 94,
			collaboration: 91,
		},
	},
	{
		id: 3,
		_id: '3',
		name: 'Mike Johnson',
		email: '<EMAIL>',
		phone: '+****************',
		avatar: null,
		department: 'Engineering',
		position: 'Full Stack Developer',
		totalHours: 203,
		projectCount: 4,
		activeProjects: 3,
		completedProjects: 1,
		avgHoursPerWeek: 50.75,
		lastActive: '2024-01-25',
		status: 'active',
		projects: [
			{
				name: 'Mobile App',
				hours: 78,
				status: 'active',
				role: 'Mobile Developer',
			},
			{
				name: 'API Integration',
				hours: 56,
				status: 'active',
				role: 'Backend Developer',
			},
			{
				name: 'Database Migration',
				hours: 45,
				status: 'active',
				role: 'Database Specialist',
			},
			{
				name: 'Legacy System',
				hours: 24,
				status: 'completed',
				role: 'Migration Lead',
			},
		],
		performance: {
			efficiency: 87,
			quality: 90,
			collaboration: 85,
		},
	},
	{
		id: 4,
		_id: '4',
		name: 'Sarah Wilson',
		email: '<EMAIL>',
		phone: '+****************',
		avatar: null,
		department: 'Design',
		position: 'Graphic Designer',
		totalHours: 85,
		projectCount: 3,
		activeProjects: 2,
		completedProjects: 1,
		avgHoursPerWeek: 21.25,
		lastActive: '2024-01-23',
		status: 'active',
		projects: [
			{
				name: 'Website Redesign',
				hours: 34,
				status: 'active',
				role: 'Visual Designer',
			},
			{ name: 'Mobile App', hours: 23, status: 'active', role: 'UI Designer' },
			{
				name: 'Brand Guidelines',
				hours: 28,
				status: 'completed',
				role: 'Brand Designer',
			},
		],
		performance: {
			efficiency: 91,
			quality: 96,
			collaboration: 93,
		},
	},
	{
		id: 5,
		_id: '5',
		name: 'David Brown',
		email: '<EMAIL>',
		phone: '+****************',
		avatar: null,
		department: 'Marketing',
		position: 'Marketing Specialist',
		totalHours: 67,
		projectCount: 2,
		activeProjects: 1,
		completedProjects: 1,
		avgHoursPerWeek: 16.75,
		lastActive: '2024-01-22',
		status: 'inactive',
		projects: [
			{
				name: 'Website Redesign',
				hours: 45,
				status: 'active',
				role: 'Content Strategist',
			},
			{
				name: 'Brand Guidelines',
				hours: 22,
				status: 'completed',
				role: 'Marketing Lead',
			},
		],
		performance: {
			efficiency: 85,
			quality: 88,
			collaboration: 90,
		},
	},
];

export default function ProjectLogsTable() {
	const [sorting, setSorting] = useState([]);
	const [columnFilters, setColumnFilters] = useState([]);
	const [columnVisibility, setColumnVisibility] = useState({});
	const [rowSelection, setRowSelection] = useState({});
	const [globalFilter, setGlobalFilter] = useState('');
	const [isEditing, setIsEditing] = useState(false);

	// In a real app, this would come from Redux store
	const data = employeesData;
	const isLoading = false;

	const columns = useMemo(
		() => createColumns(null, isEditing, setIsEditing), // dispatch would be passed here in real app
		[isEditing, setIsEditing]
	);

	const table = useReactTable({
		data,
		columns,
		onSortingChange: setSorting,
		onColumnFiltersChange: setColumnFilters,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		onColumnVisibilityChange: setColumnVisibility,
		onRowSelectionChange: setRowSelection,
		onGlobalFilterChange: setGlobalFilter,
		state: {
			sorting,
			columnFilters,
			columnVisibility,
			rowSelection,
			globalFilter,
		},
	});

	const debouncedSetGlobalFilter = useMemo(
		() =>
			debounce((value) => {
				setGlobalFilter(value);
			}, 300),
		[]
	);

	const handleFilterChange = useCallback(
		(event) => {
			debouncedSetGlobalFilter(event.target.value);
		},
		[debouncedSetGlobalFilter]
	);

	const handleBulkAction = async (action) => {
		const selectedRows = table.getFilteredSelectedRowModel().rows;

		switch (action) {
			case 'export':
				const selectedIds = selectedRows.map((row) => row.original._id);
				console.log('Exporting employees:', selectedIds);
				// TODO: Implement export logic
				break;

			default:
				break;
		}
	};

	return (
		<div className="overflow-x-auto">
			<TableActions
				table={table}
				handleFilterChange={handleFilterChange}
				handleBulkAction={handleBulkAction}
				// bulkActions={[{ label: 'Export Selected', value: 'export' }]}
				bulkActions={[]}
			/>
			<div className="rounded-md border max-w-[95vw] lg:max-w-full">
				<Table>
					<THeader table={table} />
					<TBody table={table} isLoading={isLoading} />
				</Table>
			</div>
			<TablePagination table={table} />
		</div>
	);
}
