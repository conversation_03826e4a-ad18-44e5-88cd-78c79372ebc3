'use client';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { ArrowUpDown, Clock, Calendar, Shield } from 'lucide-react';
import { DataTableRowActions } from './row-actions';
import { DataTableCellContent } from './details-popover';
import { Badge } from '@/components/ui/badge';
import { formatDate } from '@/lib/utils';

export const createColumns = (dispatch) => {
	return [
		{
			id: 'select',
			header: ({ table }) => (
				<Checkbox
					checked={table?.getIsAllPageRowsSelected?.() || false}
					onCheckedChange={(value) =>
						table?.toggleAllPageRowsSelected?.(!!value)
					}
					aria-label="Select all"
				/>
			),
			cell: ({ row }) => (
				<Checkbox
					checked={row?.getIsSelected?.() || false}
					onCheckedChange={(value) => row?.toggleSelected?.(!!value)}
					aria-label="Select row"
				/>
			),
			enableSorting: false,
			enableHiding: false,
		},
		{
			accessorKey: 'code',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Code
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => <div className="font-medium">{row.original.code}</div>,
			enableSorting: true,
		},
		{
			accessorKey: 'name',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Shift Name
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				return (
					<DataTableCellContent
						type="shift"
						value={row.original.name}
						details={{
							id: row.original._id,
							code: row.original.code,
							startTime: row.original.startTime,
							endTime: row.original.endTime,
							isDefault: row.original.isDefault,
							allowedDays: row.original.allowedAttendanceDays,
							features: {
								wifiCheck: row.original.wifiCheckEnabled,
								locationCheck: row.original.workLocationCheckEnabled,
								qrCheck: row.original.qrClockInCheckEnabled,
								facialCheck: row.original.facialCheckEnabled,
							},
						}}
					/>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'isDefault',
			header: 'Type',
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Shield className="size-4 text-muted-foreground" />
					{row.original.isDefault ? (
						<Badge variant="default">Default</Badge>
					) : (
						<Badge variant="secondary">Custom</Badge>
					)}
				</div>
			),
		},
		{
			accessorKey: 'startTime',
			header: 'Working Hours',
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Clock className="size-4 text-muted-foreground" />
					<span>
						{row.original.startTime} - {row.original.endTime}
					</span>
				</div>
			),
		},
		{
			accessorKey: 'allowedAttendanceDays',
			header: 'Working Days',
			cell: ({ row }) => {
				const days = row.original.allowedAttendanceDays || [];
				const dayCount = days.length;
				return (
					<div className="flex items-center gap-2">
						<Calendar className="size-4 text-muted-foreground" />
						<span>{dayCount} days/week</span>
					</div>
				);
			},
		},
		{
			accessorKey: 'features',
			header: 'Features',
			cell: ({ row }) => {
				const features = [];
				if (row.original.wifiCheckEnabled) features.push('WiFi');
				if (row.original.workLocationCheckEnabled) features.push('Location');
				if (row.original.qrClockInCheckEnabled) features.push('QR');
				if (row.original.facialCheckEnabled) features.push('Facial');

				return (
					<div className="flex flex-wrap gap-1">
						{features.length > 0 ? (
							features.map((feature) => (
								<Badge key={feature} variant="outline" className="text-xs">
									{feature}
								</Badge>
							))
						) : (
							<span className="text-muted-foreground text-sm">Basic</span>
						)}
					</div>
				);
			},
		},
		{
			accessorKey: 'createdAt',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Created
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const date = row.original.createdAt;
				return <span>{date ? formatDate(date) : 'N/A'}</span>;
			},
			enableSorting: true,
		},
		{
			id: 'actions',
			cell: ({ row }) => <DataTableRowActions row={row} dispatch={dispatch} />,
		},
	];
};
