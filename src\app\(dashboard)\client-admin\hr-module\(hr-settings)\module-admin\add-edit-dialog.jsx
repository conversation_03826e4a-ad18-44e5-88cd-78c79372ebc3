'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	createModuleAdmin,
	updateModuleAdmin,
} from '@/lib/features/module-admin/moduleAdminSlice';
import { fetchAllEmployees } from '@/lib/features/employees/employeeSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { addUpdateModuleAdminSchema } from '@/lib/schemas/companySchema';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { Check, ChevronsUpDown, Loader2, XCircle, Plus } from 'lucide-react';
import React, { useEffect, useMemo, useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';

// Available modules
const availableModules = [
	{ value: 'hr', label: 'HR Module', color: 'bg-blue-500' },
	{ value: 'leave', label: 'Leave Module', color: 'bg-green-500' },
	{ value: 'attendance', label: 'Attendance Module', color: 'bg-yellow-500' },
	{ value: 'expense', label: 'Expense Module', color: 'bg-red-500' },
	{ value: 'payroll', label: 'Payroll Module', color: 'bg-purple-500' },
	{ value: 'project', label: 'Project Module', color: 'bg-emerald-500' },
	{ value: 'communication', label: 'Communication Module', color: 'bg-sky-500' },
	{ value: 'performance', label: 'Performance Module', color: 'bg-indigo-500' },
	{ value: 'system', label: 'System Settings Module', color: 'bg-rose-500' },
];

const ModuleAdminAddEditDialog = ({
	isAdd,
	title,
	desc,
	moduleAdmin,
	showAddEditDialog,
	setShowAddEditDialog,
}) => {
	const dispatch = useAppDispatch();
	const { employees } = useAppSelector((store) => store.employee);
	const { isLoading } = useAppSelector((store) => store.moduleAdmin);

	const form = useForm({
		resolver: zodResolver(addUpdateModuleAdminSchema),
		defaultValues: {
			moduleAdmins: isAdd
				? [{ employeeId: '', moduleAdminAccess: [] }]
				: [
						{
							_id: moduleAdmin?._id || '',
							employeeId: moduleAdmin?.employeeId || '',
							moduleAdminAccess: moduleAdmin?.moduleAdminAccess || [],
						},
				  ],
		},
		mode: 'onSubmit',
		reValidateMode: 'onSubmit',
	});

	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: 'moduleAdmins',
	});

	useEffect(() => {
		dispatch(fetchAllEmployees());
	}, [dispatch]);

	useEffect(() => {
		if (!isAdd && moduleAdmin) {
			form.reset({
				moduleAdmins: [
					{
						_id: moduleAdmin._id,
						employeeId: moduleAdmin.employeeId,
						moduleAdminAccess: moduleAdmin.moduleAdminAccess || [],
					},
				],
			});
		}
	}, [moduleAdmin, isAdd, form]);

	const onSubmit = async ({ moduleAdmins }) => {
		const payload = moduleAdmins.map(({ _id, ...rest }) =>
			_id ? { _id, ...rest } : rest
		);

		const result = isAdd
			? await dispatch(createModuleAdmin(payload))
			: await dispatch(updateModuleAdmin(payload[0]));

		if (
			createModuleAdmin.fulfilled.match(result) ||
			updateModuleAdmin.fulfilled.match(result)
		) {
			setShowAddEditDialog(false);
			form.reset();
		}
	};

	const addModuleAdmin = () => {
		if (fields.length < 5) {
			append({ employeeId: '', moduleAdminAccess: [] });
		}
	};

	const getEmployeeName = (employeeId) => {
		const employee = employees.find((emp) => emp._id === employeeId);
		return employee ? employee.personalDetails?.name || 'Unknown' : 'Select Employee';
	};

	const getEmployeeDetails = (employeeId) => {
		const employee = employees.find((emp) => emp._id === employeeId);
		return employee || null;
	};

	return (
		<Dialog open={showAddEditDialog} onOpenChange={setShowAddEditDialog}>
			<DialogContent className="max-h-[80vh] overflow-hidden max-w-2xl">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{desc}</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form
						id="module-admin-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="grid gap-4 max-h-[50vh] overflow-y-auto pt-2 pb-2 pr-2"
					>
						{fields.map((moduleAdminField, index) => (
							<div
								key={moduleAdminField.id}
								className="relative grid gap-4 border p-4 rounded-lg"
							>
								{isAdd && fields.length > 1 && (
									<button
										type="button"
										onClick={() => remove(index)}
										className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
									>
										<XCircle className="text-red-500" size={16} />
									</button>
								)}

								{/* Employee Selection */}
								<FormField
									control={form.control}
									name={`moduleAdmins.${index}.employeeId`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>Employee *</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
												disabled={!isAdd}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select an employee">
															{field.value && (
																<div className="flex items-center gap-2">
																	<Avatar className="h-6 w-6">
																		<AvatarImage
																			src={`/abstract-geometric-shapes.png?height=24&width=24&query=${encodeURIComponent(
																				getEmployeeName(field.value)
																			)}`}
																			alt={getEmployeeName(field.value)}
																		/>
																		<AvatarFallback className="text-xs">
																			{getEmployeeName(field.value)
																				.split(' ')
																				.map((n) => n[0])
																				.join('')
																				.toUpperCase()
																				.substring(0, 2)}
																		</AvatarFallback>
																	</Avatar>
																	<span>{getEmployeeName(field.value)}</span>
																</div>
															)}
														</SelectValue>
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<ScrollArea className="h-48">
														{employees.map((employee) => (
															<SelectItem
																key={employee._id}
																value={employee._id}
															>
																<div className="flex items-center gap-2">
																	<Avatar className="h-6 w-6">
																		<AvatarImage
																			src={`/abstract-geometric-shapes.png?height=24&width=24&query=${encodeURIComponent(
																				employee.personalDetails?.name || 'Unknown'
																			)}`}
																			alt={employee.personalDetails?.name || 'Unknown'}
																		/>
																		<AvatarFallback className="text-xs">
																			{(employee.personalDetails?.name || 'UN')
																				.split(' ')
																				.map((n) => n[0])
																				.join('')
																				.toUpperCase()
																				.substring(0, 2)}
																		</AvatarFallback>
																	</Avatar>
																	<div>
																		<div className="font-medium">
																			{employee.personalDetails?.name || 'Unknown'}
																		</div>
																		<div className="text-xs text-muted-foreground">
																			{employee.employeeOrgId} • {employee.personalDetails?.email}
																		</div>
																	</div>
																</div>
															</SelectItem>
														))}
													</ScrollArea>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Module Selection */}
								<FormField
									control={form.control}
									name={`moduleAdmins.${index}.moduleAdminAccess`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												Module Access * {isAdd ? '(Max 5 modules)' : '(Single module in edit mode)'}
											</FormLabel>
											<div className="grid grid-cols-2 gap-2 p-3 border rounded-md">
												{availableModules.map((module) => (
													<div key={module.value} className="flex items-center space-x-2">
														<Checkbox
															id={`${index}-${module.value}`}
															checked={field.value?.includes(module.value)}
															onCheckedChange={(checked) => {
																if (checked) {
																	// For edit mode, only allow single selection
																	if (!isAdd) {
																		field.onChange([module.value]);
																	} else {
																		// For add mode, allow up to 5 selections
																		if (field.value?.length < 5) {
																			field.onChange([...(field.value || []), module.value]);
																		}
																	}
																} else {
																	field.onChange(
																		field.value?.filter((val) => val !== module.value) || []
																	);
																}
															}}
															disabled={
																!isAdd
																	? false
																	: field.value?.length >= 5 && !field.value?.includes(module.value)
															}
														/>
														<label
															htmlFor={`${index}-${module.value}`}
															className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
														>
															<Badge variant="outline" className={cn('text-white', module.color)}>
																{module.label}
															</Badge>
														</label>
													</div>
												))}
											</div>
											{field.value?.length > 0 && (
												<div className="flex flex-wrap gap-1 mt-2">
													<span className="text-sm text-muted-foreground">Selected:</span>
													{field.value.map((moduleValue) => {
														const module = availableModules.find((m) => m.value === moduleValue);
														return (
															<Badge key={moduleValue} variant="secondary">
																{module?.label || moduleValue}
															</Badge>
														);
													})}
												</div>
											)}
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						))}

						{/* Add More Button - Only for Add Mode */}
						{isAdd && fields.length < 5 && (
							<Button
								type="button"
								variant="outline"
								onClick={addModuleAdmin}
								className="w-full"
							>
								<Plus className="mr-2 h-4 w-4" />
								Add Another Module Admin (Max 5)
							</Button>
						)}
					</form>
				</Form>
				<DialogFooter>
					<Button
						variant="outline"
						onClick={() => setShowAddEditDialog(false)}
						disabled={isLoading}
					>
						Cancel
					</Button>
					<Button
						type="submit"
						form="module-admin-form"
						disabled={isLoading}
					>
						{isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
						{isAdd ? 'Add Module Admin' : 'Update Module Admin'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default ModuleAdminAddEditDialog;
