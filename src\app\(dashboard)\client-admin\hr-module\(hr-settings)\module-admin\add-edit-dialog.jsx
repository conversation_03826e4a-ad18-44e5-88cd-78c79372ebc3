'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
} from '@/components/ui/command';
import {
	createModuleAdmin,
	updateModuleAdmin,
} from '@/lib/features/module-admin/moduleAdminSlice';
import { fetchAllEmployees } from '@/lib/features/employees/employeeSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { moduleNames } from '@/lib/schemas/moduleAdminSchema';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { Check, ChevronsUpDown, Loader2, XCircle, Plus, X } from 'lucide-react';
import React, { useEffect } from 'react';
import { z } from 'zod';
import { useForm, useFieldArray } from 'react-hook-form';

// Available modules from schema
const availableModules = Object.keys(moduleNames).map((key) => ({
	value: key,
	label: moduleNames[key],
}));

// Form schema for the dialog (matches form structure)
const formSchema = z.object({
	moduleAdmins: z
		.array(
			z.object({
				employeeId: z.string().min(1, 'Employee is required'),
				modules: z
					.array(z.enum(Object.keys(moduleNames)))
					.min(1, 'At least one module is required')
					.max(5, 'Maximum 5 modules allowed'),
			})
		)
		.min(1, 'At least one module admin is required'),
});

const ModuleAdminAddEditDialog = ({
	isAdd,
	title,
	desc,
	moduleAdmin,
	showAddEditDialog,
	setShowAddEditDialog,
}) => {
	const dispatch = useAppDispatch();
	const { employees } = useAppSelector((store) => store.employee);
	const { isLoading } = useAppSelector((store) => store.moduleAdmin);

	const form = useForm({
		resolver: zodResolver(formSchema),
		defaultValues: {
			moduleAdmins: isAdd
				? [{ employeeId: '', modules: [] }]
				: [
						{
							employeeId: moduleAdmin?.employeeId || '',
							modules: moduleAdmin?.modules || [],
						},
					],
		},
		mode: 'onSubmit',
		reValidateMode: 'onSubmit',
	});

	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: 'moduleAdmins',
	});

	useEffect(() => {
		dispatch(fetchAllEmployees());
	}, [dispatch]);

	useEffect(() => {
		if (!isAdd && moduleAdmin) {
			form.reset({
				moduleAdmins: [
					{
						employeeId: moduleAdmin.employeeId,
						modules: moduleAdmin.modules || [],
					},
				],
			});
		}
	}, [moduleAdmin, isAdd, form]);

	const onSubmit = async ({ moduleAdmins }) => {
		// Map the form data to match backend schema
		const payload = {
			moduleAdmins: moduleAdmins.map(({ employeeId, modules }) => ({
				employeeId,
				modules,
			})),
		};

		const result = isAdd
			? await dispatch(createModuleAdmin(payload))
			: await dispatch(
					updateModuleAdmin({
						employeeId: moduleAdmins[0].employeeId,
						modules: moduleAdmins[0].modules,
					})
				);

		if (
			createModuleAdmin.fulfilled.match(result) ||
			updateModuleAdmin.fulfilled.match(result)
		) {
			setShowAddEditDialog(false);
			form.reset();
		}
	};

	const addModuleAdmin = () => {
		if (fields.length < 5) {
			append({ employeeId: '', modules: [] });
		}
	};

	const getEmployeeName = (employeeId) => {
		const employee = employees.find((emp) => emp._id === employeeId);
		return employee
			? employee.personalDetails?.name || 'Unknown'
			: 'Select Employee';
	};

	return (
		<Dialog open={showAddEditDialog} onOpenChange={setShowAddEditDialog}>
			<DialogContent className="max-h-[80vh] overflow-hidden max-w-2xl">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{desc}</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form
						id="module-admin-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="grid gap-4 max-h-[50vh] overflow-y-auto pt-2 pb-2 pr-2"
					>
						{fields.map((moduleAdminField, index) => (
							<div
								key={moduleAdminField.id}
								className="relative grid gap-4 border p-4 rounded-lg"
							>
								{isAdd && fields.length > 1 && (
									<button
										type="button"
										onClick={() => remove(index)}
										className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
									>
										<XCircle className="text-red-500" size={16} />
									</button>
								)}

								{/* Employee Selection */}
								<FormField
									control={form.control}
									name={`moduleAdmins.${index}.employeeId`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>Employee *</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
												disabled={!isAdd}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select an employee">
															{field.value && (
																<div className="flex items-center gap-2">
																	<Avatar className="h-6 w-6">
																		<AvatarImage
																			src={`/abstract-geometric-shapes.png?height=24&width=24&query=${encodeURIComponent(
																				getEmployeeName(field.value)
																			)}`}
																			alt={getEmployeeName(field.value)}
																		/>
																		<AvatarFallback className="text-xs">
																			{getEmployeeName(field.value)
																				.split(' ')
																				.map((n) => n[0])
																				.join('')
																				.toUpperCase()
																				.substring(0, 2)}
																		</AvatarFallback>
																	</Avatar>
																	<span>{getEmployeeName(field.value)}</span>
																</div>
															)}
														</SelectValue>
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<ScrollArea className="h-48">
														{employees.map((employee) => (
															<SelectItem
																key={employee._id}
																value={employee._id}
															>
																<div className="flex items-center gap-2">
																	<Avatar className="h-6 w-6">
																		<AvatarImage
																			src={`/abstract-geometric-shapes.png?height=24&width=24&query=${encodeURIComponent(
																				employee.personalDetails?.name ||
																					'Unknown'
																			)}`}
																			alt={
																				employee.personalDetails?.name ||
																				'Unknown'
																			}
																		/>
																		<AvatarFallback className="text-xs">
																			{(employee.personalDetails?.name || 'UN')
																				.split(' ')
																				.map((n) => n[0])
																				.join('')
																				.toUpperCase()
																				.substring(0, 2)}
																		</AvatarFallback>
																	</Avatar>
																	<div>
																		<div className="font-medium">
																			{employee.personalDetails?.name ||
																				'Unknown'}
																		</div>
																		<div className="text-xs text-muted-foreground">
																			{employee.employeeOrgId} •{' '}
																			{employee.personalDetails?.email}
																		</div>
																	</div>
																</div>
															</SelectItem>
														))}
													</ScrollArea>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Module Selection */}
								<FormField
									control={form.control}
									name={`moduleAdmins.${index}.modules`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												Module Access *{' '}
												{isAdd
													? '(Max 5 modules)'
													: '(Single module in edit mode)'}
											</FormLabel>
											<Popover>
												<PopoverTrigger asChild>
													<FormControl>
														<Button
															variant="outline"
															role="combobox"
															className={cn(
																'w-full justify-between',
																!field.value?.length && 'text-muted-foreground'
															)}
														>
															{field.value?.length > 0
																? `${field.value.length} module${
																		field.value.length > 1 ? 's' : ''
																	} selected`
																: 'Select modules...'}
															<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
														</Button>
													</FormControl>
												</PopoverTrigger>
												<PopoverContent className="w-full p-0">
													<Command>
														<CommandInput placeholder="Search modules..." />
														<CommandEmpty>No modules found.</CommandEmpty>
														<CommandGroup>
															<ScrollArea className="h-48">
																{availableModules.map((module) => (
																	<CommandItem
																		key={module.value}
																		onSelect={() => {
																			const currentValues = field.value || [];
																			const isSelected = currentValues.includes(
																				module.value
																			);

																			if (isSelected) {
																				// Remove module
																				field.onChange(
																					currentValues.filter(
																						(val) => val !== module.value
																					)
																				);
																			} else {
																				// Add module with restrictions
																				if (!isAdd) {
																					// Edit mode: only single selection
																					field.onChange([module.value]);
																				} else {
																					// Add mode: up to 5 selections
																					if (currentValues.length < 5) {
																						field.onChange([
																							...currentValues,
																							module.value,
																						]);
																					}
																				}
																			}
																		}}
																		className="cursor-pointer"
																	>
																		<Check
																			className={cn(
																				'mr-2 h-4 w-4',
																				field.value?.includes(module.value)
																					? 'opacity-100'
																					: 'opacity-0'
																			)}
																		/>
																		{module.label}
																	</CommandItem>
																))}
															</ScrollArea>
														</CommandGroup>
													</Command>
												</PopoverContent>
											</Popover>
											{field.value?.length > 0 && (
												<div className="flex flex-wrap gap-1 mt-2">
													{field.value.map((moduleValue) => {
														const module = availableModules.find(
															(m) => m.value === moduleValue
														);
														return (
															<Badge
																key={moduleValue}
																variant="secondary"
																className="flex items-center gap-1"
															>
																{module?.label || moduleValue}
																<X
																	className="h-3 w-3 cursor-pointer hover:text-destructive"
																	onClick={() => {
																		field.onChange(
																			field.value?.filter(
																				(val) => val !== moduleValue
																			) || []
																		);
																	}}
																/>
															</Badge>
														);
													})}
												</div>
											)}
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						))}

						{/* Add More Button - Only for Add Mode */}
						{isAdd && fields.length < 5 && (
							<Button
								type="button"
								variant="outline"
								onClick={addModuleAdmin}
								className="w-full"
							>
								<Plus className="mr-2 h-4 w-4" />
								Add Another Module Admin (Max 5)
							</Button>
						)}
					</form>
				</Form>
				<DialogFooter>
					<Button
						variant="outline"
						onClick={() => setShowAddEditDialog(false)}
						disabled={isLoading}
					>
						Cancel
					</Button>
					<Button type="submit" form="module-admin-form" disabled={isLoading}>
						{isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
						{isAdd ? 'Add Module Admin' : 'Update Module Admin'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default ModuleAdminAddEditDialog;
