'use client';

import { useState, useMemo } from 'react';
import {
	getCoreRowModel,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	useReactTable,
} from '@tanstack/react-table';
import { Table } from '@/components/ui/table';
import { THeader } from '@/components/table-header';
import { TBody } from '@/components/table-body';
import { TablePagination } from '@/components/table-pagination';
import { createColumns } from './columns';

const EditRequestsTable = ({
	data,
	filters,
	isLoading = false,
	onCompareData,
	onApproveRequest,
	onRejectRequest,
}) => {
	const [sorting, setSorting] = useState([]);
	const [columnFilters, setColumnFilters] = useState([]);
	const [columnVisibility, setColumnVisibility] = useState({});
	const [rowSelection, setRowSelection] = useState({});

	// Filter data based on filters
	const filteredData = useMemo(() => {
		return data.filter((request) => {
			// Status filter
			if (filters.status !== 'all' && request.status !== filters.status) {
				return false;
			}

			// Section filter
			if (filters.section !== 'all' && request.section !== filters.section) {
				return false;
			}

			// Employee filter
			if (
				filters.employee !== 'all' &&
				request.userId.employeeOrgId !== filters.employee
			) {
				return false;
			}

			// Date range filter
			if (filters.dateRange?.from) {
				const requestDate = new Date(request.submittedAt);
				const fromDate = new Date(filters.dateRange.from);
				const toDate = filters.dateRange.to
					? new Date(filters.dateRange.to)
					: new Date();

				if (requestDate < fromDate || requestDate > toDate) {
					return false;
				}
			}

			return true;
		});
	}, [data, filters]);

	const columns = useMemo(
		() => createColumns({ onCompareData, onApproveRequest, onRejectRequest }),
		[onCompareData, onApproveRequest, onRejectRequest]
	);

	const table = useReactTable({
		data: filteredData,
		columns,
		onSortingChange: setSorting,
		onColumnFiltersChange: setColumnFilters,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		onColumnVisibilityChange: setColumnVisibility,
		onRowSelectionChange: setRowSelection,
		state: {
			sorting,
			columnFilters,
			columnVisibility,
			rowSelection,
		},
		initialState: {
			pagination: {
				pageSize: 10,
			},
		},
	});

	return (
		<div className="space-y-4">
			<div className="rounded-md border">
				<Table>
					<THeader table={table} />
					<TBody table={table} isLoading={isLoading} />
				</Table>
			</div>
			<TablePagination table={table} />
		</div>
	);
};

export default EditRequestsTable;
