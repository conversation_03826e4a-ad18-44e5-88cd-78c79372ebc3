'use client';
import { useEffect, useState } from 'react';
import {
	fetchCompanies,
	changeCompany,
	appointAdmin,
	removeAdmin,
	resetState,
} from '@/lib/features/glorified-client-admin/glorifiedClientAdminSlice';
import {
	appointAdminSchema,
	removeAdminSchema,
	changeCompanySchema,
} from '@/lib/schemas/gClientAdminSchema';
import { toast } from 'sonner';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Loader2 } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';

export default function GClientAdmin() {
	const dispatch = useAppDispatch();
	const { companies, currentCompany, isLoading, error, success } =
		useAppSelector((store) => store.glorifiedClientAdmin);
	const [selectedCompany, setSelectedCompany] = useState('');
	const [appointForm, setAppointForm] = useState({
		employeeId: '',
		companyId: '',
	});
	const [removeForm, setRemoveForm] = useState({
		currentAdminId: '',
		newAdminId: '',
		confirmation: '',
	});
	const [showRemoveDialog, setShowRemoveDialog] = useState(false);

	useEffect(() => {
		dispatch(fetchCompanies());
	}, [dispatch]);

	useEffect(() => {
		if (error) {
			toast.error(error.message || 'An error occurred');
			dispatch(resetState());
		}
		if (success) {
			toast.success('Operation completed successfully');
			dispatch(resetState());
		}
	}, [error, success, dispatch]);

	const handleCompanyChange = async (countryId) => {
		try {
			changeCompanySchema.parse({ countryId });
			await dispatch(changeCompany(countryId)).unwrap();
			setSelectedCompany(countryId);
		} catch (error) {
			toast.error(error.message || 'Invalid country ID');
		}
	};

	const handleAppointAdmin = async (e) => {
		e.preventDefault();
		try {
			appointAdminSchema.parse(appointForm);
			await dispatch(appointAdmin(appointForm)).unwrap();
			setAppointForm({ employeeId: '', companyId: '' });
		} catch (error) {
			toast.error(error.message || 'Invalid form data');
		}
	};

	const handleRemoveAdmin = async (e) => {
		e.preventDefault();
		try {
			removeAdminSchema.parse(removeForm);
			await dispatch(
				removeAdmin({
					currentAdminId: removeForm.currentAdminId,
					newAdminId: removeForm.newAdminId,
				})
			).unwrap();
			setRemoveForm({ currentAdminId: '', newAdminId: '', confirmation: '' });
			setShowRemoveDialog(false);
		} catch (error) {
			toast.error(error.message || 'Invalid form data');
		}
	};

	return (
		<div className="container mx-auto p-4 space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>Company Management</CardTitle>
					<CardDescription>Select and manage companies</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div className="space-y-2">
							<Label>Select Company</Label>
							<Select
								value={selectedCompany}
								onValueChange={handleCompanyChange}
								disabled={isLoading}
							>
								<SelectTrigger>
									<SelectValue placeholder="Select a company" />
								</SelectTrigger>
								<SelectContent>
									{companies.map((company) => (
										<SelectItem key={company.id} value={company.id}>
											{company.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Appoint New Admin</CardTitle>
					<CardDescription>Assign a new client admin</CardDescription>
				</CardHeader>
				<CardContent>
					<form onSubmit={handleAppointAdmin} className="space-y-4">
						<div className="space-y-2">
							<Label>Employee ID</Label>
							<Input
								value={appointForm.employeeId}
								onChange={(e) =>
									setAppointForm({ ...appointForm, employeeId: e.target.value })
								}
								placeholder="Enter employee ID"
								disabled={isLoading}
							/>
						</div>
						<div className="space-y-2">
							<Label>Company ID</Label>
							<Input
								value={appointForm.companyId}
								onChange={(e) =>
									setAppointForm({ ...appointForm, companyId: e.target.value })
								}
								placeholder="Enter company ID"
								disabled={isLoading}
							/>
						</div>
						<Button type="submit" disabled={isLoading}>
							{isLoading ? (
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							) : null}
							Appoint Admin
						</Button>
					</form>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Remove Admin</CardTitle>
					<CardDescription>Remove an existing client admin</CardDescription>
				</CardHeader>
				<CardContent>
					<Dialog open={showRemoveDialog} onOpenChange={setShowRemoveDialog}>
						<DialogTrigger asChild>
							<Button variant="destructive">Remove Admin</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Remove Admin</DialogTitle>
								<DialogDescription>
									This action cannot be undone. Please confirm by typing
									&quot;CONFIRM&quot;.
								</DialogDescription>
							</DialogHeader>
							<form onSubmit={handleRemoveAdmin} className="space-y-4">
								<div className="space-y-2">
									<Label>Current Admin ID</Label>
									<Input
										value={removeForm.currentAdminId}
										onChange={(e) =>
											setRemoveForm({
												...removeForm,
												currentAdminId: e.target.value,
											})
										}
										placeholder="Enter current admin ID"
										disabled={isLoading}
									/>
								</div>
								<div className="space-y-2">
									<Label>New Admin ID (Optional)</Label>
									<Input
										value={removeForm.newAdminId}
										onChange={(e) =>
											setRemoveForm({
												...removeForm,
												newAdminId: e.target.value,
											})
										}
										placeholder="Enter new admin ID"
										disabled={isLoading}
									/>
								</div>
								<div className="space-y-2">
									<Label>Confirmation</Label>
									<Input
										value={removeForm.confirmation}
										onChange={(e) =>
											setRemoveForm({
												...removeForm,
												confirmation: e.target.value,
											})
										}
										placeholder="Type 'CONFIRM' to proceed"
										disabled={isLoading}
									/>
								</div>
								<DialogFooter>
									<Button
										type="button"
										variant="outline"
										onClick={() => setShowRemoveDialog(false)}
									>
										Cancel
									</Button>
									<Button
										type="submit"
										variant="destructive"
										disabled={isLoading}
									>
										{isLoading ? (
											<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										) : null}
										Remove Admin
									</Button>
								</DialogFooter>
							</form>
						</DialogContent>
					</Dialog>
				</CardContent>
			</Card>
		</div>
	);
}
