'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
	<PERSON>alog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { XCircle } from 'lucide-react';
import React, { useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { onboardEmployeeUsingLink } from '@/lib/features/employees/employeeSlice';

const addEmployeesSchema = z.object({
	employees: z
		.array(
			z.object({
				name: z.string().min(1, 'Name is required'),
				email: z.string().email('Invalid email'),
			})
		)
		.min(1, 'At least one employee is required')
		.max(10, 'You can only add up to 10 employees at once'),
});

const AddEmployeesDialog = ({ open, setOpen }) => {
	const dispatch = useAppDispatch();

	const form = useForm({
		resolver: zodResolver(addEmployeesSchema),
		defaultValues: {
			employees: [{ name: '', email: '' }],
		},
		mode: 'onSubmit',
		reValidateMode: 'onSubmit',
	});

	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: 'employees',
	});

	const { authenticatedUser } = useAppSelector((state) => state.auth);

	const onSubmit = async ({ employees }) => {
		const payload = {
			employeeDetails: employees.map((emp) => ({
				name: emp.name,
				email: emp.email,
				companyId: authenticatedUser?.companyId,
				clientAdminId: authenticatedUser?.clientAdminId,
			})),
		};

		const result = await dispatch(onboardEmployeeUsingLink(payload));

		if (onboardEmployeeUsingLink.fulfilled.match(result)) {
			setOpen(false);
			form.reset();
		}
	};

	useEffect(() => {
		if (open) {
			form.reset({ employees: [{ name: '', email: '' }] });
		}
	}, [open]);

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogContent className="max-h-[80vh] overflow-hidden">
				<DialogHeader>
					<DialogTitle>Onboard New Employees</DialogTitle>
					<DialogDescription>
						Enter names and emails to send onboarding links.
					</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form
						id="add-employees-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="grid gap-4 max-h-[50vh] overflow-y-auto pt-2 pb-2 pr-2"
					>
						{fields.map((employee, index) => (
							<div
								key={employee.id}
								className="relative grid grid-cols-2 gap-4 border p-4 rounded-lg"
							>
								{fields.length > 1 && (
									<button
										type="button"
										onClick={() => remove(index)}
										className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200"
									>
										<XCircle className="text-red-500" size={16} />
									</button>
								)}
								<FormField
									control={form.control}
									name={`employees.${index}.name`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>Name</FormLabel>
											<FormControl>
												<Input
													{...field}
													type="text"
													placeholder="Enter name"
													autocomplete="name"
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name={`employees.${index}.email`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>Email</FormLabel>
											<FormControl>
												<Input
													{...field}
													type="email"
													placeholder="Enter email"
													autocomplete="email"
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						))}
						{fields.length >= 10 && (
							<p className="text-sm text-muted-foreground">
								Maximum 10 employees can be added at a time.
							</p>
						)}
						<Button
							type="button"
							variant="outline"
							onClick={() => append({ name: '', email: '' })}
							className="mt-2"
							disabled={fields.length >= 10}
						>
							Add Another Employee
						</Button>
					</form>
				</Form>
				<DialogFooter>
					<Button variant="outline" onClick={() => setOpen(false)}>
						Cancel
					</Button>
					<Button type="submit" form="add-employees-form">
						Submit
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default AddEmployeesDialog;
