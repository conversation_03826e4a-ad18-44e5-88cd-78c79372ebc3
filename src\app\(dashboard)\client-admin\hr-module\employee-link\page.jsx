'use client';

import React, { useState } from 'react';
import { Separator } from '@/components/ui/separator';
import { But<PERSON> } from '@/components/ui/button';
import AddEmployeesDialog from './add-employee-dialog'; // Adjust path as needed

const Page = () => {
	const [showDialog, setShowDialog] = useState(false);

	return (
		<div className="container mx-auto py-10 px-2">
			<h1 className="text-2xl font-bold text-gray-700 dark:text-gray-100">
				Employee Link
			</h1>
			<Separator className="my-2" />

			<Button onClick={() => setShowDialog(true)}>Link Employee</Button>

			<AddEmployeesDialog open={showDialog} setOpen={setShowDialog} />
		</div>
	);
};

export default Page;
