'use client';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import {
	ArrowUpDown,
	Calendar,
	CheckCircle,
	PencilLine,
	Building,
	UserCheck,
	Network,
	Link2,
} from 'lucide-react';
import { DataTableRowActions } from './row-actions';
import { DataTableCellContent } from './details-popover';
import { formatDate, userRoles } from '@/lib/utils';
import { Badge, badgeVariants } from '@/components/ui/badge';
import Link from 'next/link';
import { IconHierarchy } from '@tabler/icons-react';
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from '@/components/ui/tooltip';
import { resendOnboardingLink } from '@/lib/features/employees/employeeSlice';

export const createColumns = (dispatch) => {
	return [
		{
			id: 'select',
			header: ({ table }) => (
				<Checkbox
					checked={table.getIsAllPageRowsSelected()}
					onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Select all"
				/>
			),
			cell: ({ row }) => (
				<Checkbox
					checked={row.getIsSelected()}
					onCheckedChange={(value) => row.toggleSelected(!!value)}
					aria-label="Select row"
				/>
			),
			enableSorting: false,
			enableHiding: false,
		},
		{
			accessorKey: 'employeeOrgId',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Employee ID
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="font-medium">{row.original.employeeOrgId || 'N/A'}</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'isRegistrationComplete',
			header: 'Status',
			cell: ({ row }) => {
				const {
					isRegistrationComplete,
					isContactDetailsComplete,
					isFamilyDetailsComplete,
					isPersonalDetailsComplete,
					isQualificationDetailsComplete,
					isOnboardedUsingLink,
				} = row?.original?.userFlags;

				if (isRegistrationComplete) {
					return (
						<div className="flex items-center w-full">
							<Badge>
								<div className="flex items-center justify-center gap-2 w-full">
									<CheckCircle className="size-3" /> Onboarded
								</div>
							</Badge>
						</div>
					);
				} else if (
					isOnboardedUsingLink &&
					(!isContactDetailsComplete ||
						!isFamilyDetailsComplete ||
						!isPersonalDetailsComplete ||
						!isQualificationDetailsComplete)
				) {
					return (
						<div className="flex items-center w-full">
							<Tooltip>
								<Badge variant="secondary">
									<TooltipTrigger asChild>
										<div className="flex items-center justify-center gap-2 w-full">
											<Link2 className="size-3" /> Pending
										</div>
									</TooltipTrigger>
								</Badge>
								<TooltipContent>
									<div
										className="flex items-center justify-center gap-2 w-full cursor-pointer"
										onClick={() => {
											dispatch(
												resendOnboardingLink({ userId: row.original._id })
											);
										}}
									>
										<Link2 className="size-3" /> Resend Link
									</div>
								</TooltipContent>
							</Tooltip>
						</div>
					);
				} else {
					return (
						<div className="flex items-center w-full">
							<Link
								href={`/client-admin/hr-module/employee-onboarding?employeeId=${row.original._id}`}
								className={badgeVariants({ variant: 'outline' })}
							>
								<div className="flex items-center justify-center gap-2 w-full">
									<PencilLine className="size-3" /> Draft
								</div>
							</Link>
						</div>
					);
				}
			},
		},
		{
			accessorKey: 'name',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Name
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const fullName =
					row.original.personalDetails?.fullName || row.original.name || 'N/A';
				return (
					<DataTableCellContent
						type="employee"
						value={fullName}
						details={{
							id: row.original._id,
							email: row.original.email,
							phone: row.original.personalDetails?.mobile,
							profilePhoto: row.original.profilePhoto,
							designation: row.original.employmentDetails?.designation,
							department: row.original.employmentDetails?.department,
						}}
					/>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'businessUnit',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Business Unit
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Network className="size-4 text-muted-foreground" />
					<span>
						{row.original.businessUnit
							? `${row.original.businessUnit} - (${row.original.businessUnitLocation})`
							: 'N/A'}
					</span>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'department',
			header: 'Department',
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Building className="size-4 text-muted-foreground" />
					<span>{row.original.department || 'N/A'}</span>
				</div>
			),
		},
		{
			accessorKey: 'designation',
			header: 'Designation',
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<UserCheck className="size-4 text-muted-foreground" />
					<span>{row.original.designation || 'N/A'}</span>
				</div>
			),
		},
		{
			accessorKey: 'reportingTo',
			header: 'Reporting To',
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<IconHierarchy className="size-4 text-muted-foreground" />
					<span>{row.original.reportingTo || 'N/A'}</span>
				</div>
			),
		},
		{
			accessorKey: 'dateOfJoining',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Joining Date
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const date = row.original.dateOfJoining;
				return (
					<div className="flex items-center gap-2">
						<Calendar className="size-4 text-muted-foreground" />
						<span>{date ? formatDate(date) : 'N/A'}</span>
					</div>
				);
			},
			enableSorting: true,
		},
		{
			id: 'actions',
			cell: ({ row }) => {
				const employee = row.original;
				const isADmin =
					employee?.role === userRoles.CLIENT_ADMIN ||
					employee?.role === userRoles.GLORIFIED_CLIENT_ADMIN;
				return (
					employee?.userFlags?.isRegistrationComplete &&
					!isADmin && <DataTableRowActions row={row} dispatch={dispatch} />
				);
			},
		},
	];
};
