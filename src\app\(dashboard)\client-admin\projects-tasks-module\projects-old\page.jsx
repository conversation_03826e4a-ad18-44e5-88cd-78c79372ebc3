'use client';
import { useState } from 'react';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import ProjectsTable from './projects-table';
import { AddProjectDialog } from './add-project-dialog';

export default function ProjectsPage() {
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	return (
		<div className="container mx-auto py-10 px-2">
			<section className="flex items-center justify-between">
				<h1 className="text-2xl font-bold text-gray-700 dark:text-gray-100">
					Projects
				</h1>
				<Button
					onClick={() => setIsDialogOpen(true)}
					className="flex items-center gap-2"
				>
					<Plus className="h-4 w-4" />
					Add Project
				</Button>
			</section>
			<Separator className="my-2" />
			<ProjectsTable />

			<AddProjectDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} />
		</div>
	);
}
