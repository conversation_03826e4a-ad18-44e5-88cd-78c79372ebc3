'use client';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import {
	ArrowUpDown,
	Clock,
	MessageSquare,
	Edit,
	Move,
	Hash,
	Trash,
	Paperclip,
	Plus,
} from 'lucide-react';
import { DataTableRowActions } from './row-actions';
import { DataTableCellContent } from './details-popover';
import { Badge } from '@/components/ui/badge';
import { formatDate } from '@/lib/utils';

// Action type mapping
const actionTypes = {
	7.1: { name: 'Position Updated', icon: Move, color: 'bg-blue-500' },
	7.2: { name: 'Task Updated', icon: Edit, color: 'bg-green-500' },
	7.11: { name: 'Comment Added', icon: MessageSquare, color: 'bg-purple-500' },
	7.3: { name: 'Task Status Updated', icon: Edit, color: 'bg-yellow-500' },
	7.4: { name: 'Task Deleted', icon: Trash, color: 'bg-red-500' },
	7.5: { name: 'Task Media Removed', icon: Paperclip, color: 'bg-gray-500' },
	7.6: { name: 'Task Board Retrieved', icon: Hash, color: 'bg-teal-500' },
	7.7: { name: 'Task Group Created', icon: Plus, color: 'bg-indigo-500' },
	7.8: { name: 'Task Group Updated', icon: Edit, color: 'bg-orange-500' },
	7.9: { name: 'Task Group Deleted', icon: Trash, color: 'bg-pink-500' },
	7.12: { name: 'Task Description Updated', icon: Edit, color: 'bg-cyan-500' },
};

export const createColumns = (dispatch) => {
	return [
		{
			id: 'select',
			header: ({ table }) => (
				<Checkbox
					checked={table?.getIsAllPageRowsSelected?.() || false}
					onCheckedChange={(value) =>
						table?.toggleAllPageRowsSelected?.(!!value)
					}
					aria-label="Select all"
				/>
			),
			cell: ({ row }) => (
				<Checkbox
					checked={row?.getIsSelected?.() || false}
					onCheckedChange={(value) => row?.toggleSelected?.(!!value)}
					aria-label="Select row"
				/>
			),
			enableSorting: false,
			enableHiding: false,
		},
		{
			accessorKey: 'timestamp',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Timestamp
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const timestamp = row.original.timestamp;
				return (
					<div className="flex items-center gap-2">
						<Clock className="h-4 w-4 text-muted-foreground" />
						<div className="flex flex-col">
							<span className="text-sm font-medium">
								{formatDate(timestamp)}
							</span>
							<span className="text-xs text-muted-foreground">
								{new Date(timestamp).toLocaleTimeString()}
							</span>
						</div>
					</div>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'action',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Action
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const action = row.original.action;
				const actionInfo = actionTypes[action] || {
					name: `Action ${action}`,
					icon: Hash,
					color: 'bg-gray-500',
				};
				const IconComponent = actionInfo.icon;

				return (
					<div className="flex items-center gap-2">
						<div className={`p-1 rounded-full ${actionInfo.color}`}>
							<IconComponent className="h-3 w-3 text-white" />
						</div>
						<Badge variant="outline" className="text-xs">
							{actionInfo.name}
						</Badge>
					</div>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'message',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Message
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="font-medium">{row.original.message}</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'username',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					User
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const log = row.original;
				return (
					<DataTableCellContent
						type="user"
						value={log.username}
						details={{
							userId: log.userId,
							username: log.username,
							timestamp: log.timestamp,
							action: log.action,
							message: log.message,
							profilePhoto: log.profilePhoto,
							taskId: log.taskId,
							taskTitle: log.taskTitle,
							projectId: log.projectId,
						}}
					/>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'taskTitle',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Task
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const log = row.original;
				return (
					<DataTableCellContent
						type="task"
						value={log.taskTitle}
						details={{
							taskId: log.taskId,
							taskTitle: log.taskTitle,
							userId: log.userId,
							username: log.username,
							timestamp: log.timestamp,
							action: log.action,
							message: log.message,
							profilePhoto: log.profilePhoto,
							projectId: log.projectId,
						}}
					/>
				);
			},
			enableSorting: true,
		},
		// {
		// 	id: 'actions',
		// 	cell: ({ row }) => <DataTableRowActions row={row} dispatch={dispatch} />,
		// },
	];
};
