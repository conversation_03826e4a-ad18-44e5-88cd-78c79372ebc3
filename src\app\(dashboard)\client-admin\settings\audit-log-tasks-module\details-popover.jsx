'use client';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
	User,
	Clock,
	Hash,
	ExternalLink,
	MessageSquare,
	Edit,
	Move,
	Trash,
	Paperclip,
	Plus,
} from 'lucide-react';
import { formatDate, getInitials } from '@/lib/utils';
import { useRouter } from 'next/navigation';

// Action type mapping
const actionTypes = {
	7.1: { name: 'Position Updated', icon: Move, color: 'bg-blue-500' },
	7.2: { name: 'Task Updated', icon: Edit, color: 'bg-green-500' },
	7.11: { name: 'Comment Added', icon: MessageSquare, color: 'bg-purple-500' },
	7.3: { name: 'Task Status Updated', icon: Edit, color: 'bg-yellow-500' },
	7.4: { name: 'Task Deleted', icon: Trash, color: 'bg-red-500' },
	7.5: { name: 'Task Media Removed', icon: Paperclip, color: 'bg-gray-500' },
	7.6: { name: 'Task Board Retrieved', icon: Hash, color: 'bg-teal-500' },
	7.7: { name: 'Task Group Created', icon: Plus, color: 'bg-indigo-500' },
	7.8: { name: 'Task Group Updated', icon: Edit, color: 'bg-orange-500' },
	7.9: { name: 'Task Group Deleted', icon: Trash, color: 'bg-pink-500' },
	7.12: { name: 'Task Description Updated', icon: Edit, color: 'bg-cyan-500' },
};

export function DataTableCellContent({ type, value, details }) {
	if (type === 'user') {
		return (
			<Popover>
				<PopoverTrigger asChild>
					<Button
						variant="link"
						className="p-0 h-auto font-medium text-left flex items-center gap-2"
					>
						<Avatar className="h-6 w-6">
							<AvatarImage
								src={details.profilePhoto || '/placeholder.svg'}
								alt={value}
							/>
							<AvatarFallback>
								{value
									.split(' ')
									.map((n) => n[0])
									.join('')
									.toUpperCase()
									.substring(0, 2)}
							</AvatarFallback>
						</Avatar>
						{value}
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-80 p-0">
					<UserDetailsCard details={details} value={value} />
				</PopoverContent>
			</Popover>
		);
	}

	if (type === 'task') {
		return (
			<Popover>
				<PopoverTrigger asChild>
					<Button variant="link" className="p-0 h-auto font-medium text-left">
						{value}
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-80 p-0">
					<TaskDetailsCard details={details} value={value} />
				</PopoverContent>
			</Popover>
		);
	}

	return <div>{value}</div>;
}

function UserDetailsCard({ details, value }) {
	const router = useRouter();
	const handleViewProfile = () => {
		router.push(`/client-admin/hr-module/employees-list/${details.userId}`);
	};

	const actionInfo = actionTypes[details.action] || {
		name: `Action ${details.action}`,
		icon: Hash,
		color: 'bg-gray-500',
	};
	const IconComponent = actionInfo.icon;

	return (
		<Card className="border-0">
			<CardHeader className="pb-3">
				<div className="flex items-center gap-3">
					<Avatar className="h-12 w-12">
						<AvatarImage
							src={details.profilePhoto || '/placeholder.svg'}
							alt={value}
						/>
						<AvatarFallback>{getInitials(value)}</AvatarFallback>
					</Avatar>
					<div>
						<CardTitle className="text-lg">{value}</CardTitle>
						<CardDescription className="flex items-center gap-2 mt-1">
							<User className="h-3 w-3" />
							User ID: {details.userId.slice(-8)}
						</CardDescription>
					</div>
				</div>
			</CardHeader>
			<CardContent className="pb-3">
				<div className="space-y-3">
					{/* Recent Action */}
					<div className="space-y-2">
						<div className="text-sm font-medium">Recent Action</div>
						<div className="flex items-center gap-2">
							<div className={`p-1 rounded-full ${actionInfo.color}`}>
								<IconComponent className="h-3 w-3 text-white" />
							</div>
							<Badge variant="outline" className="text-xs">
								{actionInfo.name}
							</Badge>
						</div>
						<div className="text-sm text-muted-foreground">
							{details.message}
						</div>
					</div>

					<Separator />

					{/* Timestamp */}
					<div className="flex items-center gap-2">
						<Clock className="h-4 w-4 text-muted-foreground" />
						<div className="flex flex-col">
							<span className="text-sm">{formatDate(details.timestamp)}</span>
							<span className="text-xs text-muted-foreground">
								{new Date(details.timestamp).toLocaleTimeString()}
							</span>
						</div>
					</div>

					<Button
						variant="default"
						className="w-full"
						onClick={handleViewProfile}
					>
						<User className="h-4 w-4 mr-2" />
						View User Profile
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}

function TaskDetailsCard({ details, value }) {
	const router = useRouter();
	const handleViewTask = () => {
		router.push(
			`/client-admin/projects-tasks-module/projects/${details.projectId}`
		);
	};

	const actionInfo = actionTypes[details.action] || {
		name: `Action ${details.action}`,
		icon: Hash,
		color: 'bg-gray-500',
	};
	const IconComponent = actionInfo.icon;

	return (
		<Card className="border-0">
			<CardHeader className="pb-3">
				<div>
					<CardTitle className="text-lg">{value}</CardTitle>
					<CardDescription className="flex items-center gap-2 mt-1">
						<Hash className="h-3 w-3" />
						Task ID: {details.taskId.slice(-8)}
					</CardDescription>
				</div>
			</CardHeader>
			<CardContent className="pb-3">
				<div className="space-y-3">
					{/* Action Details */}
					<div className="space-y-2">
						<div className="text-sm font-medium">Last Action</div>
						<div className="flex items-center gap-2">
							<div className={`p-1 rounded-full ${actionInfo.color}`}>
								<IconComponent className="h-3 w-3 text-white" />
							</div>
							<Badge variant="outline" className="text-xs">
								{actionInfo.name}
							</Badge>
						</div>
						<div className="text-sm text-muted-foreground">
							{details.message}
						</div>
					</div>

					<Separator />

					{/* User Info */}
					<div className="flex items-center gap-2">
						<Avatar className="h-6 w-6">
							<AvatarImage
								src={details.profilePhoto || '/placeholder.svg'}
								alt={details.username}
							/>
							<AvatarFallback>
								{details.username
									.split(' ')
									.map((n) => n[0])
									.join('')
									.toUpperCase()
									.substring(0, 2)}
							</AvatarFallback>
						</Avatar>
						<div className="flex flex-col">
							<span className="text-sm font-medium">{details.username}</span>
							<span className="text-xs text-muted-foreground">
								Performed action
							</span>
						</div>
					</div>

					{/* Timestamp */}
					<div className="flex items-center gap-2">
						<Clock className="h-4 w-4 text-muted-foreground" />
						<div className="flex flex-col">
							<span className="text-sm">{formatDate(details.timestamp)}</span>
							<span className="text-xs text-muted-foreground">
								{new Date(details.timestamp).toLocaleTimeString()}
							</span>
						</div>
					</div>

					<Button variant="default" className="w-full" onClick={handleViewTask}>
						<ExternalLink className="h-4 w-4 mr-2" />
						View Task Details
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}
