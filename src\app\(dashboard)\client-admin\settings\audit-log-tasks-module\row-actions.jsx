'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { MoreHorizontal, Eye, ExternalLink, Trash, User } from 'lucide-react';
import { toast } from 'sonner';

export function DataTableRowActions({ row, dispatch }) {
	const taskLog = row.original;
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);

	const handleViewDetails = () => {
		dispatch({
			type: 'taskLog/viewDetails',
			payload: taskLog._id,
		});
		// Navigate to log details page
		window.location.href = `/task-logs/${taskLog._id}`;
	};

	const handleViewTask = () => {
		dispatch({
			type: 'task/viewTask',
			payload: taskLog.taskId,
		});
		// Navigate to task details page
		window.location.href = `/tasks/${taskLog.taskId}`;
	};

	const handleViewUser = () => {
		dispatch({
			type: 'user/viewUser',
			payload: taskLog.userId,
		});
		// Navigate to user profile page
		window.location.href = `/users/${taskLog.userId}`;
	};

	const handleDelete = () => {
		dispatch({
			type: 'taskLog/deleteLog',
			payload: taskLog._id,
		});

		toast({
			title: 'Log deleted',
			description: 'The task log has been deleted successfully.',
		});
		setShowDeleteDialog(false);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					<DropdownMenuItem onClick={handleViewDetails}>
						<Eye className="mr-2 h-4 w-4" />
						View Log Details
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					<DropdownMenuItem onClick={handleViewTask}>
						<ExternalLink className="mr-2 h-4 w-4" />
						View Task
					</DropdownMenuItem>
					<DropdownMenuItem onClick={handleViewUser}>
						<User className="mr-2 h-4 w-4" />
						View User Profile
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					<DropdownMenuItem
						onClick={() => setShowDeleteDialog(true)}
						className="text-destructive focus:text-destructive"
					>
						<Trash className="mr-2 h-4 w-4" />
						Delete Log
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Delete Confirmation Dialog */}
			<AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete Task Log?</AlertDialogTitle>
						<AlertDialogDescription>
							This will permanently delete this task log entry. This action
							cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction variant="destructive" onClick={handleDelete}>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
