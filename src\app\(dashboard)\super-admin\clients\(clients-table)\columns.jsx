'use client';
import { Checkbox } from '@/components/ui/checkbox';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowUpDown, Mail, Building, Shield } from 'lucide-react';
import { DataTableRowActions } from './row-actions';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

// Role mapping for display purposes
const userRoleNames = {
	1: 'Super Admin',
	2: 'Country Admin',
	3: 'Glorified Client Admin',
	4: 'Client Admin',
	5: 'Business Admin',
	6: 'Department Admin',
	7.1: 'HR Module Admin',
	7.2: 'Leave Module Admin',
	7.3: 'Attendance Module Admin',
	7.4: 'Expense Module Admin',
	7.5: 'Payroll Module Admin',
	7.6: 'Project Module Admin',
	7.7: 'Communication Module Admin',
	7.8: 'Performance Module Admin',
	7.9: 'System Settings Module Admin',
	8: 'Reporting Manager',
	9: 'Approver',
	10: 'Employee',
	11: 'Partner Admin',
};

// Badge variants based on role
const getRoleBadgeVariant = (role) => {
	switch (role) {
		case 1:
			return 'destructive'; // Super Admin - red
		case 2:
			return 'destructive'; // Country Admin - red
		case 3:
			return 'default'; // Glorified Client Admin - primary color
		case 4:
			return 'secondary'; // Client Admin - gray
		case 5:
		case 6:
			return 'outline'; // Business/Department Admin - outline
		default:
			if (role >= 7 && role < 8) return 'warning'; // Module Admins - yellow/orange
			return 'outline'; // Others - outline
	}
};

export const createColumns = (dispatch) => {
	return [
		{
			id: 'select',
			header: ({ table }) => (
				<Checkbox
					checked={table.getIsAllPageRowsSelected()}
					onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Select all"
				/>
			),
			cell: ({ row }) => (
				<Checkbox
					checked={row.getIsSelected()}
					onCheckedChange={(value) => row.toggleSelected(!!value)}
					aria-label="Select row"
				/>
			),
			enableSorting: false,
			enableHiding: false,
		},
		{
			accessorKey: 'name',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Name
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-3">
					<Avatar className="h-8 w-8">
						<AvatarImage
							src={row.original.logo}
							alt={row.original.companyName}
						/>
						<AvatarFallback>
							{row.original.companyName
								.split(' ')
								.map((n) => n[0])
								.join('')
								.toUpperCase()
								.substring(0, 2)}
						</AvatarFallback>
					</Avatar>
					<span className="font-medium">{row.original.name}</span>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'email',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Email
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Mail className="size-4 text-muted-foreground" />
					<span>{row.original.email}</span>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'role',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Role
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const role = row.original.role;
				return (
					<div className="flex items-center gap-2">
						<Shield className="size-4 text-muted-foreground" />
						<Badge variant={getRoleBadgeVariant(role)}>
							{userRoleNames[role] || `Role ${role}`}
						</Badge>
					</div>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'companyName',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Company
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Building className="size-4 text-muted-foreground" />
					<span>{row.original.companyName}</span>
				</div>
			),
			enableSorting: true,
		},
		{
			id: 'actions',
			cell: ({ row }) => <DataTableRowActions row={row} dispatch={dispatch} />,
		},
	];
};
