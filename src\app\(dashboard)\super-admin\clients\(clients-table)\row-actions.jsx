'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
	MoreHorizontal,
	Edit,
	Trash,
	Eye,
	ShieldAlert,
	ShieldCheck,
	UserCog,
} from 'lucide-react';
import { toast } from 'sonner';
import { promoteClientToGlorifiedAdmin } from '@/lib/features/super-admin/superAdminSlice';

export function DataTableRowActions({ row, dispatch }) {
	const client = row.original;
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [showPromoteDialog, setShowPromoteDialog] = useState(false);

	const isClientAdmin = client.role === 4;
	const isGlorifiedClientAdmin = client.role === 3;

	const handleView = () => {
		dispatch({
			type: 'client/viewClient',
			payload: client._id,
		});
		// Navigate to view page
		window.location.href = `/clients/${client._id}`;
	};

	const handleEdit = () => {
		dispatch({
			type: 'client/editClient',
			payload: client._id,
		});
		// Navigate to edit page
		window.location.href = `/clients/edit/${client._id}`;
	};

	const handleDelete = () => {
		dispatch({
			type: 'client/deleteClient',
			payload: client._id,
		});

		toast({
			title: 'Client deleted',
			description: `${client.name} has been deleted.`,
		});
		setShowDeleteDialog(false);
	};

	const handlePromote = () => {
		dispatch(promoteClientToGlorifiedAdmin({ clientAdminId: client._id }));
		setShowPromoteDialog(false);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					<DropdownMenuItem onClick={handleView}>
						<Eye className="mr-2 h-4 w-4" />
						View Details
					</DropdownMenuItem>
					{/* <DropdownMenuItem onClick={handleEdit}>
						<Edit className="mr-2 h-4 w-4" />
						Edit
					</DropdownMenuItem> */}
					<DropdownMenuSeparator />

					{isClientAdmin && (
						<DropdownMenuItem onClick={() => setShowPromoteDialog(true)}>
							<ShieldAlert className="mr-2 h-4 w-4 text-blue-500" />
							Promote to Glorified Client Admin
						</DropdownMenuItem>
					)}

					{/* {isGlorifiedClientAdmin && (
						<DropdownMenuItem onClick={() => {}}>
							<ShieldCheck className="mr-2 h-4 w-4 text-green-500" />
							Already Glorified Admin
						</DropdownMenuItem>
					)} */}

					{/* <DropdownMenuItem onClick={() => {}}>
						<UserCog className="mr-2 h-4 w-4" />
						Manage Permissions
					</DropdownMenuItem> */}

					{/* <DropdownMenuSeparator />
					<DropdownMenuItem
						onClick={() => setShowDeleteDialog(true)}
						className="text-destructive focus:text-destructive"
					>
						<Trash className="mr-2 h-4 w-4" />
						Delete
					</DropdownMenuItem> */}
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Delete Confirmation Dialog */}
			<AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>
							Are you sure you want to delete this client?
						</AlertDialogTitle>
						<AlertDialogDescription>
							This action cannot be undone. This will permanently delete the
							client account and remove their access.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction variant="destructive" onClick={handleDelete}>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>

			{/* Promote Confirmation Dialog */}
			<AlertDialog open={showPromoteDialog} onOpenChange={setShowPromoteDialog}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>
							Promote to Glorified Client Admin?
						</AlertDialogTitle>
						<AlertDialogDescription>
							This will promote {client.name} from Client Admin to Glorified
							Client Admin. They will gain additional permissions to manage
							multiple companies.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction onClick={handlePromote}>
							Promote
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
