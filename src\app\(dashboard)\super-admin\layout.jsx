'use client';

import { MainSidebar } from '@/components/main-sidebar';
import ProtectedLayout from '@/components/protected-layout';
import { userRoles } from '@/lib/utils';

export default function SuperAdminDashboardLayout({ children }) {
	return (
		<ProtectedLayout userType={[userRoles.SUPER_ADMIN]}>
			<MainSidebar role={[userRoles.SUPER_ADMIN]}>{children}</MainSidebar>
		</ProtectedLayout>
	);
}
