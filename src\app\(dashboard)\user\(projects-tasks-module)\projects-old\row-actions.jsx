'use client';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { MoreHorizontal, Eye, UserCheck } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from 'sonner';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { switchProjectLead } from '@/lib/features/projects/projectsSlice';
import { getInitials } from '@/lib/utils';
import { useRouter } from 'next/navigation';

export function DataTableRowActions({ row }) {
	const router = useRouter();
	const project = row.original;
	const [showSwitchLeadDialog, setShowSwitchLeadDialog] = useState(false);
	const [selectedLead, setSelectedLead] = useState('');
	const { employees, isLoading } = useAppSelector((store) => store.projects);
	const dispatch = useAppDispatch();

	const handleViewProject = () => {
		router.push(`/client-admin/projects-tasks-module/projects/${project._id}`);
	};

	const handleSwitchProjectLead = async () => {
		if (!selectedLead) {
			toast({
				title: 'Error',
				description: 'Please select a project lead',
				variant: 'destructive',
			});
			return;
		}
		await dispatch(
			switchProjectLead({
				projectId: row.original._id,
				leadId: selectedLead,
			})
		);

		setShowSwitchLeadDialog(false);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					<DropdownMenuItem onClick={handleViewProject}>
						<Eye className="mr-2 h-4 w-4" />
						View Project
					</DropdownMenuItem>
					<DropdownMenuItem onClick={() => setShowSwitchLeadDialog(true)}>
						<UserCheck className="mr-2 h-4 w-4" />
						Switch Project Lead
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Switch Project Lead Dialog */}
			<Dialog
				open={showSwitchLeadDialog}
				onOpenChange={setShowSwitchLeadDialog}
			>
				<DialogContent className="sm:max-w-[425px]">
					<DialogHeader>
						<DialogTitle>Switch Project Lead</DialogTitle>
						<DialogDescription>
							Select a new project lead for $quot;{project.name}$quot;. The
							current lead will be notified of this change.
						</DialogDescription>
					</DialogHeader>

					<div className="grid gap-4 py-4">
						<div className="space-y-4">
							<Label>New Project Lead</Label>
							<RadioGroup
								value={selectedLead}
								onValueChange={setSelectedLead}
								className="space-y-3"
							>
								{employees?.map((lead) => (
									<div
										key={lead.reportingUserId}
										className="flex items-center space-x-2 rounded-md border p-3"
									>
										<RadioGroupItem
											value={lead.reportingUserId}
											id={lead.reportingUserId}
											disabled={isLoading}
										/>
										<Label
											htmlFor={lead.reportingUserId}
											className="flex flex-1 items-center gap-3 cursor-pointer"
										>
											<Avatar className="h-8 w-8">
												<AvatarImage
													src={lead.profilePhoto || '/placeholder.svg'}
													alt={lead.reportingUserName}
												/>
												<AvatarFallback>
													{getInitials(lead.reportingUserName)}
												</AvatarFallback>
											</Avatar>
											<div className="flex flex-col">
												<span className="font-medium">
													{lead.reportingUserName}
												</span>
												<span className="text-sm text-muted-foreground">
													{lead.email}
												</span>
											</div>
										</Label>
									</div>
								))}
							</RadioGroup>
						</div>
					</div>

					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => {
								setShowSwitchLeadDialog(false);
								setSelectedLead('');
							}}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							onClick={handleSwitchProjectLead}
							disabled={!selectedLead || isLoading}
						>
							{isLoading ? 'Switching...' : 'Switch Lead'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
