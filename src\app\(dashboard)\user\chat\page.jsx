'use client';

import { AppSidebar } from '@/components/app-sidebar';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { ChatArea } from '@/components/chat-area';
import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { showMe } from '@/lib/features/auth/authSlice';
import { useRouter } from 'next/navigation';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export default function ChatPage() {
	const dispatch = useAppDispatch();
	const { selectedChatUser } = useAppSelector((store) => store.chat);
	const [otherUser, setOtherUser] = useState(null);
	const { authenticatedUser: user } = useAppSelector((store) => store.auth);

	useEffect(() => {
		if (selectedChatUser) {
			// console.log('selectedChatUser', selectedChatUser);
			selectedChatUser?.participants?.forEach((participant) => {
				if (participant?.userId !== user?.userId) {
					// console.log('participant', participant);
					setOtherUser(participant);
				}
			});
		}
	}, [selectedChatUser, user?.userId]);

	return (
		<>
			<header className="sticky min-h-10 top-0 flex shrink-0 items-center gap-2 shadow bg-background p-2 rounded-lg">
				{selectedChatUser && (
					<>
						<div className="relative">
							<Avatar className="h-10 w-10">
								<AvatarImage
									src={otherUser?.profilePhoto}
									alt={otherUser?.name}
								/>
								<AvatarFallback>
									{otherUser?.name.slice(0, 2).toUpperCase()}
								</AvatarFallback>
							</Avatar>
							<span
								className={`absolute top-0 right-0 h-3 w-3 rounded-full border-2 border-white ${
									otherUser?.isOnline ? 'bg-green-500' : 'bg-gray-300'
								}`}
							/>
						</div>
						<div className="flex flex-col flex-grow min-w-0 ml-2">
							<div className="flex items-center justify-between w-full">
								<span className="font-medium truncate">{otherUser?.name}</span>
							</div>
						</div>
					</>
				)}
			</header>
			<ChatArea />
		</>
	);
}
