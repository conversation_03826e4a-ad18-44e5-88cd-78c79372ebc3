import { Briefcase, Building2, GraduationCap, Users } from 'lucide-react';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { useAppSelector } from '@/lib/hooks';

export function Stats({ stats, index = 0 }) {
	// console.log(` Stats - stats:`, stats.value);
	// Determine if this is an odd or even stat based on index
	const isOdd = index % 2 === 0;

	return (
		// <Card
		// 	// className={`overflow-hidden transition-all ${
		// 	// 	isOdd ? 'bg-muted/50' : 'bg-background'
		// 	// }`}
		// 	className={`overflow-hidden transition-all`}
		// >
		// 	<CardHeader className="pb-2">
		// 		<div className="flex items-center justify-between">
		// 			<CardTitle className="text-lg md:text-xl">{stats.title}</CardTitle>
		// 			<stats.icon className="size-8 text-primary" />
		// 		</div>
		// 	</CardHeader>
		// 	<CardContent>
		// 		<div className="text-2xl sm:text-3xl md:text-4xl font-bold py-4">
		// 			{stats.value || 0}
		// 		</div>
		// 	</CardContent>
		// 	<CardFooter
		// 		// className={`border-t ${isOdd ? 'bg-muted/70' : 'bg-muted/30'} py-3`}
		// 		className={`border-t py-3`}
		// 	>
		// 		{stats.footer || 'Footer looks like this'}
		// 	</CardFooter>
		// </Card>

		<Card className="@container/card">
			<CardHeader className="p-4">
				<CardDescription className="mb-3">
					{stats.icon && (
						<stats.icon className="inline-block size-6 text-primary mr-2" />
					)}{' '}
					{stats.title}
				</CardDescription>
				<CardTitle className="text-3xl font-semibold tabular-nums @[250px]/card:text-3xl">
					{stats.value || 0}
				</CardTitle>
			</CardHeader>
			<CardFooter className="flex-col items-start gap-1.5 p-4 pt-0 text-sm">
				<div className="text-muted-foreground">{stats.footer}</div>
			</CardFooter>
		</Card>
	);
}

// Example usage in a parent component
export function StatsGrid({ statsData }) {
	return (
		// <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
			{statsData.map((stat, index) => (
				<Stats key={index} stats={stat} index={index} />
			))}
		</div>
	);
}
