'use client';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { MoreHorizontal, Edit, Trash } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import AddEditDialog from './add-edit-dialog';
import { deleteBusinessUnit } from '@/lib/features/company-infrastructure/businessUnitSlice';

export function DataTableRowActions({ row, dispatch }) {
	const businessUnit = row.original;
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [showStatusDialog, setShowStatusDialog] = useState(false);
	const [showAddEditDialog, setShowAddEditDialog] = useState(false);
	const [selectedBusinessUnit, setSelectedBusinessUnit] = useState(null);

	const handleView = () => {
		dispatch({
			type: 'businessUnit/viewBusinessUnit',
			payload: businessUnit._id,
		});
		// Navigate to view page
		window.location.href = `/business-units/${businessUnit._id}`;
	};

	const handleEditClick = () => {
		setSelectedBusinessUnit(businessUnit);
		setShowAddEditDialog(true);
	};

	const handleDelete = async () => {
		const result = await dispatch(deleteBusinessUnit([businessUnit._id]));

		if (deleteBusinessUnit.fulfilled.match(result)) {
			setShowDeleteDialog(false);
		}
	};

	const handleToggleStatus = () => {
		dispatch({
			type: 'businessUnit/toggleStatus',
			payload: {
				id: businessUnit._id,
				deleted: !businessUnit.deleted,
			},
		});

		toast({
			title: businessUnit.deleted
				? 'Business Unit activated'
				: 'Business Unit deactivated',
			description: `${businessUnit.name} has been ${
				businessUnit.deleted ? 'activated' : 'deactivated'
			}.`,
		});
		setShowStatusDialog(false);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					{/* <DropdownMenuItem onClick={handleView}>
						<Eye className="mr-2 h-4 w-4" />
						View Details
					</DropdownMenuItem> */}
					<DropdownMenuItem onClick={handleEditClick}>
						<Edit className="mr-2 h-4 w-4" />
						Edit
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					{/* <DropdownMenuItem onClick={() => setShowStatusDialog(true)}>
						{businessUnit.deleted ? (
							<>
								<CheckCircle className="mr-2 h-4 w-4 text-green-500" />
								Activate
							</>
						) : (
							<>
								<XCircle className="mr-2 h-4 w-4 text-destructive" />
								Deactivate
							</>
						)}
					</DropdownMenuItem> */}
					{/* <DropdownMenuSeparator /> */}
					<DropdownMenuItem
						onClick={() => setShowDeleteDialog(true)}
						className="text-destructive focus:text-destructive"
					>
						<Trash className="mr-2 h-4 w-4" />
						Delete
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Delete Confirmation Dialog */}
			<Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							Are you sure you want to delete this business unit?
						</DialogTitle>
						<DialogDescription>
							This action cannot be undone. This will permanently delete the
							business unit and may affect departments and employees assigned to
							it.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowDeleteDialog(false)}
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={handleDelete}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Status Change Confirmation Dialog */}
			<Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{businessUnit.deleted
								? 'Are you sure you want to activate this business unit?'
								: 'Are you sure you want to deactivate this business unit?'}
						</DialogTitle>
						<DialogDescription>
							{businessUnit.deleted
								? 'This will make the business unit available again.'
								: 'This will make the business unit unavailable for new assignments.'}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowStatusDialog(false)}
						>
							Cancel
						</Button>
						<Button
							variant={businessUnit.deleted ? 'default' : 'destructive'}
							onClick={handleToggleStatus}
						>
							{businessUnit.deleted ? 'Activate' : 'Deactivate'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Edit Business Unit Dialog */}
			{showAddEditDialog && (
				<AddEditDialog
					title="Edit Business Unit"
					desc="Edit business unit details"
					businessUnit={selectedBusinessUnit}
					showAddEditDialog={showAddEditDialog}
					setShowAddEditDialog={setShowAddEditDialog}
				/>
			)}
		</>
	);
}
