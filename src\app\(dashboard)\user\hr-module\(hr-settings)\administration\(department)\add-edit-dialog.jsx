import { But<PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { dummyEmployees } from '@/data/employeesData';
import { fetchBusinessUnits } from '@/lib/features/company-infrastructure/businessUnitSlice';
import {
	addDepartment,
	updateDepartment,
} from '@/lib/features/company-infrastructure/departmentSlice';
import { fetchAllEmployees } from '@/lib/features/employees/employeeSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { addUpdateDepartmentSchema } from '@/lib/schemas/companySchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { XCircle } from 'lucide-react';
import React, { useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';

const DepartmentAddEditDialog = ({
	isAdd,
	title,
	desc,
	department,
	showAddEditDialog,
	setShowAddEditDialog,
}) => {
	const dispatch = useAppDispatch();
	const { employees } = useAppSelector((store) => store.employee);
	const { businessUnits } = useAppSelector((store) => store.businessUnit);
	const employeeList = employees;

	const form = useForm({
		resolver: zodResolver(addUpdateDepartmentSchema),
		defaultValues: {
			departments: isAdd
				? [{ name: '', businessUnitId: '' }]
				: [{ _id: '', name: '', businessUnitId: '', admin: '' }],
		},
		mode: 'onSubmit',
		reValidateMode: 'onSubmit',
	});

	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: 'departments',
	});

	useEffect(() => {
		if (!isAdd && department) {
			form.setValue('departments', [
				{
					_id: department._id,
					name: department.name,
					businessUnitId: department.businessUnit._id,
					admin: department.admin?.userId,
				},
			]);
		}
	}, [isAdd, department, form]);

	useEffect(() => {
		if (businessUnits.length === 0) {
			dispatch(fetchBusinessUnits());
		}
		if (showAddEditDialog && employees.length === 0) {
			dispatch(fetchAllEmployees());
		}
	}, [showAddEditDialog, employees.length, dispatch, businessUnits.length]);

	const onSubmit = async (data) => {
		console.log('Submitting Data:', data);

		let result;
		if (isAdd) {
			const payload = { departments: data.departments };
			result = await dispatch(addDepartment(payload));
		} else {
			const payload = data.departments[0];
			result = await dispatch(updateDepartment(payload));
		}

		if (
			addDepartment.fulfilled.match(result) ||
			updateDepartment.fulfilled.match(result)
		) {
			setShowAddEditDialog(false);
		}
	};

	return (
		<Dialog open={showAddEditDialog} onOpenChange={setShowAddEditDialog}>
			<DialogContent className="max-h-[80vh] overflow-hidden">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{desc}</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form
						id="department-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="grid gap-4 max-h-[50vh] overflow-y-auto pt-2 pb-2 pr-2"
					>
						{fields.map((department, index) => (
							<div
								key={department.id}
								className="relative grid grid-cols-2 gap-4 border p-4 rounded-lg"
							>
								{isAdd && fields.length > 1 && (
									<button
										type="button"
										onClick={() => remove(index)}
										className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200"
									>
										<XCircle className="text-red-500" size={16} />
									</button>
								)}
								<FormField
									control={form.control}
									name={`departments.${index}.name`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>Name</FormLabel>
											<FormControl>
												<Input {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name={`departments.${index}.businessUnitId`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>Business Unit</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
											>
												<SelectTrigger>
													<SelectValue placeholder="Select a business unit" />
												</SelectTrigger>
												<SelectContent>
													<ScrollArea className="max-h-60 overflow-y-auto">
														<div>
															{businessUnits.map((businessUnit) => (
																<SelectItem
																	key={businessUnit._id}
																	value={businessUnit._id}
																>
																	{businessUnit.name} - {businessUnit.location}
																</SelectItem>
															))}
														</div>
													</ScrollArea>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
								{!isAdd && (
									<FormField
										control={form.control}
										name={`departments.${index}.admin`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Admin</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<SelectTrigger>
														<SelectValue placeholder="Select an admin" />
													</SelectTrigger>
													<SelectContent>
														<ScrollArea className="max-h-60 overflow-y-auto">
															<div>
																{employeeList.map((employee) => (
																	<SelectItem
																		key={employee._id}
																		value={employee._id}
																	>
																		{employee.name}
																	</SelectItem>
																))}
															</div>
														</ScrollArea>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}
							</div>
						))}
						{isAdd && (
							<Button
								type="button"
								variant="outline"
								onClick={() =>
									append({ name: '', businessUnitId: '', admin: '' })
								}
								className="mt-2"
							>
								Add Another Department
							</Button>
						)}
					</form>
				</Form>
				<DialogFooter>
					<Button variant="outline" onClick={() => setShowAddEditDialog(false)}>
						Cancel
					</Button>
					<Button type="submit" form="department-form">
						Confirm
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default DepartmentAddEditDialog;
