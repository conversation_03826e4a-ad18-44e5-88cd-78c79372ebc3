'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { MoreHorizontal, Edit, Trash } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import AddEditDialog from './add-edit-dialog';
import { deleteDepartment } from '@/lib/features/company-infrastructure/departmentSlice';

export function DataTableRowActions({ row, dispatch }) {
	const department = row.original;
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [showStatusDialog, setShowStatusDialog] = useState(false);
	const [showAddEditDialog, setShowAddEditDialog] = useState(false);
	const [selectedDepartment, setSelectedDepartment] = useState(null);

	const handleView = () => {
		dispatch({
			type: 'department/viewDepartment',
			payload: department._id,
		});
		// Navigate to view page
		window.location.href = `/business-units/${department._id}`;
	};

	const handleEditClick = () => {
		setSelectedDepartment(department);
		setShowAddEditDialog(true);
	};

	const handleDelete = async () => {
		const result = await dispatch(deleteDepartment([department._id]));

		if (deleteDepartment.fulfilled.match(result)) {
			setShowDeleteDialog(false);
		}
	};

	const handleToggleStatus = () => {
		dispatch({
			type: 'department/toggleStatus',
			payload: {
				id: department._id,
				deleted: !department.deleted,
			},
		});

		toast({
			title: department.deleted
				? 'Department activated'
				: 'Department deactivated',
			description: `${department.name} has been ${
				department.deleted ? 'activated' : 'deactivated'
			}.`,
		});
		setShowStatusDialog(false);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					{/* <DropdownMenuItem onClick={handleView}>
						<Eye className="mr-2 h-4 w-4" />
						View Details
					</DropdownMenuItem> */}
					<DropdownMenuItem onClick={handleEditClick}>
						<Edit className="mr-2 h-4 w-4" />
						Edit
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					{/* <DropdownMenuItem onClick={() => setShowStatusDialog(true)}>
						{department.deleted ? (
							<>
								<CheckCircle className="mr-2 h-4 w-4 text-green-500" />
								Activate
							</>
						) : (
							<>
								<XCircle className="mr-2 h-4 w-4 text-destructive" />
								Deactivate
							</>
						)}
					</DropdownMenuItem> */}
					{/* <DropdownMenuSeparator /> */}
					<DropdownMenuItem
						onClick={() => setShowDeleteDialog(true)}
						className="text-destructive focus:text-destructive"
					>
						<Trash className="mr-2 h-4 w-4" />
						Delete
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Delete Confirmation Dialog */}
			<Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							Are you sure you want to delete this department?
						</DialogTitle>
						<DialogDescription>
							This action cannot be undone. This will permanently delete the
							department and may affect designations assigned to it.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowDeleteDialog(false)}
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={handleDelete}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Status Change Confirmation Dialog */}
			<Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{department.deleted
								? 'Are you sure you want to activate this department?'
								: 'Are you sure you want to deactivate this department?'}
						</DialogTitle>
						<DialogDescription>
							{department.deleted
								? 'This will make the department available again.'
								: 'This will make the department unavailable for new assignments.'}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowStatusDialog(false)}
						>
							Cancel
						</Button>
						<Button
							variant={department.deleted ? 'default' : 'destructive'}
							onClick={handleToggleStatus}
						>
							{department.deleted ? 'Activate' : 'Deactivate'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Edit Department Dialog */}
			{showAddEditDialog && (
				<AddEditDialog
					title="Edit Department"
					desc="Edit department details"
					department={selectedDepartment}
					showAddEditDialog={showAddEditDialog}
					setShowAddEditDialog={setShowAddEditDialog}
				/>
			)}
		</>
	);
}
