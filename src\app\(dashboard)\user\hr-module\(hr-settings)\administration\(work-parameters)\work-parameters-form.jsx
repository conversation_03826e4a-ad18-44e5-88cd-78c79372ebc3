'use client';

import { useEffect, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { CalendarDays, Clock, CreditCard } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { workParametersSchema } from '@/lib/schemas/companySchema';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateWorkParameters } from '@/lib/features/company-details/companyDetailsSlice';

export default function WorkParametersForm() {
	const dispatch = useAppDispatch();
	const { isLoading, companyData } = useAppSelector(
		(store) => store.companyDetails
	);

	const form = useForm({
		resolver: zodResolver(workParametersSchema),
		defaultValues: {
			monthlySchedule: {
				total: 0,
				description: '',
			},
			dailySchedule: {
				total: 0,
				description: '',
			},
			hourlySchedule: {
				total: 0,
				description: '',
			},
		},
	});

	useEffect(() => {
		if (companyData) {
			form.setValue('monthlySchedule', {
				total: Number(companyData.companyDetails.monthlySchedule.total),
				description: companyData.companyDetails.monthlySchedule.description,
			});
			form.setValue('dailySchedule', {
				total: Number(companyData.companyDetails.dailySchedule.total),
				description: companyData.companyDetails.dailySchedule.description,
			});
			form.setValue('hourlySchedule', {
				total: Number(companyData.companyDetails.hourlySchedule.total),
				description: companyData.companyDetails.hourlySchedule.description,
			});
		}
	}, [companyData, form]);

	async function onSubmit(data) {
		console.log(data);

		dispatch(updateWorkParameters(data));
	}

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)}>
				<div className="space-y-6">
					{['monthly', 'daily', 'hourly'].map((type, index) => {
						const icons = [CreditCard, CalendarDays, Clock];
						const labels = {
							monthly: 'Monthly Schedule',
							daily: 'Daily Schedule',
							hourly: 'Hourly Schedule',
						};
						const totalLabels = {
							monthly: 'Total Months',
							daily: 'Working Days',
							hourly: 'Working Hours',
						};
						const descriptions = {
							monthly: 'Number of months in a year',
							daily: 'Number of working days in a month',
							hourly: 'Number of working hours in a day',
						};
						const Icon = icons[index];
						return (
							<div key={type} className="">
								<div className="flex items-center gap-2">
									<Icon className="h-5 w-5 text-muted-foreground" />
									<h3 className="text-lg font-medium">{labels[type]}</h3>
								</div>
								<div className="grid gap-4 sm:grid-cols-2">
									<FormField
										control={form.control}
										name={`${type}Schedule.total`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>{totalLabels[type]}</FormLabel>
												<FormControl>
													<Input
														type="number"
														{...field}
														onChange={(e) =>
															field.onChange(Number(e.target.value))
														}
														value={field.value}
													/>
												</FormControl>
												<FormDescription>{descriptions[type]}</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`${type}Schedule.description`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Description</FormLabel>
												<FormControl>
													<Input {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>
						);
					})}
				</div>
				<div className="flex justify-end mt-4">
					<Button type="submit" disabled={isLoading} className="ml-auto">
						{isLoading ? 'Saving...' : 'Save Changes'}
					</Button>
				</div>
			</form>
		</Form>
	);
}
