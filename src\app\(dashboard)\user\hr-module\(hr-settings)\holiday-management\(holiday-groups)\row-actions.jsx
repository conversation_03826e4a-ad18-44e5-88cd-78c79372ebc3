'use client';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { useRouter } from 'next/navigation';
import { MoreHorizontal, Edit, Trash, Eye, Copy } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import AddEditDialog from './add-edit-dialog';
import DetailsDialog from './details-dialog';
import CopyDialog from './copy-dialog';
import { deleteHolidayGroup } from '@/lib/features/holiday/holidayGroupSlice';

export function DataTableRowActions({ row, dispatch }) {
	const router = useRouter();
	const holidayGroup = row.original;
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [showStatusDialog, setShowStatusDialog] = useState(false);
	const [showAddEditDialog, setShowAddEditDialog] = useState(false);
	const [showDetailsDialog, setShowDetailsDialog] = useState(false);
	const [showCopyDialog, setShowCopyDialog] = useState(false);
	const [selectedHolidayGroup, setSelectedHolidayGroup] = useState(null);

	const handleView = () => {
		setSelectedHolidayGroup(holidayGroup);
		setShowDetailsDialog(true);
	};

	const handleEditClick = () => {
		setSelectedHolidayGroup(holidayGroup);
		setShowAddEditDialog(true);
	};

	const handleCopyClick = () => {
		setSelectedHolidayGroup(holidayGroup);
		setShowCopyDialog(true);
	};

	const handleDelete = async () => {
		const result = await dispatch(deleteHolidayGroup([holidayGroup._id]));

		if (deleteHolidayGroup.fulfilled.match(result)) {
			setShowDeleteDialog(false);
		}
	};

	const handleToggleStatus = () => {
		dispatch({
			type: 'holidayGroup/toggleStatus',
			payload: {
				id: holidayGroup._id,
				deleted: !holidayGroup.deleted,
			},
		});

		toast({
			title: holidayGroup.deleted
				? 'Holiday group activated'
				: 'Holiday group deactivated',
			description: `${holidayGroup.name} has been ${
				holidayGroup.deleted ? 'activated' : 'deactivated'
			}.`,
		});
		setShowStatusDialog(false);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					<DropdownMenuItem onClick={handleView}>
						<Eye className="mr-2 h-4 w-4" />
						View Details
					</DropdownMenuItem>
					<DropdownMenuItem onClick={handleEditClick}>
						<Edit className="mr-2 h-4 w-4" />
						Edit
					</DropdownMenuItem>
					<DropdownMenuItem onClick={handleCopyClick}>
						<Copy className="mr-2 h-4 w-4" />
						Copy
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					{/* <DropdownMenuItem onClick={() => setShowStatusDialog(true)}>
						{holidayGroup.deleted ? (
							<>
								<CheckCircle className="mr-2 h-4 w-4 text-green-500" />
								Activate
							</>
						) : (
							<>
								<XCircle className="mr-2 h-4 w-4 text-destructive" />
								Deactivate
							</>
						)}
					</DropdownMenuItem> */}
					{/* <DropdownMenuSeparator /> */}
					<DropdownMenuItem
						onClick={() => setShowDeleteDialog(true)}
						className="text-destructive focus:text-destructive"
					>
						<Trash className="mr-2 h-4 w-4" />
						Delete
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Delete Confirmation Dialog */}
			<Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							Are you sure you want to delete this holiday group?
						</DialogTitle>
						<DialogDescription>
							This action cannot be undone. This will permanently delete the
							holiday group and may affect the holidays assigned to it.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowDeleteDialog(false)}
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={handleDelete}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Status Change Confirmation Dialog */}
			<Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{holidayGroup.deleted
								? 'Are you sure you want to activate this holiday group?'
								: 'Are you sure you want to deactivate this holiday group?'}
						</DialogTitle>
						<DialogDescription>
							{holidayGroup.deleted
								? 'This will make the holiday group available again.'
								: 'This will make the holiday group unavailable for new assignments.'}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowStatusDialog(false)}
						>
							Cancel
						</Button>
						<Button
							variant={holidayGroup.deleted ? 'default' : 'destructive'}
							onClick={handleToggleStatus}
						>
							{holidayGroup.deleted ? 'Activate' : 'Deactivate'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Edit Holiday group Dialog */}
			{showAddEditDialog && (
				<AddEditDialog
					title="Edit Holiday group"
					desc="Edit holiday group details"
					holidayGroup={selectedHolidayGroup}
					showAddEditDialog={showAddEditDialog}
					setShowAddEditDialog={setShowAddEditDialog}
				/>
			)}

			{/* View Holiday group Details Dialog */}
			{showDetailsDialog && (
				<DetailsDialog
					holidayGroup={selectedHolidayGroup}
					showDetailsDialog={showDetailsDialog}
					setShowDetailsDialog={setShowDetailsDialog}
				/>
			)}

			{/* Copy Holiday group Dialog */}
			{showCopyDialog && (
				<CopyDialog
					holidayGroup={selectedHolidayGroup}
					showCopyDialog={showCopyDialog}
					setShowCopyDialog={setShowCopyDialog}
				/>
			)}
		</>
	);
}
