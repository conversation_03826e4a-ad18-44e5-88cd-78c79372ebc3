'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ListFilter, PlusCircle } from 'lucide-react';
import HolidayGroupsTable from './(holiday-groups)/table';
import HolidaysTable from './(holidays)/table';
import HolidayGroupAddEditDialog from './(holiday-groups)/add-edit-dialog';
import HolidayAddEditDialog from './(holidays)/add-edit-dialog';
import { useState } from 'react';

const HolidayManagement = () => {
	const [showAddEditDialog, setShowAddEditDialog] = useState(false);
	const [isAdd, setIsAdd] = useState(false);

	const tabsTriggers = [
		{ name: 'Holiday Groups', value: 'holiday-groups' },
		{ name: 'Holidays', value: 'holidays' },
	];

	const tabsContents = [
		{
			value: 'holiday-groups',
			title: 'Holiday Groups',
			desc: 'Manage your holiday groups.',
			component: <HolidayGroupsTable />,
			dialogComponent: HolidayGroupAddEditDialog,
			dialogTitle: 'Add Holiday Group',
			dialogDesc: 'Add a new holiday group',
		},
		{
			value: 'holidays',
			title: 'Holidays',
			desc: 'Manage your holidays.',
			component: <HolidaysTable />,
			dialogComponent: HolidayAddEditDialog,
			dialogTitle: 'Add Holiday',
			dialogDesc: 'Add a new holiday',
		},
	];

	return (
		<section className="w-full h-full p-4">
			<header className="flex flex-col gap-2">
				<h1 className="text-xl md:text-2xl font-semibold">
					Holiday Management
				</h1>
				<p className="font-medium text-gray-500">
					Manage your holiday groups and holidays.
				</p>
				<Separator />
			</header>
			<main className="grid flex-1 items-start gap-4 md:gap-8 w-full mt-4">
				<Tabs defaultValue={tabsTriggers[0].value}>
					<div className="flex items-center">
						<TabsList>
							{tabsTriggers.map((trigger) => (
								<TabsTrigger
									key={trigger.value}
									value={trigger.value}
									className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
								>
									{trigger.name}
								</TabsTrigger>
							))}
						</TabsList>
					</div>

					{tabsContents.map((content) => (
						<TabsContent key={content.value} value={content.value}>
							<Card>
								<CardHeader className="flex flex-row justify-between items-center">
									<div>
										<CardTitle>{content.title}</CardTitle>
										<CardDescription>{content.desc}</CardDescription>
									</div>
									{content.dialogComponent && (
										<>
											<Button
												className="flex items-center gap-2"
												onClick={() => {
													setShowAddEditDialog(true);
													setIsAdd(true);
												}}
											>
												<PlusCircle className="h-5 w-5" />
												Add
											</Button>
											{showAddEditDialog && (
												<content.dialogComponent
													isAdd={isAdd}
													title={content.dialogTitle}
													desc={content.dialogDesc}
													showAddEditDialog={showAddEditDialog}
													setShowAddEditDialog={setShowAddEditDialog}
												/>
											)}
										</>
									)}
								</CardHeader>
								<CardContent>{content.component}</CardContent>
							</Card>
						</TabsContent>
					))}
				</Tabs>
			</main>
		</section>
	);
};

export default HolidayManagement;
