'use client';

import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { Separator } from '@/components/ui/separator';
import { SimpleLoader } from '@/components/loading-component';
import EmployeeView from '@/components/employee-details';
import { useEffect } from 'react';
import { fetchEmployeeDetails } from '@/lib/features/employees/employeeSlice';

export default function EmployeeDetailsPage({ params }) {
	const dispatch = useAppDispatch();
	const { isLoading, employeeDetails } = useAppSelector(
		(store) => store.employee
	);
	// console.log(` EmployeeDetailsPage - employeeDetails:`, employeeDetails);
	const employeeId = params?.id;
	useEffect(() => {
		dispatch(fetchEmployeeDetails(employeeId));
	}, [dispatch, employeeId]);

	if (isLoading) {
		return (
			<div className="w-full h-full flex items-center justify-center">
				<SimpleLoader />
			</div>
		);
	}

	return (
		<div className="container mx-auto px-2">
			<h1 className="text-2xl font-bold text-gray-700 dark:text-gray-100">
				Employee Details
			</h1>
			<Separator className="my-2" />
			{employeeDetails ? (
				<EmployeeView employee={employeeDetails} />
			) : (
				<p className="text-gray-500">No employee data available.</p>
			)}
		</div>
	);
}
