'use client';

import { useEffect, useState } from 'react';
import {
	BarC<PERSON>,
	Bar,
	Pie<PERSON>hart,
	Pie,
	LineChart,
	Line,
	CartesianGrid,
	XAxis,
	YAxis,
	Cell,
	RadialBarChart,
	RadialBar,
	Legend,
	ResponsiveContainer,
	LabelList,
	Label,
	PolarRadiusAxis,
} from 'recharts';
import {
	Users,
	Building2,
	Briefcase,
	GraduationCap,
	TrendingUp,
} from 'lucide-react';

import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	ChartConfig,
	ChartContainer,
	ChartTooltip,
	ChartTooltipContent,
} from '@/components/ui/chart';
import { EmployeeQualificationsBarChart } from '@/components/employee-qualifications-barchart';
import OrganizationalChart from '@/components/organizational-chart';
import {
	Stats,
	StatsGrid,
} from '@/app/(dashboard)/client-admin/hr-module/(dashboard-components)/dashboard-stats';
import {
	GenderDistribution,
	GenderDistributionAlt,
	GenderDistributionCard,
} from './(dashboard-components)/gender-stats';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { fetchGenderAndEducationData } from '@/lib/features/hr-module/hrModuleSlice';
import EmployeeSkillsCard from '@/components/employee-skills-card';

export default function HRDashboard() {
	const dispatch = useAppDispatch();
	const {
		genderData,
		educationData,
		skillsData,
		totalBusinessUnits,
		totalDepartments,
		totalDesignations,
		totalEmployees,
	} = useAppSelector((store) => store.hrModule);
	const [yearFilter, setYearFilter] = useState('2023');
	const stats = [
		{
			title: 'Business Unit',
			value: totalBusinessUnits,
			footer: 'Total business units',
			icon: Building2,
		},
		{
			title: 'Department',
			value: totalDepartments,
			footer: 'Total Departments',
			icon: Briefcase,
		},
		{
			title: 'Designation',
			value: totalDesignations,
			footer: 'Total Designation',
			icon: GraduationCap,
		},
		{
			title: 'Employee',
			value: totalEmployees,
			footer: 'Total Employees',
			icon: Users,
		},
	];
	// Different hiring/firing data for different years
	const hiringFiringDataByYear = {
		2021: [
			{ month: 'Jan', onboarded: 8, offboarded: 2 },
			{ month: 'Feb', onboarded: 6, offboarded: 1 },
			{ month: 'Mar', onboarded: 10, offboarded: 3 },
			{ month: 'Apr', onboarded: 5, offboarded: 1 },
			{ month: 'May', onboarded: 7, offboarded: 2 },
			{ month: 'Jun', onboarded: 9, offboarded: 2 },
			{ month: 'Jul', onboarded: 6, offboarded: 1 },
			{ month: 'Aug', onboarded: 4, offboarded: 0 },
			{ month: 'Sep', onboarded: 8, offboarded: 2 },
			{ month: 'Oct', onboarded: 11, offboarded: 3 },
			{ month: 'Nov', onboarded: 7, offboarded: 1 },
			{ month: 'Dec', onboarded: 4, offboarded: 1 },
		],
		2022: [
			{ month: 'Jan', onboarded: 10, offboarded: 2 },
			{ month: 'Feb', onboarded: 8, offboarded: 2 },
			{ month: 'Mar', onboarded: 12, offboarded: 3 },
			{ month: 'Apr', onboarded: 7, offboarded: 1 },
			{ month: 'May', onboarded: 9, offboarded: 2 },
			{ month: 'Jun', onboarded: 11, offboarded: 3 },
			{ month: 'Jul', onboarded: 8, offboarded: 2 },
			{ month: 'Aug', onboarded: 6, offboarded: 1 },
			{ month: 'Sep', onboarded: 10, offboarded: 2 },
			{ month: 'Oct', onboarded: 13, offboarded: 3 },
			{ month: 'Nov', onboarded: 8, offboarded: 2 },
			{ month: 'Dec', onboarded: 5, offboarded: 1 },
		],
		2023: [
			{ month: 'Jan', onboarded: 12, offboarded: 3 },
			{ month: 'Feb', onboarded: 9, offboarded: 2 },
			{ month: 'Mar', onboarded: 15, offboarded: 4 },
			{ month: 'Apr', onboarded: 8, offboarded: 1 },
			{ month: 'May', onboarded: 11, offboarded: 2 },
			{ month: 'Jun', onboarded: 14, offboarded: 3 },
			{ month: 'Jul', onboarded: 10, offboarded: 2 },
			{ month: 'Aug', onboarded: 7, offboarded: 1 },
			{ month: 'Sep', onboarded: 13, offboarded: 3 },
			{ month: 'Oct', onboarded: 16, offboarded: 4 },
			{ month: 'Nov', onboarded: 9, offboarded: 2 },
			{ month: 'Dec', onboarded: 6, offboarded: 1 },
		],
		2024: [
			{ month: 'Jan', onboarded: 14, offboarded: 3 },
			{ month: 'Feb', onboarded: 11, offboarded: 2 },
			{ month: 'Mar', onboarded: 17, offboarded: 4 },
			{ month: 'Apr', onboarded: 10, offboarded: 2 },
			{ month: 'May', onboarded: 13, offboarded: 3 },
			{ month: 'Jun', onboarded: 16, offboarded: 3 },
			{ month: 'Jul', onboarded: 12, offboarded: 2 },
			{ month: 'Aug', onboarded: 9, offboarded: 1 },
			{ month: 'Sep', onboarded: 15, offboarded: 3 },
			{ month: 'Oct', onboarded: 18, offboarded: 4 },
			{ month: 'Nov', onboarded: 11, offboarded: 2 },
			{ month: 'Dec', onboarded: 8, offboarded: 1 },
		],
	};

	const salaryRangeData = [
		{ range: '0-30k', count: 87 },
		{ range: '30k-50k', count: 156 },
		{ range: '50k-80k', count: 124 },
		{ range: '80k-100k', count: 76 },
		{ range: '100k+', count: 44 },
	];

	const genderConfig = {
		male: {
			label: 'Male',
			color: 'hsl(var(--chart-1))',
		},
		female: {
			label: 'Female',
			color: 'hsl(var(--chart-2))',
		},
	};

	const hiringConfig = {
		onboarded: {
			label: 'onboarded',
			color: 'hsl(var(--chart-1))',
		},
		offboarded: {
			label: 'offboarded',
			color: 'hsl(var(--chart-3))',
		},
	};

	// Calculate total onboarded and offboarded for the selected year
	const currentYearData = hiringFiringDataByYear[yearFilter] || [];
	const totalonboarded = currentYearData.reduce(
		(sum, item) => sum + item.onboarded,
		0
	);
	const totaloffboarded = currentYearData.reduce(
		(sum, item) => sum + item.offboarded,
		0
	);

	useEffect(() => {
		dispatch(fetchGenderAndEducationData());
	}, [dispatch]);

	return (
		<div className="flex w-full flex-col bg-background">
			<div className="flex flex-col">
				<main className="flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
					{/* Key Metrics */}

					{/* Charts - First Row */}
					<div className="grid gap-4 lg:grid-cols-12">
						<div className="col-span-12">
							<StatsGrid statsData={stats} />
						</div>
						<div className="col-span-12 lg:col-span-6">
							{/* Gender Distribution */}
							<GenderDistributionAlt
								maleCount={genderData?.male}
								femaleCount={genderData?.female}
								othersCount={genderData?.others}
							/>
						</div>

						<div className="col-span-12 lg:col-span-6">
							{/* <EmployeeQualificationsBarChart /> */}
							<EmployeeSkillsCard
								hardSkills={skillsData?.hardSkills}
								softSkills={skillsData?.softSkills}
								underGraduate={educationData?.UNDER_GRADUATE}
								postGraduate={educationData?.POST_GRADUATE}
								noFormalEducation={educationData?.NO_FORMAL_EDUCATION}
							/>
						</div>
						{/* Hiring and Firing Trends */}
						{/* <Card className="overflow-hidden">
							<CardHeader className="flex flex-row items-center justify-between px-4 pb-2">
								<div>
									<CardTitle className="text-base sm:text-lg">
										Hiring & Separation Trends
									</CardTitle>
									<CardDescription>Monthly data</CardDescription>
								</div>
								<Select value={yearFilter} onValueChange={setYearFilter}>
									<SelectTrigger className="w-[80px] sm:w-[100px]">
										<SelectValue placeholder="Year" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="2021">2021</SelectItem>
										<SelectItem value="2022">2022</SelectItem>
										<SelectItem value="2023">2023</SelectItem>
										<SelectItem value="2024">2024</SelectItem>
									</SelectContent>
								</Select>
							</CardHeader>
							<CardContent className="px-2 pb-4 h-[300px]">
								<ChartContainer
									config={hiringConfig}
									className="h-[calc(100%-24px)] w-full"
								>
									<ResponsiveContainer width="100%" height="100%">
										<LineChart data={hiringFiringDataByYear[yearFilter] || []}>
											<CartesianGrid strokeDasharray="3 3" vertical={false} />
											<XAxis
												dataKey="month"
												tick={{ fontSize: 10 }}
												tickFormatter={(value) => {
													// On small screens, show shorter month names
													return window.innerWidth < 640
														? value.substring(0, 1)
														: value;
												}}
											/>
											<YAxis tick={{ fontSize: 10 }} />
											<ChartTooltip content={<ChartTooltipContent />} />
											<Line
												type="monotone"
												dataKey="onboarded"
												stroke="var(--color-onboarded)"
												strokeWidth={2}
												dot={{ r: 3 }}
												activeDot={{ r: 5 }}
											/>
											<Line
												type="monotone"
												dataKey="offboarded"
												stroke="var(--color-offboarded)"
												strokeWidth={2}
												dot={{ r: 3 }}
												activeDot={{ r: 5 }}
											/>
											<Legend
												layout="horizontal"
												verticalAlign="top"
												align="center"
												wrapperStyle={{ fontSize: '12px' }}
											/>
										</LineChart>
									</ResponsiveContainer>
								</ChartContainer>
								<div className="mt-2 flex justify-between text-xs sm:text-sm">
									<div>
										Total onboarded:{' '}
										<span className="font-bold text-green-600">
											{totalonboarded}
										</span>
									</div>
									<div>
										Total Separated:{' '}
										<span className="font-bold text-red-600">
											{totaloffboarded}
										</span>
									</div>
								</div>
							</CardContent>
						</Card> */}
					</div>

					{/* Charts - Second Row */}
					<div className="grid gap-6 md:grid-cols-2">
						{/* Salary Range */}
						{/* <Card className="overflow-hidden">
							<CardHeader className="px-4 pb-2">
								<CardTitle className="text-base sm:text-lg">
									Salary Distribution
								</CardTitle>
								<CardDescription>Employees by salary range</CardDescription>
							</CardHeader>
							<CardContent className="px-2 pb-4 h-[300px]">
								<ChartContainer
									config={{
										count: {
											label: 'Employees',
											color: 'hsl(var(--chart-1))',
										},
									}}
									className="h-full w-full"
								>
									<ResponsiveContainer width="100%" height="100%">
										<BarChart data={salaryRangeData}>
											<CartesianGrid strokeDasharray="3 3" vertical={false} />
											<XAxis dataKey="range" tick={{ fontSize: 10 }} />
											<YAxis tick={{ fontSize: 10 }} />
											<ChartTooltip content={<ChartTooltipContent />} />
											<Bar
												dataKey="count"
												fill="var(--color-count)"
												radius={4}
											/>
											<Legend
												layout="horizontal"
												verticalAlign="top"
												align="center"
												wrapperStyle={{ fontSize: '12px' }}
											/>
										</BarChart>
									</ResponsiveContainer>
								</ChartContainer>
							</CardContent>
						</Card> */}
						{/* We will use this in payroll dashboard */}
					</div>
					{/* <div>
						<OrganizationalChart userRole="admin" />
					</div> */}
				</main>
			</div>
		</div>
	);
}
