'use client';

import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { verifyOnboardingLink } from '@/lib/features/employees/employeeOnboardingSlice';
import { Loader2 } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { EmployeePersonalDetailsForm } from '@/components/employee-personal-details-form';
import { EmployeeEducationSkillsForm } from '@/components/employee-qualifications-form';
import { EmployeeContactDetailsForm } from '@/components/employee-contact-form';
import { ThankYouPage } from '@/components/employee-onboarding-link/thank-you';

export default function Page({ params }) {
	const dispatch = useAppDispatch();
	const { isLoading, onBoardingEmployeeDetails, step } = useAppSelector(
		(state) => state.employeeOnboarding
	);
	const [status, setStatus] = useState('verifying'); // 'verifying', 'success', 'error'

	useEffect(() => {
		const verify = async () => {
			try {
				const result = await dispatch(
					verifyOnboardingLink({ token: params.token })
				);

				if (verifyOnboardingLink.fulfilled.match(result)) {
					setStatus('success');
				} else {
					setStatus('error');
				}
			} catch (e) {
				setStatus('error');
			}
		};

		verify();
	}, [dispatch, params.token]);

	const stepComponents = [
		{
			id: 1,
			component: <EmployeePersonalDetailsForm />,
			title: 'Personal Details',
		},
		{
			id: 2,
			component: <EmployeeEducationSkillsForm />,
			title: 'Qualifications',
		},
		{
			id: 3,
			component: <EmployeeContactDetailsForm />,
			title: 'Contact Details',
		},
		{
			id: 4,
			component: <div>Your onboarding form is completed, thank you</div>,
			title: 'Thanks for filling the form',
		},
	];

	if (status === 'verifying' || isLoading) {
		return (
			<div className="flex items-center justify-center h-screen">
				<div className="text-center">
					<div className="flex items-center gap-2 justify-center text-muted-foreground text-lg">
						<Loader2 className="animate-spin" />
						<span>Verifying your identity...</span>
					</div>
				</div>
			</div>
		);
	}

	if (status === 'error') {
		return (
			<div className="flex items-center justify-center h-screen">
				<div className="text-center">
					<h2 className="text-xl font-semibold text-red-500">
						Invalid or expired link
					</h2>
					<p className="mt-2 text-muted-foreground">
						Please contact your admin for a new onboarding link
					</p>
				</div>
			</div>
		);
	}

	if (step === 4) {
		return <ThankYouPage />;
	}

	// Show onboarding form if success
	return (
		<div className="container mx-auto py-10 px-4 max-h-full overflow-y-auto">
			<h1 className="text-2xl font-bold">
				Welcome {onBoardingEmployeeDetails?.name || ''}!
			</h1>
			<p className="mt-2 text-muted-foreground">
				Your identity has been verified. Please complete your onboarding form
				below.
			</p>

			<header className="grid grid-cols-12 gap-2 my-6">
				<Separator className="col-span-12 mt-4" />
			</header>

			<main>
				<Progress
					value={(step / stepComponents.length) * 100}
					className="mb-4"
				/>
				<Card>
					<CardHeader>
						<CardTitle className="text-xl">
							{stepComponents[step - 1]?.title}
						</CardTitle>
						<Separator />
					</CardHeader>
					<CardContent>{stepComponents[step - 1]?.component}</CardContent>
				</Card>
			</main>
		</div>
	);
}
