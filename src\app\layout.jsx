import localFont from 'next/font/local';
import './globals.css';
import StoreProvider from './StoreProvider';
import { Toaster } from 'sonner';
import { ScrollArea } from '@/components/ui/scroll-area';
import ThemeProvider from '@/components/theme-provider';

const geistSans = localFont({
	src: './fonts/GeistVF.woff',
	variable: '--font-geist-sans',
	weight: '100 900',
});
const geistMono = localFont({
	src: './fonts/GeistMonoVF.woff',
	variable: '--font-geist-mono',
	weight: '100 900',
});

export const metadata = {
	title: 'Vallu<PERSON>',
	description: 'Manage your team and automate your payroll with ease',
};

export default function RootLayout({ children }) {
	return (
		<StoreProvider>
			<html
				lang="en"
				className={`${geistSans.variable} ${geistMono.variable}`}
				suppressHydrationWarning
			>
				<body className="antialiased">
					<ThemeProvider
						attribute="class"
						defaultTheme="system"
						enableSystem
						disableTransitionOnChange
					>
						{/* <ScrollArea className="h-[100dvh]"> */}
						{children}
						<Toaster position={'top-right'} richColors />
						{/* </ScrollArea> */}
					</ThemeProvider>
				</body>
			</html>
		</StoreProvider>
	);
}
