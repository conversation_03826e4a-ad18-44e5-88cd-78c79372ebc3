'use client';

import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { showUser } from '@/lib/features/auth/authSlice';
import { Loading } from '@/components/loading-component';

export default function ProtectedLayout({ children, userType }) {
	const {
		authenticatedUser: user,
		isLoading,
		isLoggedOut,
	} = useAppSelector((store) => store.auth);
	const router = useRouter();
	const dispatch = useAppDispatch();
	const [isChecking, setIsChecking] = useState(true);

	useEffect(() => {
		const checkUser = async () => {
			if (!user) {
				await dispatch(showUser());
			}
			setIsChecking(false);
		};

		checkUser();
	}, [dispatch, user]);

	useEffect(() => {
		if (!isChecking && !isLoading) {
			if (user?.isOnboard) {
				// toast.warning(
				// 	'Looks like you are already onboarded, Kindly <PERSON><PERSON> to continue.'
				// );
				router.push('/login');
			} else if (!user) {
				toast.error('You need to be authenticated to access this page');
				router.push('/login');
			}
			// } else if (user?.type !== userType && user?.type !== 'super') {
			// 	toast.error("You don't have permission to access this page.");
			// 	router.push('/');
			// }
		}
	}, [isLoading, isLoggedOut, router, user, user?.isOnboard, isChecking]);

	if (isChecking || isLoading) {
		return <Loading isLoading={true} />; // Or a loading spinner component
	}

	return user && !user?.isOnboard ? children : null;
}
