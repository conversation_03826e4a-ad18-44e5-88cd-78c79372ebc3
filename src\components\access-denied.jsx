'use client';

import { AlertTriangle, Home, LogIn, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

const AccessDenied = ({
	title = 'Access Denied',
	message = "You don't have permission to view this page.",
	showHomeButton = true,
	showLoginButton = true,
	showBackButton = true,
	onHome = () => (window.location.href = '/'),
	onLogin = () => (window.location.href = '/login'),
	onBack = () => window.history.back(),
	customActions = null,
}) => {
	return (
		<div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-50 p-4">
			<Card className="w-full max-w-md shadow-lg border-red-200">
				<CardHeader className="text-center pb-4">
					<div className="mx-auto mb-4 w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
						<AlertTriangle className="w-8 h-8 text-red-600" />
					</div>
					<CardTitle className="text-2xl font-bold text-red-700">
						{title}
					</CardTitle>
					<CardDescription className="text-red-600 mt-2">
						{message}
					</CardDescription>
				</CardHeader>

				<CardContent className="space-y-4">
					<Alert className="border-red-200 bg-red-50">
						<AlertTriangle className="h-4 w-4 text-red-600" />
						<AlertDescription className="text-red-700">
							This page is restricted. Please contact your administrator if you
							believe this is an error.
						</AlertDescription>
					</Alert>

					<div className="text-sm text-gray-600 space-y-2">
						<p>Possible reasons for this error:</p>
						<ul className="list-disc list-inside space-y-1 ml-2">
							<li>You are not logged in</li>
							<li>Your account lacks the required permissions</li>
							<li>Your session has expired</li>
							<li>The page has been moved or deleted</li>
						</ul>
					</div>
				</CardContent>

				<CardFooter className="flex flex-col space-y-3 pt-6">
					{customActions ? (
						customActions
					) : (
						<div className="flex flex-col w-full space-y-2">
							{showLoginButton && (
								<Button
									onClick={onLogin}
									className="w-full bg-red-600 hover:bg-red-700"
								>
									<LogIn className="w-4 h-4 mr-2" />
									Sign In
								</Button>
							)}

							<div className="flex space-x-2">
								{showBackButton && (
									<Button
										variant="outline"
										onClick={onBack}
										className="flex-1 border-red-200 text-red-700 hover:bg-red-50 bg-transparent"
									>
										<ArrowLeft className="w-4 h-4 mr-2" />
										Go Back
									</Button>
								)}

								{showHomeButton && (
									<Button
										variant="outline"
										onClick={onHome}
										className="flex-1 border-red-200 text-red-700 hover:bg-red-50 bg-transparent"
									>
										<Home className="w-4 h-4 mr-2" />
										Home
									</Button>
								)}
							</div>
						</div>
					)}

					<p className="text-xs text-gray-500 text-center mt-4">
						Error Code: 403 - Forbidden
					</p>
				</CardFooter>
			</Card>
		</div>
	);
};

export default AccessDenied;
