'use client';

import * as React from 'react';
import {
	ArchiveX,
	Banknote,
	BriefcaseBusiness,
	Building,
	Building2,
	CalendarCheck,
	CalendarCheck2,
	CalendarDays,
	CheckSquare,
	ChevronRight,
	ClipboardCheck,
	ClipboardList,
	Command,
	Earth,
	File,
	FileCheck,
	FileClock,
	Fingerprint,
	HandCoins,
	Home,
	Inbox,
	Landmark,
	List,
	Megaphone,
	MessageSquareText,
	Send,
	Settings,
	ShieldCheck,
	Sparkles,
	Trash2,
	TreePalm,
	User,
	Users,
} from 'lucide-react';
import logo from '@/assets/valluva.png';
import { NavUser } from '@/components/nav-user';
import { Label } from '@/components/ui/label';
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarGroup,
	SidebarGroupContent,
	SidebarHeader,
	SidebarInput,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarMenuSub,
	SidebarMenuSubButton,
	SidebarMenuSubItem,
	useSidebar,
} from '@/components/ui/sidebar';
import { Switch } from '@/components/ui/switch';
import Image from 'next/image';
import Link from 'next/link';
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from './ui/collapsible';
import ChatUserList from './chat-user-list';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { ChatsList } from './chats-list';

// This is sample data
const data = {
	user: {
		name: 'Client Admin',
		email: '<EMAIL>',
		avatar: '/avatars/shadcn.jpg',
	},
	clientNav: [
		// {
		// 	title: 'Dashboard',
		// 	url: '/client-admin/',
		// 	icon: Home,
		// 	isActive: true,
		// },
		{
			title: 'Profile',
			url: '/client-admin/profile/',
			icon: User,
			isActive: false,
			secondaryMenus: [
				{
					icon: User,
					title: 'Profile',
					url: '#',
					items: [
						{
							title: 'Edit Profile',
							url: '/client-admin/profile/edit/',
						},
					],
				},
			],
		},
		{
			title: 'Chat',
			url: '/client-admin/chat/',
			icon: MessageSquareText,
			isActive: false,
			secondaryMenus: [
				{
					icon: Home,
					title: 'Home',
					url: '#',
					items: [
						{
							title: 'Chat Dashboard',
							url: '/client-admin/chat/',
						},
					],
				},
				{
					icon: Inbox,
					title: 'Messages',
					url: '#',
					items: [
						{
							title: 'Inbox',
							url: '/client-admin/chat/inbox/',
						},
						{
							title: 'Sent Messages',
							url: '/client-admin/chat/sent/',
						},
					],
				},
				{
					icon: Settings,
					title: 'Settings',
					url: '#',
					items: [
						{
							title: 'Chat Settings',
							url: '/client-admin/chat/settings/',
						},
						{
							title: 'Message Templates',
							url: '/client-admin/chat/templates/',
						},
					],
				},
			],
		},
		{
			title: 'HR Module',
			url: '/client-admin/hr-module/',
			icon: Users,
			isActive: true,
			secondaryMenus: [
				{
					icon: Home,
					title: 'HR Dashboard',
					url: '#',
					items: [
						{
							title: 'Overview',
							url: '/client-admin/hr-module/',
						},
					],
				},
				{
					icon: List,
					title: 'List',
					url: '#',
					items: [
						{
							title: 'Employees List',
							url: '/client-admin/hr-module/employees-list/',
						},
						{
							title: 'Verify & Update',
							url: '/client-admin/hr-module/verify-updates/',
						},
					],
				},
				{
					icon: Users,
					title: 'Employee Transitions',
					url: '#',
					items: [
						{
							title: 'Link',
							url: '/client-admin/hr-module/employee-link/',
						},
						{
							title: 'Onboarding',
							url: '/client-admin/hr-module/employee-onboarding/',
						},
						{
							title: 'Offboarding',
							url: '/client-admin/hr-module/employee-offboarding/',
						},
					],
				},
				{
					icon: Settings,
					title: 'Settings',
					url: '#',
					items: [
						{
							title: 'Administration',
							url: '/client-admin/hr-module/administration/',
						},
						{
							title: 'Holiday Management',
							url: '/client-admin/hr-module/holiday-management/',
						},
						{
							title: 'Module Admin',
							url: '/client-admin/hr-module/module-admin/',
						},
					],
				},
			],
		},
		{
			title: 'Attendance',
			url: '/client-admin/attendance-module/',
			icon: CalendarDays,
			isActive: false,
			secondaryMenus: [
				{
					icon: Home,
					title: 'Overview',
					url: '#',
					items: [
						{
							title: 'Attendance Dashboard',
							url: '/client-admin/attendance-module/',
						},
						{
							title: 'Project Logs',
							url: '/client-admin/attendance-module/project-logs/',
						},
					],
				},
				{
					icon: Settings,
					title: 'Settings',
					url: '#',
					items: [
						{
							title: 'Attendance Settings',
							url: '/client-admin/attendance-module/settings/',
						},
					],
				},
			],
		},
		{
			title: 'Communication',
			url: '/client-admin/communication-module/',
			icon: Megaphone,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: FileCheck, title: 'Submissions', url: '#', items: [] },
				{ icon: Settings, title: 'Settings', url: '#', items: [] },
			],
		},
		{
			title: 'Expense Claim',
			url: '/client-admin/expense-module/',
			icon: HandCoins,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: List, title: 'List', url: '#', items: [] },
				{ icon: ClipboardList, title: 'Report', url: '#', items: [] },
				{ icon: Settings, title: 'Claim Settings', url: '#', items: [] },
			],
		},
		{
			title: 'Leave Management',
			url: '/client-admin/leave-module/',
			icon: TreePalm,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: List, title: 'List', url: '#', items: [] },
				{ icon: ClipboardList, title: 'Report', url: '#', items: [] },
				{ icon: Settings, title: 'Claim Settings', url: '#', items: [] },
			],
		},
		{
			title: 'Payroll',
			url: '/client-admin/payroll-module/',
			icon: Banknote,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: List, title: 'List', url: '#', items: [] },
				{ icon: ShieldCheck, title: 'Compliance', url: '#', items: [] },
				{ icon: ClipboardList, title: 'Report', url: '#', items: [] },
				{ icon: Landmark, title: 'Bank', url: '#', items: [] },
				{ icon: Settings, title: 'Settings', url: '#', items: [] },
			],
		},
		{
			title: 'Projects and Tasks',
			url: '/client-admin/projects-tasks-module/projects',
			icon: ClipboardCheck,
			isActive: false,
			secondaryMenus: [
				{
					icon: BriefcaseBusiness,
					title: 'Projects and Tasks',
					url: '/client-admin/projects-tasks-module/tasks/',
					items: [
						{
							title: 'Projects',
							url: '/client-admin/projects-tasks-module/projects/',
						},
					],
				},
				// { icon: ClipboardCheck, title: 'Task', url: '/client-admin/projects-tasks-module/tasks/', items: [] },
			],
		},
		{
			title: 'Performance and Appraisals',
			url: '/client-admin/performance-appraisals-module/',
			icon: Sparkles,
			isActive: false,
		},
		{
			title: 'Settings',
			url: '/client-admin/settings/',
			icon: Settings,
			isActive: false,
			secondaryMenus: [
				{
					icon: Earth,
					title: 'Brand',
					url: '#',
					items: [
						{
							// it will be a typeable desc
							// add the reply email in the about us page
							title: 'About Us',
							url: '#',
						},
						{
							// they are going to be a pdf
							// it will have a title like date of publishing
							title: 'Notice Board',
							url: '#',
						},
						{
							// they are going to be a pdf
							// it will have chapters and version
							title: 'Hand Book',
							url: '#',
						},
						{
							// they are going to be a pdf
							// it will follow handbook and it is nearly as same as handbook
							title: 'Policy',
							url: '#',
						},
					],
				},
				{
					icon: FileClock,
					title: 'Audit Log',
					url: '#',
					items: [
						{
							title: 'Tasks Module Audit Log',
							url: '/client-admin/settings/audit-log-tasks-module',
						},
					],
				},
			],
		},
	],
};

export function AppSidebar({ ...props }) {
	// Note: I'm using state to show active item.
	// IRL you should use the url/router.
	// const [mails, setMails] = React.useState(data.mails);
	const [activeItem, setActiveItem] = React.useState(data.clientNav[0]);
	const [showChat, setShowChat] = React.useState(false); // New state for chat visibility
	const { setOpen } = useSidebar();

	return (
		<Sidebar
			variant="inset"
			collapsible="icon"
			className="overflow-hidden [&>[data-sidebar=sidebar]]:flex-row"
			{...props}
		>
			{/* This is the first sidebar */}
			{/* We disable collapsible and adjust width to icon. */}
			{/* This will make the sidebar appear as icons. */}
			<Sidebar
				variant="inset"
				collapsible="none"
				className="!w-[calc(var(--sidebar-width-icon)_+_1px)] mr-4"
			>
				<SidebarHeader>
					<SidebarMenu>
						<SidebarMenuItem>
							<SidebarMenuButton size="lg" asChild className="md:h-8 md:p-0">
								<Link href="/client-admin">
									<div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-transparent text-sidebar-primary-foreground">
										<Image
											src={logo}
											width={64}
											height={64}
											className="object-fit size-6"
											alt="Harp HR"
										/>
									</div>
									<div className="grid flex-1 text-left text-sm leading-tight">
										<span className="truncate font-semibold">Valluva</span>
									</div>
								</Link>
							</SidebarMenuButton>
						</SidebarMenuItem>
					</SidebarMenu>
				</SidebarHeader>
				<SidebarContent>
					<SidebarGroup>
						<SidebarGroupContent className="px-1.5 md:px-0">
							<SidebarMenu>
								{data.clientNav.map((item) => (
									<SidebarMenuItem key={item.title}>
										<Link href={item.url}>
											<SidebarMenuButton
												tooltip={{
													children: item.title,
													hidden: false,
												}}
												onClick={() => {
													setActiveItem(item);
													setShowChat(item.title === 'Chat'); // Enable chat view when "Chat" is clicked
													setOpen(true);
												}}
												isActive={activeItem.title === item.title}
											>
												<item.icon />
												<span>{item.title}</span>
											</SidebarMenuButton>
										</Link>
									</SidebarMenuItem>
								))}
							</SidebarMenu>
						</SidebarGroupContent>
					</SidebarGroup>
				</SidebarContent>
				<SidebarFooter>
					<NavUser user={data.user} />
				</SidebarFooter>
			</Sidebar>
			{/* This is the second sidebar */}
			{/* We disable collapsible and let it fill remaining space */}
			<Sidebar
				variant="inset"
				collapsible="none"
				className="hidden flex-1 md:flex shadow-lg rounded-xl"
			>
				<SidebarHeader className="gap-3.5 p-4 max-h-16">
					<div className="flex w-full items-center justify-between">
						<div className="text-base font-medium text-foreground">
							{activeItem.title}
						</div>
						<Label className="flex items-center gap-2 text-sm">
							<activeItem.icon />
						</Label>
					</div>
				</SidebarHeader>
				<SidebarContent>
					<SidebarGroup className="px-0">
						{showChat ? (
							<ChatsList />
						) : (
							<SidebarMenu>
								{activeItem.secondaryMenus?.map((item) => (
									<Collapsible
										key={item.title}
										asChild
										defaultOpen
										className="group/collapsible"
									>
										<SidebarMenuItem>
											<CollapsibleTrigger asChild>
												<SidebarMenuButton tooltip={item.title}>
													{item.icon && <item.icon />}
													<span>{item.title}</span>
													<ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
												</SidebarMenuButton>
											</CollapsibleTrigger>
											<CollapsibleContent>
												<SidebarMenuSub>
													{item.items?.map((subItem) => (
														<SidebarMenuSubItem key={subItem.title}>
															<SidebarMenuSubButton asChild>
																<Link href={subItem.url}>
																	<span>{subItem.title}</span>
																</Link>
															</SidebarMenuSubButton>
														</SidebarMenuSubItem>
													))}
												</SidebarMenuSub>
											</CollapsibleContent>
										</SidebarMenuItem>
									</Collapsible>
								))}
							</SidebarMenu>
						)}
					</SidebarGroup>
				</SidebarContent>
			</Sidebar>
		</Sidebar>
	);
}
