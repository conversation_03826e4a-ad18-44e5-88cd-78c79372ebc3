'use client';
import { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { Check, ChevronsUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from '@/components/ui/command';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { fetchBusinessUnits } from '@/lib/features/company-infrastructure/businessUnitSlice';
import { fetchDepartments } from '@/lib/features/company-infrastructure/departmentSlice';
import { fetchDesignations } from '@/lib/features/company-infrastructure/designationSlice';
import {
	filterAvailableEmployees,
	assignShift,
} from '@/lib/features/attendance/shiftSettingsSlice';
import { SimpleLoader } from '@/components/loading-component';

export function AssignShiftDialog({ open, onOpenChange, shift }) {
	const dispatch = useAppDispatch();

	const { businessUnits } = useAppSelector((store) => store.businessUnit);
	const { departments } = useAppSelector((store) => store.department);
	const { designations } = useAppSelector((store) => store.designation);
	const { availableEmployees, isLoading } = useAppSelector(
		(store) => store.shifts
	);

	const [selectedBusinessUnits, setSelectedBusinessUnits] = useState([]);
	const [selectedDepartments, setSelectedDepartments] = useState([]);
	const [selectedDesignations, setSelectedDesignations] = useState([]);

	const [selectedEmployees, setSelectedEmployees] = useState([]);
	const [isSubmitting, setIsSubmitting] = useState(false);

	useEffect(() => {
		if (open) {
			dispatch(fetchBusinessUnits());
			dispatch(fetchDepartments());
			dispatch(fetchDesignations());
		}
	}, [open]);

	useEffect(() => {
		if (open) {
			dispatch(
				filterAvailableEmployees({
					businessUnitIds: selectedBusinessUnits.map((u) => u._id),
					departmentIds: selectedDepartments.map((d) => d._id),
					designationIds: selectedDesignations.map((d) => d._id),
				})
			);
		}
	}, [selectedBusinessUnits, selectedDepartments, selectedDesignations, open]);

	const toggleSelection = (item, selectedList, setSelectedList) => {
		const exists = selectedList.some((i) => i._id === item._id);
		if (exists) {
			setSelectedList(selectedList.filter((i) => i._id !== item._id));
		} else {
			setSelectedList([...selectedList, item]);
		}
	};

	const handleEmployeeToggle = (employee) => {
		setSelectedEmployees((prev) =>
			prev.some((e) => e._id === employee._id)
				? prev.filter((e) => e._id !== employee._id)
				: [...prev, employee]
		);
	};

	const handleSelectAllEmployees = () => {
		if (selectedEmployees.length === availableEmployees.length) {
			setSelectedEmployees([]);
		} else {
			setSelectedEmployees(availableEmployees);
		}
	};

	const handleSubmit = async () => {
		if (isSubmitting || selectedEmployees.length === 0) return;
		setIsSubmitting(true);

		dispatch(
			assignShift({
				shiftId: shift._id,
				employeeIds: selectedEmployees.map((e) => e._id),
			})
		);

		setIsSubmitting(false);
		onOpenChange(false);
	};

	const renderMultiSelect = (label, items, selectedItems, setSelectedItems) => {
		const allSelected = selectedItems.length === items.length;

		const handleSelectAll = () => {
			if (allSelected) {
				setSelectedItems([]);
			} else {
				setSelectedItems(items);
			}
		};

		return (
			<div className="space-y-2">
				<Label>{label}</Label>
				<Popover>
					<PopoverTrigger asChild>
						<Button variant="outline" className="w-full justify-between">
							{selectedItems.length > 0
								? `${selectedItems.length} selected`
								: `Select ${label.toLowerCase()}...`}
							<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-[--radix-popover-trigger-width] p-0">
						<Command>
							<CommandInput placeholder={`Search ${label.toLowerCase()}...`} />
							<CommandList>
								<CommandEmpty>No {label.toLowerCase()} found.</CommandEmpty>
								<CommandGroup>
									{/* Select All Option */}
									<CommandItem onSelect={handleSelectAll}>
										<Check
											className={cn(
												'mr-2 h-4 w-4',
												allSelected ? 'opacity-100' : 'opacity-0'
											)}
										/>
										{allSelected ? 'Deselect All' : 'Select All'}
									</CommandItem>

									{/* Actual Items */}
									{items.map((item) => {
										const isSelected = selectedItems.some(
											(i) => i._id === item._id
										);
										return (
											<CommandItem
												key={item._id}
												onSelect={() =>
													toggleSelection(item, selectedItems, setSelectedItems)
												}
											>
												<Check
													className={cn(
														'mr-2 h-4 w-4',
														isSelected ? 'opacity-100' : 'opacity-0'
													)}
												/>
												{item.name}
											</CommandItem>
										);
									})}
								</CommandGroup>
							</CommandList>
						</Command>
					</PopoverContent>
				</Popover>
			</div>
		);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[600px]">
				<DialogHeader>
					<DialogTitle>Assign Shift to Employees</DialogTitle>
					<DialogDescription>
						Select multiple business units, departments, and designations to
						filter employees for assigning shift: &quot;{shift?.name}&quot;.
					</DialogDescription>
				</DialogHeader>

				<div className="grid gap-4 py-4">
					{renderMultiSelect(
						'Business Units',
						businessUnits,
						selectedBusinessUnits,
						setSelectedBusinessUnits
					)}
					{renderMultiSelect(
						'Departments',
						departments,
						selectedDepartments,
						setSelectedDepartments
					)}
					{renderMultiSelect(
						'Designations',
						designations,
						selectedDesignations,
						setSelectedDesignations
					)}
				</div>
				<div className="space-y-2">
					<div className="flex items-center justify-between">
						<Label>Employees</Label>
						<Button
							variant="outline"
							size="sm"
							onClick={handleSelectAllEmployees}
							className="text-xs"
						>
							{selectedEmployees.length === availableEmployees.length
								? 'Deselect All'
								: 'Select All'}
						</Button>
					</div>

					{isLoading ? (
						<div className="h-32 flex items-center justify-center border rounded-md">
							<SimpleLoader />
						</div>
					) : (
						<ScrollArea className="h-32 border rounded-md p-2">
							<div className="space-y-2">
								{availableEmployees.length === 0 ? (
									<p className="text-xs text-muted-foreground">
										No employees found for the selected filters.
									</p>
								) : (
									availableEmployees.map((employee) => (
										<div
											key={employee._id}
											className="flex items-center space-x-2"
										>
											<Checkbox
												id={employee._id}
												checked={selectedEmployees.some(
													(e) => e._id === employee._id
												)}
												onCheckedChange={() => handleEmployeeToggle(employee)}
											/>
											<label
												htmlFor={employee._id}
												className="text-sm font-medium leading-none"
											>
												{employee.name}
											</label>
											<span className="text-xs text-muted-foreground">
												({employee.email})
											</span>
										</div>
									))
								)}
							</div>
						</ScrollArea>
					)}

					<p className="text-xs text-muted-foreground">
						{selectedEmployees.length > 0
							? `${selectedEmployees.length} employee(s) selected`
							: `No employees selected`}
					</p>
				</div>

				<DialogFooter>
					<Button
						variant="outline"
						onClick={() => onOpenChange(false)}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button disabled={isSubmitting} onClick={handleSubmit}>
						Assign Shift
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
