'use client';

import * as React from 'react';
import {
	ArchiveX,
	File,
	MessageCircle,
	Send,
	Trash2,
	Plus,
	Search,
} from 'lucide-react';
import {
	SidebarContent,
	SidebarGroup,
	SidebarGroupContent,
	SidebarHeader,
	SidebarInput,
	useSidebar,
} from '@/components/ui/sidebar';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	getChats,
	createChat,
	setSelectedChat,
	getChatUsers,
} from '@/lib/features/chat/chatSlice';
import { fetchAllEmployees } from '@/lib/features/employees/employeeSlice';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import dayjs from 'dayjs';
import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';

const navItems = [
	{
		title: 'Chats',
		icon: MessageCircle,
		isActive: true,
	},
	{
		title: 'Drafts',
		icon: File,
		isActive: false,
	},
	{
		title: 'Sent',
		icon: Send,
		isActive: false,
	},
	{
		title: 'Archived',
		icon: ArchiveX,
		isActive: false,
	},
	{
		title: 'Trash',
		icon: Trash2,
		isActive: false,
	},
];

export function ChatsList({ ...props }) {
	const dispatch = useAppDispatch();
	const [status, setStatus] = React.useState('Available');
	const { chats, selectedChat } = useAppSelector((store) => store.chat);
	const { authenticatedUser: user } = useAppSelector((store) => store.auth);
	const { setOpen } = useSidebar();
	const [activeItem, setActiveItem] = React.useState(navItems[0]);
	const [searchQuery, setSearchQuery] = React.useState('');
	const [dialogOpen, setDialogOpen] = React.useState(false);

	React.useEffect(() => {
		dispatch(getChats());
	}, [dispatch]);

	// React.useEffect(() => {
	//   if (selectedChatUser) {
	//     console.log('selectedChatUser', selectedChatUser);
	//     selectedChatUser?.participants?.forEach((participant) => {
	//       if (participant?._id !== user?.userId) {
	//         console.log('participant', participant);
	//         setOtherUser(participant);
	//       }
	//     });
	//   }
	// }, [selectedChatUser, user?.userId]);

	const handleStatusChange = (newStatus) => {
		setStatus(newStatus);
		console.log(`Status changed to: ${newStatus}`);
	};

	// Filter chats based on search query
	const filteredChats = React.useMemo(() => {
		if (!searchQuery.trim()) return chats;

		return chats.filter((chat) => {
			const otherUser = chat?.participants?.find(
				(p) => p.userId !== user?.userId
			);
			return otherUser?.name.toLowerCase().includes(searchQuery.toLowerCase());
		});
	}, [chats, searchQuery, user]);

	return (
		<>
			<SidebarHeader className="p-2">
				<div className="flex items-center gap-2">
					<div className="relative flex-1">
						<SidebarInput
							placeholder="Search chats..."
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
						/>
						<Search className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
					</div>
					<Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
						<DialogTrigger asChild>
							<Button variant="outline" size="icon" className="h-8 w-8">
								<Plus className="h-4 w-4" />
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>New Chat</DialogTitle>
							</DialogHeader>
							<EmployeesList
								onSelectEmployee={async (employee) => {
									console.log('Creating chat with:', employee);
									const res = await dispatch(
										createChat({ users: [employee?.id] })
									);
									setDialogOpen(false);
									const newChat = res.payload.data;
									console.log('New chat created:', newChat);
									dispatch(setSelectedChat(newChat?.id));
								}}
							/>
						</DialogContent>
					</Dialog>
				</div>
			</SidebarHeader>
			<SidebarContent>
				<SidebarGroup className="px-0 w-full">
					<SidebarGroupContent>
						{filteredChats.map((chat) => {
							const otherUser = chat?.participants?.find(
								(p) => p.userId !== user?.userId
							);

							return (
								<div
									key={chat._id}
									className={`flex items-center gap-1 whitespace-nowrap border-b p-2 text-sm leading-tight last:border-b-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground ${
										chat._id === selectedChat ? 'bg-sidebar-accent' : ''
									}`}
									onClick={() => dispatch(setSelectedChat(chat._id))}
								>
									<div className="relative">
										<Avatar className="h-9 w-9">
											<AvatarImage
												src={otherUser?.profilePhoto || '/placeholder.svg'}
												alt={otherUser?.name}
											/>
											<AvatarFallback>
												{otherUser?.name?.slice(0, 2).toUpperCase()}
											</AvatarFallback>
										</Avatar>
										<span
											className={`absolute top-0 right-0 h-3 w-3 rounded-full border-2 border-white ${
												otherUser?.isOnline ? 'bg-green-500' : 'bg-gray-300'
											}`}
										/>
									</div>
									<div className="flex flex-col flex-grow min-w-0">
										<div className="flex items-center justify-between w-full">
											<span className="font-semibold truncate text-xs">
												{otherUser?.name}
											</span>
											<span className="text-xs text-muted-foreground">
												{dayjs(chat?.lastMessage?.createdAt).format('h:mm a')}
											</span>
										</div>
										<span className="line-clamp-1 text-xs text-muted-foreground">
											{chat.lastMessage
												? chat.lastMessage.content
												: 'No messages yet'}
										</span>
									</div>
								</div>
							);
						})}
					</SidebarGroupContent>
				</SidebarGroup>
			</SidebarContent>
		</>
	);
}

function EmployeesList({ onSelectEmployee }) {
	const dispatch = useAppDispatch();
	const [searchQuery, setSearchQuery] = React.useState('');
	// const [employees, setEmployees] = React.useState([])
	const [loading, setLoading] = React.useState(true);
	const {
		chatList: data,
		chatUsers: employees,
		isLoading: employeesLoading,
	} = useAppSelector((store) => store.chat);

	React.useEffect(() => {
		dispatch(getChatUsers());
	}, [dispatch]);

	React.useEffect(() => {
		setLoading(employeesLoading);
	}, [employeesLoading]);

	// Mock data - in a real app, you would fetch this from an API
	// React.useEffect(() => {
	//   // Simulate API call
	//   // setTimeout(() => {
	//   //   setEmployees([
	//   //     { id: 1, name: "John Doe", position: "Software Engineer", profilePhoto: "/stylized-jd-initials.png" },
	//   //     { id: 2, name: "Jane Smith", position: "Product Manager", profilePhoto: "/javascript-code.png" },
	//   //     { id: 3, name: "Robert Johnson", position: "UX Designer", profilePhoto: "/abstract-rj.png" },
	//   //     { id: 4, name: "Emily Davis", position: "Marketing Specialist", profilePhoto: "/ed-initials-abstract.png" },
	//   //     { id: 5, name: "Michael Wilson", position: "Data Analyst", profilePhoto: "/intertwined-letters.png" },
	//   //   ])
	//   //   setLoading(false)
	//   // }, 500)
	// }, [])

	// Filter employees based on search query
	const filteredEmployees = React.useMemo(() => {
		let formattedEmployees = employees.map((emp) => ({
			id: emp?._id,
			name: emp?.name,
			// position: emp?.designation?.name,
			profilePhoto: emp?.profilePhoto,
			email: emp?.email,
		}));

		if (!searchQuery.trim()) return formattedEmployees;

		let filteredChats = formattedEmployees.filter((employee) =>
			employee?.name?.toLowerCase().includes(searchQuery.toLowerCase())
		);

		return filteredChats;
	}, [employees, searchQuery]);

	return (
		<div className="space-y-4">
			<div className="relative">
				<Input
					placeholder="Search employees..."
					value={searchQuery}
					onChange={(e) => setSearchQuery(e.target.value)}
					className="pl-9"
				/>
				<Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
			</div>

			{loading ? (
				<div className="space-y-2">
					{[1, 2, 3].map((i) => (
						<div key={i} className="flex items-center gap-3 p-2 animate-pulse">
							<div className="h-10 w-10 rounded-full bg-muted"></div>
							<div className="space-y-2 flex-1">
								<div className="h-4 w-24 bg-muted rounded"></div>
								<div className="h-3 w-32 bg-muted rounded"></div>
							</div>
						</div>
					))}
				</div>
			) : (
				<div className="max-h-[300px] overflow-y-auto space-y-1">
					{filteredEmployees.length === 0 ? (
						<p className="text-center text-muted-foreground py-4">
							No employees found
						</p>
					) : (
						filteredEmployees.map((employee) => (
							<div
								key={employee.id}
								className="flex items-center gap-3 p-2 rounded-md hover:bg-accent cursor-pointer"
								onClick={() => onSelectEmployee(employee)}
							>
								<Avatar className="h-10 w-10">
									<AvatarImage
										src={employee?.profilePhoto || '/placeholder.svg'}
										alt={employee.name}
									/>
									<AvatarFallback>
										{employee?.name?.slice(0, 2).toUpperCase()}
									</AvatarFallback>
								</Avatar>
								<div>
									<p className="font-medium">{employee.name}</p>
									<p className="text-xs text-muted-foreground">
										{employee.email}
									</p>
								</div>
							</div>
						))
					)}
				</div>
			)}
		</div>
	);
}
