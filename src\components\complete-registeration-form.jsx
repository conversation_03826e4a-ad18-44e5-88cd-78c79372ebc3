'use client';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from './ui/form';
import Link from 'next/link';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import {
	registerEmailSchema,
	registerSchema,
} from '@/lib/schemas/authenticationSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import {
	registerEmail,
	registerUser,
	storeEmailInState,
} from '@/lib/features/auth/authSlice';
import { useState } from 'react';
import { LoadingSubmitButton } from './loading-component';

export default function CompleteRegistrationForm({ className, ...props }) {
	const [showPassword, setShowPassword] = useState(false);

	const form = useForm({
		resolver: zodResolver(registerSchema),
		defaultValues: {
			password: '',
			confirmPassword: '',
		},
	});
	const { isLoading, authenticatedUser, userEmail } = useAppSelector(
		(store) => store.auth
	);
	const dispatch = useAppDispatch();
	const onSubmit = (data) => {
		dispatch(
			registerUser({
				password: data.password,
				email: authenticatedUser ? authenticatedUser.email : userEmail,
			})
		);
	};
	return (
		<Form>
			<Form {...form}>
				<form
					className={cn('flex flex-col gap-6', className)}
					{...props}
					onSubmit={form.handleSubmit(onSubmit)}
				>
					<div className="flex flex-col items-center gap-2 text-center">
						<h1 className="text-2xl font-bold">Enter Password</h1>
						<p className="text-balance text-sm text-muted-foreground">
							Enter password to secure up your account
						</p>
					</div>
					<div className="grid gap-4">
						<FormField
							control={form.control}
							name="password"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Password</FormLabel>
									<FormControl>
										<div className="relative w-full">
											<Input
												{...field}
												placeholder="Password"
												type={showPassword ? 'text' : 'password'}
											/>
											<Button
												type="button"
												variant="icon"
												onClick={() => setShowPassword((prev) => !prev)}
												className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500"
											>
												{showPassword ? (
													<EyeOff size={20} />
												) : (
													<Eye size={20} />
												)}
											</Button>
										</div>
									</FormControl>

									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="confirmPassword"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Confirm password</FormLabel>
									<FormControl>
										<Input
											{...field}
											type="password"
											autoComplete="confirmPassword"
											autoFocus
											placeholder="Confirm password"
										/>
									</FormControl>
									<FormDescription>
										Password should contain at least 8 characters, one uppercase
										letter, one lowercase letter and one number
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<LoadingSubmitButton
							isLoading={isLoading}
							buttonLoadingText={'Setting Password...'}
							buttonText={'Set Password'}
						/>
					</div>
				</form>
			</Form>
		</Form>
	);
}
