import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import {
	Edit,
	Loader2,
	Save,
	X,
	Shield,
	Calendar,
	Clock,
	Gift,
	Users,
	MapPin,
	Pointer,
	Trash2,
	Undo2,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateEmployeeDetailsBenefit } from '@/lib/features/employees/updateEmployeeSlice';
import * as z from 'zod';
import { icons } from '@/data/icons';

// Benefits schema
const benefitsDetailsSchema = z.object({
	eligibleForOffInLieu: z.boolean().default(false),
	holidayGroups: z
		.array(z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format'))
		.optional(),
	leaveGroups: z
		.array(z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format'))
		.optional(),
});

const EditEmployeeBenefitDetailsForm = ({ employeeId, benefits }) => {
	const dispatch = useAppDispatch();
	const { isLoading } = useAppSelector((store) => store.updateEmployee);

	const [isEditing, setIsEditing] = useState(false);
	const [originalFormValues, setOriginalFormValues] = useState(null);
	console.log(benefits);
	const form = useForm({
		resolver: zodResolver(benefitsDetailsSchema),
		defaultValues: {
			eligibleForOffInLieu: benefits?.eligibleForOffInLieu || false,
			holidayGroups: benefits?.holidayGroups?.map((group) => group._id) || [],
			leaveGroups: benefits?.leaveGroups?.map((group) => group._id) || [],
		},
	});

	// Update form when benefits prop changes
	React.useEffect(() => {
		if (benefits) {
			form.reset({
				eligibleForOffInLieu: benefits.eligibleForOffInLieu || false,
				holidayGroups: benefits.holidayGroups?.map((group) => group._id) || [],
				leaveGroups: benefits.leaveGroups?.map((group) => group._id) || [],
			});
		}
	}, [benefits, form]);

	const onSubmit = async (data) => {
		console.log('Benefits form data to submit:', data);

		const result = await dispatch(
			updateEmployeeDetailsBenefit({
				employeeId,
				...data,
			})
		);

		if (updateEmployeeDetailsBenefit.fulfilled.match(result)) {
			setIsEditing(false);
		}
	};

	const formatDate = (dateString) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			weekday: 'short',
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	const getLeaveProgressColor = (percentage) => {
		if (percentage >= 80) return 'bg-red-500';
		if (percentage >= 60) return 'bg-yellow-500';
		return 'bg-green-500';
	};

	// Delete handlers for removing groups from form
	const handleRemoveHolidayGroup = (groupId) => {
		const currentHolidayGroups = form.getValues('holidayGroups') || [];
		const updatedHolidayGroups = currentHolidayGroups.filter(
			(id) => id !== groupId
		);
		form.setValue('holidayGroups', updatedHolidayGroups, { shouldDirty: true });
	};

	const handleRemoveLeaveGroup = (groupId) => {
		const currentLeaveGroups = form.getValues('leaveGroups') || [];
		const updatedLeaveGroups = currentLeaveGroups.filter(
			(id) => id !== groupId
		);
		form.setValue('leaveGroups', updatedLeaveGroups, { shouldDirty: true });
	};

	// Restore handlers for adding groups back to form
	const handleRestoreHolidayGroup = (groupId) => {
		const currentHolidayGroups = form.getValues('holidayGroups') || [];
		if (!currentHolidayGroups.includes(groupId)) {
			form.setValue('holidayGroups', [...currentHolidayGroups, groupId], {
				shouldDirty: true,
			});
		}
	};

	const handleRestoreLeaveGroup = (groupId) => {
		const currentLeaveGroups = form.getValues('leaveGroups') || [];
		if (!currentLeaveGroups.includes(groupId)) {
			form.setValue('leaveGroups', [...currentLeaveGroups, groupId], {
				shouldDirty: true,
			});
		}
	};

	// Watch form values to make UI reactive to changes
	const watchedHolidayGroups = form.watch('holidayGroups') || [];
	const watchedLeaveGroups = form.watch('leaveGroups') || [];

	// Helper functions to check if a group is marked for deletion
	const isHolidayGroupMarkedForDeletion = (groupId) => {
		return !watchedHolidayGroups.includes(groupId);
	};

	const isLeaveGroupMarkedForDeletion = (groupId) => {
		return !watchedLeaveGroups.includes(groupId);
	};

	return (
		<>
			{/* Edit Controls */}
			<div className="flex justify-end mb-4">
				<div className="flex gap-2">
					{isEditing && (
						<Button
							className="bg-red-600 text-white"
							variant="outline"
							onClick={() => {
								// Reset form to original values
								if (originalFormValues) {
									form.reset(originalFormValues);
								}
								setIsEditing(false);
							}}
							disabled={isLoading}
						>
							<X className="h-4 w-4 mr-2" size={16} />
							Cancel
						</Button>
					)}
					<Button
						variant="default"
						onClick={() => {
							if (isEditing) {
								form.handleSubmit(onSubmit)();
							} else {
								// Save original form values before entering edit mode
								const currentValues = form.getValues();
								setOriginalFormValues(currentValues);
								setIsEditing(true);
							}
						}}
						disabled={isLoading}
					>
						{isLoading ? (
							<Loader2 className="animate-spin mr-2" size={16} />
						) : isEditing ? (
							<Save className="h-4 w-4 mr-2" size={16} />
						) : (
							<Edit className="h-4 w-4 mr-2" size={16} />
						)}
						{isEditing ? 'Save' : 'Edit'}
					</Button>
				</div>
			</div>

			<div className="grid grid-cols-1 gap-6">
				{/* Benefits Configuration Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<Shield className="h-5 w-5" />
							Benefits Configuration
						</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								<div className="space-y-6">
									<FormField
										control={form.control}
										name="eligibleForOffInLieu"
										render={({ field }) => (
											<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
												<div className="space-y-0.5">
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Eligible for Off in Lieu
													</FormLabel>
													<FormDescription>
														Allow time off in lieu of overtime compensation
													</FormDescription>
												</div>
												<FormControl>
													<Switch
														checked={field.value}
														onCheckedChange={field.onChange}
													/>
												</FormControl>
											</FormItem>
										)}
									/>
								</div>
							</Form>
						) : (
							<div className="space-y-4">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Eligible for Off in Lieu
									</p>
									<p className="text-sm">
										{benefits?.eligibleForOffInLieu ? 'Yes' : 'No'}
									</p>
								</div>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Holiday Groups Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<Calendar className="h-5 w-5" />
							Holiday Groups
						</CardTitle>
					</CardHeader>
					<CardContent>
						{!benefits?.holidayGroups || benefits.holidayGroups.length === 0 ? (
							<div className="text-center py-8 text-muted-foreground">
								<Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
								<p className="text-sm">No holiday groups assigned</p>
								{isEditing && (
									<p className="text-xs mt-1">
										Contact HR to assign holiday groups
									</p>
								)}
							</div>
						) : (
							<Accordion type="single" collapsible className="w-full">
								{benefits.holidayGroups.map((group) => {
									const groupId = group._id || group.id;
									const isMarkedForDeletion =
										isEditing && isHolidayGroupMarkedForDeletion(groupId);

									return (
										<AccordionItem
											key={groupId}
											value={`holiday-${groupId}`}
											className={cn(isMarkedForDeletion && 'opacity-50')}
										>
											<AccordionTrigger className="hover:no-underline">
												<div className="flex items-center justify-between w-full mr-4">
													<div className="flex items-center gap-3">
														<div className="flex items-center gap-2">
															<Gift
																className={cn(
																	'h-4 w-4 text-primary',
																	isMarkedForDeletion && 'text-muted-foreground'
																)}
															/>
															<span
																className={cn(
																	'font-medium',
																	isMarkedForDeletion &&
																		'line-through text-muted-foreground'
																)}
															>
																{group.name}
															</span>
														</div>
														{group.holidays && (
															<Badge variant="outline" className="text-xs">
																{group.holidays.length} holidays
															</Badge>
														)}
														{isMarkedForDeletion && (
															<Badge variant="destructive" className="text-xs">
																Will be removed
															</Badge>
														)}
														{isEditing && (
															<Button
																variant="ghost"
																size="sm"
																className={cn(
																	'h-6 w-6 p-0',
																	isMarkedForDeletion
																		? 'text-green-600 hover:text-green-600 hover:bg-green-50'
																		: 'text-destructive hover:text-destructive hover:bg-destructive/10'
																)}
																onClick={(e) => {
																	e.stopPropagation();
																	if (isMarkedForDeletion) {
																		handleRestoreHolidayGroup(groupId);
																	} else {
																		handleRemoveHolidayGroup(groupId);
																	}
																}}
																title={
																	isMarkedForDeletion
																		? 'Restore group'
																		: 'Remove group'
																}
															>
																{isMarkedForDeletion ? (
																	<Undo2 className="h-4 w-4" />
																) : (
																	<Trash2 className="h-4 w-4" />
																)}
															</Button>
														)}
													</div>
													{group.description && (
														<p className="text-sm text-muted-foreground text-left">
															{group.description}
														</p>
													)}
												</div>
											</AccordionTrigger>
											<AccordionContent>
												<div className="space-y-3 pt-2">
													{group.holidays && group.holidays.length > 0 ? (
														<div className="grid gap-3">
															{group.holidays.map((holiday) => {
																const holidayIcon = icons.find(
																	(icon) => icon.value === holiday.icon
																);
																return (
																	<div
																		key={holiday._id || holiday.id}
																		className="flex items-center justify-between p-3 rounded-lg border bg-card"
																	>
																		<div className="flex items-center gap-3">
																			{holidayIcon ? (
																				<holidayIcon.icon className="h-4 w-4" />
																			) : (
																				<Pointer className="h-4 w-4" />
																			)}
																			<div>
																				<p className="font-medium text-sm">
																					{holiday.title || holiday.name}
																				</p>
																				<p className="text-xs text-muted-foreground">
																					{formatDate(
																						holiday.startDate || holiday.date
																					)}
																					{holiday.endDate &&
																						holiday.endDate !==
																							holiday.startDate && (
																							<>
																								{' '}
																								- {formatDate(holiday.endDate)}
																							</>
																						)}
																				</p>
																			</div>
																		</div>
																		<div className="flex items-center gap-2">
																			<Badge
																				variant="secondary"
																				className="text-xs bg-muted"
																			>
																				{holiday.numberOfDays}{' '}
																				{holiday.numberOfDays === 1
																					? 'day'
																					: 'days'}
																			</Badge>
																		</div>
																	</div>
																);
															})}
														</div>
													) : (
														<div className="text-center py-4 text-muted-foreground">
															<p className="text-sm">
																No holidays defined for this group
															</p>
														</div>
													)}
												</div>
											</AccordionContent>
										</AccordionItem>
									);
								})}
							</Accordion>
						)}
					</CardContent>
				</Card>

				{/* Leave Groups Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<Clock className="h-5 w-5" />
							Leave Groups
						</CardTitle>
					</CardHeader>
					<CardContent>
						{!benefits?.leaveGroups || benefits.leaveGroups.length === 0 ? (
							<div className="text-center py-8 text-muted-foreground">
								<Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
								<p className="text-sm">No leave groups assigned</p>
								{isEditing && (
									<p className="text-xs mt-1">
										Contact HR to assign leave groups
									</p>
								)}
							</div>
						) : (
							<Accordion type="single" collapsible className="w-full">
								{benefits.leaveGroups.map((group) => {
									const groupId = group._id || group.id;
									const isMarkedForDeletion =
										isEditing && isLeaveGroupMarkedForDeletion(groupId);

									return (
										<AccordionItem
											key={groupId}
											value={`leave-${groupId}`}
											className={cn(isMarkedForDeletion && 'opacity-50')}
										>
											<AccordionTrigger className="hover:no-underline">
												<div className="flex items-center justify-between w-full mr-4">
													<div className="flex items-center gap-3">
														<div className="flex items-center gap-2">
															<Users
																className={cn(
																	'h-4 w-4 text-primary',
																	isMarkedForDeletion && 'text-muted-foreground'
																)}
															/>
															<span
																className={cn(
																	'font-medium',
																	isMarkedForDeletion &&
																		'line-through text-muted-foreground'
																)}
															>
																{group.name}
															</span>
														</div>
														<div className="flex items-center gap-2">
															{group.totalDays && (
																<Badge variant="outline" className="text-xs">
																	{group.remainingDays || group.totalDays}/
																	{group.totalDays} days
																	{group.remainingDays ? ' left' : ' total'}
																</Badge>
															)}
															{isMarkedForDeletion && (
																<Badge
																	variant="destructive"
																	className="text-xs"
																>
																	Will be removed
																</Badge>
															)}
															{group.totalDays &&
																group.usedDays !== undefined && (
																	<div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
																		<div
																			className={cn(
																				'h-full transition-all duration-300',
																				getLeaveProgressColor(
																					(group.usedDays / group.totalDays) *
																						100
																				)
																			)}
																			style={{
																				width: `${Math.min((group.usedDays / group.totalDays) * 100, 100)}%`,
																			}}
																		/>
																	</div>
																)}
															{isEditing && (
																<Button
																	variant="ghost"
																	size="sm"
																	className={cn(
																		'h-6 w-6 p-0',
																		isMarkedForDeletion
																			? 'text-green-600 hover:text-green-600 hover:bg-green-50'
																			: 'text-destructive hover:text-destructive hover:bg-destructive/10'
																	)}
																	onClick={(e) => {
																		e.stopPropagation();
																		if (isMarkedForDeletion) {
																			handleRestoreLeaveGroup(groupId);
																		} else {
																			handleRemoveLeaveGroup(groupId);
																		}
																	}}
																	title={
																		isMarkedForDeletion
																			? 'Restore group'
																			: 'Remove group'
																	}
																>
																	{isMarkedForDeletion ? (
																		<Undo2 className="h-4 w-4" />
																	) : (
																		<Trash2 className="h-4 w-4" />
																	)}
																</Button>
															)}
														</div>
													</div>
													{group.description && (
														<p className="text-sm text-muted-foreground text-left">
															{group.description}
														</p>
													)}
												</div>
											</AccordionTrigger>
											<AccordionContent>
												<div className="space-y-3 pt-2">
													{group.leaveTypes && group.leaveTypes.length > 0 ? (
														<div className="grid gap-3">
															{group.leaveTypes.map((leaveType) => (
																<div
																	key={leaveType._id || leaveType.id}
																	className="flex items-center justify-between p-3 rounded-lg border bg-card"
																>
																	<div className="flex items-center gap-3">
																		<MapPin className="h-4 w-4 text-muted-foreground" />
																		<div>
																			<p className="font-medium text-sm">
																				{leaveType.name || leaveType.type}
																			</p>
																			<p className="text-xs text-muted-foreground">
																				{leaveType.usedDays || 0} of{' '}
																				{leaveType.allowedDays ||
																					leaveType.totalDays ||
																					0}{' '}
																				days used
																			</p>
																		</div>
																	</div>
																	<div className="flex items-center gap-2">
																		{leaveType.allowedDays && (
																			<div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
																				<div
																					className={cn(
																						'h-full transition-all duration-300',
																						getLeaveProgressColor(
																							((leaveType.usedDays || 0) /
																								leaveType.allowedDays) *
																								100
																						)
																					)}
																					style={{
																						width: `${Math.min(((leaveType.usedDays || 0) / leaveType.allowedDays) * 100, 100)}%`,
																					}}
																				/>
																			</div>
																		)}
																		{leaveType.carryForward && (
																			<Badge
																				variant="outline"
																				className="text-xs"
																			>
																				Carry Forward
																			</Badge>
																		)}
																	</div>
																</div>
															))}
														</div>
													) : (
														<div className="text-center py-4 text-muted-foreground">
															<p className="text-sm">
																No leave types defined for this group
															</p>
														</div>
													)}
												</div>
											</AccordionContent>
										</AccordionItem>
									);
								})}
							</Accordion>
						)}
					</CardContent>
				</Card>
			</div>
		</>
	);
};

export default EditEmployeeBenefitDetailsForm;
