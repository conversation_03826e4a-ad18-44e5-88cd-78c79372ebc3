import React, { useEffect, useState, useCallback } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from '@/components/ui/command';
import {
	Check,
	ChevronsUpDown,
	Edit,
	Loader2,
	Save,
	X,
	DollarSign,
	CreditCard,
	Calculator,
} from 'lucide-react';
import { cn, weeksInMonth } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateEmployeeDetailsEarnings } from '@/lib/features/employees/updateEmployeeSlice';
import { fetchCurrencies } from '@/lib/features/location/locationSlice';
import { earningsDetailsSchema } from '@/lib/schemas/employeeRegistrationSchema';

// Rate field types
const RATE_FIELDS = {
	MONTHLY: 'monthly',
	HOURLY: 'hourly',
	DAILY: 'daily',
	WEEKLY: 'weekly',
	YEARLY: 'yearly',
};

const EditEmployeeEarningDetailsForm = ({ employeeId, earnings }) => {
	const dispatch = useAppDispatch();
	const { isLoading } = useAppSelector((store) => store.employee);
	const { currencies } = useAppSelector((store) => store.location);
	const { companyData } = useAppSelector((store) => store.companyDetails);
	const companyDetails = companyData?.companyDetails;

	const [isEditing, setIsEditing] = useState(false);
	const [originalFormValues, setOriginalFormValues] = useState(null);
	const [lastModifiedField, setLastModifiedField] = useState(
		RATE_FIELDS.MONTHLY
	);

	const form = useForm({
		resolver: zodResolver(earningsDetailsSchema),
		defaultValues: {
			basicPay: {
				currency: earnings?.basicPay?.currency || '',
				amount: earnings?.basicPay?.amount || 0,
			},
			paymentMode: earnings?.paymentMode || 'bank',
			frequency: earnings?.frequency || 'monthly',
			payBasis: earnings?.payBasis || 'monthly',
			dailyRate: earnings?.dailyRate || 0,
			hourlyRate: earnings?.hourlyRate || 0,
			weeklyRate: earnings?.weeklyRate || 0,
			yearlyRate: earnings?.yearlyRate || 0,
			overtimeRate: earnings?.overtimeRate || 0,
			isSalaryAdvanceEligible: earnings?.isSalaryAdvanceEligible || false,
			salaryAdvance: earnings?.salaryAdvance || 0,
			bankName: earnings?.bankName || '',
			accountNumber: earnings?.accountNumber || '',
			accountHolderName: earnings?.accountHolderName || '',
			bankCode: earnings?.bankCode || '',
			swiftBIC: earnings?.swiftBIC || '',
			branchCode: earnings?.branchCode || '',
		},
	});

	const watchFields = useWatch({
		control: form.control,
		name: [
			'basicPay.amount',
			'hourlyRate',
			'dailyRate',
			'weeklyRate',
			'yearlyRate',
			'isSalaryAdvanceEligible',
			'paymentMode',
		],
	});

	const [
		basicPayAmount,
		hourlyRate,
		dailyRate,
		weeklyRate,
		yearlyRate,
		isSalaryAdvanceEligible,
		paymentMode,
	] = watchFields;

	// Fetch currencies if needed
	useEffect(() => {
		if (currencies.length === 0) {
			dispatch(fetchCurrencies());
		}
	}, [currencies.length, dispatch]);

	// Parse number input and update form
	const parseNumberInput = useCallback(
		(field, value) => {
			const numValue = Number.parseFloat(value);
			if (!isNaN(numValue)) {
				form.setValue(field, numValue, { shouldValidate: true });
			}
		},
		[form]
	);

	// Handle rate field change
	const handleRateChange = useCallback(
		(field, value) => {
			parseNumberInput(field, value);
			setLastModifiedField(
				field === 'basicPay.amount'
					? RATE_FIELDS.MONTHLY
					: field.replace('Rate', '')
			);
		},
		[parseNumberInput]
	);

	// Calculate rates based on the last modified field
	const calculateRates = useCallback((amount, basis, companyDetails) => {
		if (!amount || !companyDetails) return null;

		const numAmount = Number(amount);
		const rates = {
			hourlyRate: 0,
			dailyRate: 0,
			weeklyRate: 0,
			monthlyRate: 0,
			yearlyRate: 0,
		};

		// Calculate based on the basis
		switch (basis) {
			case RATE_FIELDS.HOURLY:
				rates.hourlyRate = Number.parseFloat(numAmount.toFixed(2));
				rates.dailyRate = Number.parseFloat(
					(rates.hourlyRate * companyDetails.hourlySchedule.total).toFixed(2)
				);
				rates.monthlyRate = Number.parseFloat(
					(rates.dailyRate * companyDetails.dailySchedule.total).toFixed(2)
				);
				rates.weeklyRate = Number.parseFloat(
					(rates.monthlyRate / weeksInMonth).toFixed(2)
				);
				rates.yearlyRate = Number.parseFloat(
					(rates.monthlyRate * companyDetails.monthlySchedule.total).toFixed(2)
				);
				break;
			case RATE_FIELDS.DAILY:
				rates.dailyRate = Number.parseFloat(numAmount.toFixed(2));
				rates.hourlyRate = Number.parseFloat(
					(rates.dailyRate / companyDetails.hourlySchedule.total).toFixed(2)
				);
				rates.monthlyRate = Number.parseFloat(
					(rates.dailyRate * companyDetails.dailySchedule.total).toFixed(2)
				);
				rates.weeklyRate = Number.parseFloat(
					(rates.monthlyRate / weeksInMonth).toFixed(2)
				);
				rates.yearlyRate = Number.parseFloat(
					(rates.monthlyRate * companyDetails.monthlySchedule.total).toFixed(2)
				);
				break;
			case RATE_FIELDS.WEEKLY:
				rates.weeklyRate = Number.parseFloat(numAmount.toFixed(2));
				rates.monthlyRate = Number.parseFloat(
					(rates.weeklyRate * weeksInMonth).toFixed(2)
				);
				rates.dailyRate = Number.parseFloat(
					(rates.monthlyRate / companyDetails.dailySchedule.total).toFixed(2)
				);
				rates.hourlyRate = Number.parseFloat(
					(rates.dailyRate / companyDetails.hourlySchedule.total).toFixed(2)
				);
				rates.yearlyRate = Number.parseFloat(
					(rates.monthlyRate * companyDetails.monthlySchedule.total).toFixed(2)
				);
				break;
			case RATE_FIELDS.YEARLY:
				rates.yearlyRate = Number.parseFloat(numAmount.toFixed(2));
				rates.monthlyRate = Number.parseFloat(
					(rates.yearlyRate / companyDetails.monthlySchedule.total).toFixed(2)
				);
				rates.weeklyRate = Number.parseFloat(
					(rates.monthlyRate / weeksInMonth).toFixed(2)
				);
				rates.dailyRate = Number.parseFloat(
					(rates.monthlyRate / companyDetails.dailySchedule.total).toFixed(2)
				);
				rates.hourlyRate = Number.parseFloat(
					(rates.dailyRate / companyDetails.hourlySchedule.total).toFixed(2)
				);
				break;
			case RATE_FIELDS.MONTHLY:
			default:
				rates.monthlyRate = Number.parseFloat(numAmount.toFixed(2));
				rates.yearlyRate = Number.parseFloat(
					(rates.monthlyRate * companyDetails.monthlySchedule.total).toFixed(2)
				);
				rates.weeklyRate = Number.parseFloat(
					(rates.monthlyRate / weeksInMonth).toFixed(2)
				);
				rates.dailyRate = Number.parseFloat(
					(rates.monthlyRate / companyDetails.dailySchedule.total).toFixed(2)
				);
				rates.hourlyRate = Number.parseFloat(
					(rates.dailyRate / companyDetails.hourlySchedule.total).toFixed(2)
				);
				break;
		}

		return rates;
	}, []);

	// Update rates when a field changes
	useEffect(() => {
		if (!companyDetails || !isEditing) return;

		// Skip calculation if form is initializing
		if (
			Object.values(form.getValues()).every(
				(val) => val === 0 || val === undefined || val === ''
			)
		) {
			return;
		}

		// Get the value of the last modified field
		const fieldValues = {
			[RATE_FIELDS.MONTHLY]: basicPayAmount,
			[RATE_FIELDS.HOURLY]: hourlyRate,
			[RATE_FIELDS.DAILY]: dailyRate,
			[RATE_FIELDS.WEEKLY]: weeklyRate,
			[RATE_FIELDS.YEARLY]: yearlyRate,
		};

		const changedValue = fieldValues[lastModifiedField];
		const calculatedRates = calculateRates(
			changedValue,
			lastModifiedField,
			companyDetails
		);

		if (calculatedRates) {
			// Update all fields except the one that was modified
			if (lastModifiedField !== RATE_FIELDS.MONTHLY) {
				form.setValue(
					'basicPay.amount',
					Number.parseFloat(calculatedRates.monthlyRate.toFixed(2))
				);
			}
			if (lastModifiedField !== RATE_FIELDS.HOURLY) {
				form.setValue(
					'hourlyRate',
					Number.parseFloat(calculatedRates.hourlyRate)
				);
			}
			if (lastModifiedField !== RATE_FIELDS.DAILY) {
				form.setValue(
					'dailyRate',
					Number.parseFloat(calculatedRates.dailyRate)
				);
			}
			if (lastModifiedField !== RATE_FIELDS.WEEKLY) {
				form.setValue(
					'weeklyRate',
					Number.parseFloat(calculatedRates.weeklyRate)
				);
			}
			if (lastModifiedField !== RATE_FIELDS.YEARLY) {
				form.setValue(
					'yearlyRate',
					Number.parseFloat(calculatedRates.yearlyRate)
				);
			}
		}
	}, [
		basicPayAmount,
		hourlyRate,
		dailyRate,
		weeklyRate,
		yearlyRate,
		lastModifiedField,
		form,
		companyDetails,
		calculateRates,
		isEditing,
	]);

	const onSubmit = async (data) => {
		console.log('Earnings form data to submit:', data);

		const result = await dispatch(
			updateEmployeeDetailsEarnings({
				employeeId,
				...data,
			})
		);

		if (updateEmployeeDetailsEarnings.fulfilled.match(result)) {
			setIsEditing(false);
		}
	};

	return (
		<>
			{/* Edit Controls */}
			<div className="flex justify-end mb-4">
				<div className="flex gap-2">
					{isEditing && (
						<Button
							className="bg-red-600 text-white"
							variant="outline"
							onClick={() => {
								// Reset form to original values
								if (originalFormValues) {
									form.reset(originalFormValues);
								}
								setIsEditing(false);
							}}
							disabled={isLoading}
						>
							<X className="h-4 w-4 mr-2" size={16} />
							Cancel
						</Button>
					)}
					<Button
						variant="default"
						onClick={() => {
							if (isEditing) {
								form.handleSubmit(onSubmit)();
							} else {
								// Save original form values before entering edit mode
								const currentValues = form.getValues();
								setOriginalFormValues(currentValues);
								setIsEditing(true);
							}
						}}
						disabled={isLoading}
					>
						{isLoading ? (
							<Loader2 className="animate-spin mr-2" size={16} />
						) : isEditing ? (
							<Save className="h-4 w-4 mr-2" size={16} />
						) : (
							<Edit className="h-4 w-4 mr-2" size={16} />
						)}
						{isEditing ? 'Save' : 'Edit'}
					</Button>
				</div>
			</div>

			<div className="grid grid-cols-1 gap-6">
				{/* Compensation Details Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<DollarSign className="h-5 w-5" />
							Compensation Details
						</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
									<div className="space-y-4">
										<FormField
											control={form.control}
											name="basicPay.currency"
											render={({ field }) => {
												const currency = currencies?.find(
													(currency) => currency.currency === field.value
												);

												return (
													<FormItem>
														<FormLabel className="text-sm font-medium text-muted-foreground">
															Basic Pay Currency
														</FormLabel>
														<Popover>
															<PopoverTrigger asChild>
																<FormControl>
																	<Button
																		variant="outline"
																		role="combobox"
																		className={cn(
																			'w-full justify-between',
																			!field.value && 'text-muted-foreground'
																		)}
																	>
																		{currency
																			? `${currency.currency} - ${currency.currencyName}`
																			: 'Select currency'}
																		<ChevronsUpDown className="opacity-50" />
																	</Button>
																</FormControl>
															</PopoverTrigger>
															<PopoverContent className="w-full p-0">
																<Command>
																	<CommandInput
																		placeholder="Search Currency..."
																		className="h-9"
																	/>
																	<CommandList>
																		<CommandEmpty>
																			No Currency found.
																		</CommandEmpty>
																		<CommandGroup>
																			{currencies?.map((currency, index) => (
																				<CommandItem
																					value={currency.currency}
																					key={index}
																					onSelect={() => {
																						form.setValue(
																							'basicPay.currency',
																							currency.currency
																						);
																					}}
																				>
																					{`${currency.currency} - ${currency.currencyName}`}
																					<Check
																						className={cn(
																							'ml-auto',
																							currency.currency === field.value
																								? 'opacity-100'
																								: 'opacity-0'
																						)}
																					/>
																				</CommandItem>
																			))}
																		</CommandGroup>
																	</CommandList>
																</Command>
															</PopoverContent>
														</Popover>
														<FormMessage />
													</FormItem>
												);
											}}
										/>

										<FormField
											control={form.control}
											name="basicPay.amount"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Basic Pay Amount (Monthly)
													</FormLabel>
													<FormControl>
														<Input
															type="number"
															placeholder="Enter amount"
															value={field.value}
															onChange={(e) =>
																handleRateChange(
																	'basicPay.amount',
																	e.target.value
																)
															}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="payBasis"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Pay Basis
													</FormLabel>
													<Select
														onValueChange={field.onChange}
														defaultValue={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Select pay basis" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															<SelectItem value="hourly">Hourly</SelectItem>
															<SelectItem value="daily">Daily</SelectItem>
															<SelectItem value="weekly">Weekly</SelectItem>
															<SelectItem value="monthly">Monthly</SelectItem>
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>

									<div className="space-y-4">
										<FormField
											control={form.control}
											name="frequency"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Payment Frequency
													</FormLabel>
													<Select
														onValueChange={field.onChange}
														defaultValue={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Select payment frequency" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															<SelectItem value="daily">Daily</SelectItem>
															<SelectItem value="weekly">Weekly</SelectItem>
															<SelectItem value="monthly">Monthly</SelectItem>
															<SelectItem value="bi-monthly">
																Bi-Monthly
															</SelectItem>
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="overtimeRate"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Overtime Rate (Hourly)
													</FormLabel>
													<FormControl>
														<Input
															type="number"
															placeholder="Enter overtime rate"
															value={field.value}
															onChange={(e) =>
																parseNumberInput('overtimeRate', e.target.value)
															}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
								</div>
							</Form>
						) : (
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div className="space-y-4">
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Basic Pay Currency
										</p>
										<p className="text-sm">
											{earnings?.basicPay?.currency || 'Not specified'}
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Basic Pay Amount (Monthly)
										</p>
										<p className="text-sm">{earnings?.basicPay?.amount || 0}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Pay Basis
										</p>
										<p className="text-sm capitalize">
											{earnings?.payBasis || 'Not specified'}
										</p>
									</div>
								</div>
								<div className="space-y-4">
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Payment Frequency
										</p>
										<p className="text-sm capitalize">
											{earnings?.frequency || 'Not specified'}
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Overtime Rate (Hourly)
										</p>
										<p className="text-sm">{earnings?.overtimeRate || 0}</p>
									</div>
								</div>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Calculated Rates Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<Calculator className="h-5 w-5" />
							Calculated Rates
						</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
									<FormField
										control={form.control}
										name="hourlyRate"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Hourly Rate
												</FormLabel>
												<FormControl>
													<Input
														type="number"
														value={field.value}
														onChange={(e) =>
															handleRateChange('hourlyRate', e.target.value)
														}
													/>
												</FormControl>
												<FormDescription>
													{companyDetails?.hourlySchedule?.description}
												</FormDescription>
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="dailyRate"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Daily Rate
												</FormLabel>
												<FormControl>
													<Input
														type="number"
														value={field.value}
														onChange={(e) =>
															handleRateChange('dailyRate', e.target.value)
														}
													/>
												</FormControl>
												<FormDescription>
													{companyDetails?.dailySchedule?.description}
												</FormDescription>
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="weeklyRate"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Weekly Rate
												</FormLabel>
												<FormControl>
													<Input
														type="number"
														value={field.value}
														onChange={(e) =>
															handleRateChange('weeklyRate', e.target.value)
														}
													/>
												</FormControl>
												<FormDescription>
													Monthly rate divided by number of weeks
												</FormDescription>
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="yearlyRate"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Yearly Rate
												</FormLabel>
												<FormControl>
													<Input
														type="number"
														value={field.value}
														onChange={(e) =>
															handleRateChange('yearlyRate', e.target.value)
														}
													/>
												</FormControl>
												<FormDescription>
													{companyDetails?.monthlySchedule?.description}
												</FormDescription>
											</FormItem>
										)}
									/>
								</div>
							</Form>
						) : (
							<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Hourly Rate
									</p>
									<p className="text-sm">{earnings?.hourlyRate || 0}</p>
								</div>
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Daily Rate
									</p>
									<p className="text-sm">{earnings?.dailyRate || 0}</p>
								</div>
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Weekly Rate
									</p>
									<p className="text-sm">{earnings?.weeklyRate || 0}</p>
								</div>
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Yearly Rate
									</p>
									<p className="text-sm">{earnings?.yearlyRate || 0}</p>
								</div>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Payment Method Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<CreditCard className="h-5 w-5" />
							Payment Method
						</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								<div className="space-y-4">
									<FormField
										control={form.control}
										name="paymentMode"
										render={({ field }) => (
											<FormItem className="space-y-3">
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Payment Mode
												</FormLabel>
												<FormControl>
													<RadioGroup
														onValueChange={field.onChange}
														defaultValue={field.value}
														className="flex flex-col space-y-1"
													>
														<FormItem className="flex items-center space-x-3 space-y-0">
															<FormControl>
																<RadioGroupItem value="cash/cheque" />
															</FormControl>
															<FormLabel className="font-normal">
																Cash / Cheque
															</FormLabel>
														</FormItem>
														<FormItem className="flex items-center space-x-3 space-y-0">
															<FormControl>
																<RadioGroupItem value="bank" />
															</FormControl>
															<FormLabel className="font-normal">
																Bank
															</FormLabel>
														</FormItem>
													</RadioGroup>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									{form.watch('paymentMode') === 'bank' && (
										<div className="grid md:grid-cols-3 gap-6">
											<FormField
												control={form.control}
												name="bankName"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-sm font-medium text-muted-foreground">
															Bank Name
														</FormLabel>
														<FormControl>
															<Input {...field} placeholder="Bank Name" />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name="accountNumber"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-sm font-medium text-muted-foreground">
															Account Number
														</FormLabel>
														<FormControl>
															<Input {...field} placeholder="Account Number" />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name="branchCode"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-sm font-medium text-muted-foreground">
															Branch Code
														</FormLabel>
														<FormControl>
															<Input {...field} placeholder="Branch Code" />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name="bankCode"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-sm font-medium text-muted-foreground">
															Bank Code
														</FormLabel>
														<FormControl>
															<Input {...field} placeholder="Bank Code" />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name="swiftBIC"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-sm font-medium text-muted-foreground">
															Swift BIC
														</FormLabel>
														<FormControl>
															<Input {...field} placeholder="Swift BIC" />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name="accountHolderName"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-sm font-medium text-muted-foreground">
															Account Holder Name
														</FormLabel>
														<FormControl>
															<Input
																{...field}
																placeholder="Account Holder Name"
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
									)}
								</div>
							</Form>
						) : (
							<div className="space-y-4">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Payment Mode
									</p>
									<p className="text-sm capitalize">
										{earnings?.paymentMode || 'Not specified'}
									</p>
								</div>
								{earnings?.paymentMode === 'bank' && (
									<div className="grid md:grid-cols-3 gap-6">
										<div>
											<p className="text-sm font-medium text-muted-foreground">
												Bank Name
											</p>
											<p className="text-sm">
												{earnings?.bankName || 'Not specified'}
											</p>
										</div>
										<div>
											<p className="text-sm font-medium text-muted-foreground">
												Account Number
											</p>
											<p className="text-sm">
												{earnings?.accountNumber || 'Not specified'}
											</p>
										</div>
										<div>
											<p className="text-sm font-medium text-muted-foreground">
												Branch Code
											</p>
											<p className="text-sm">
												{earnings?.branchCode || 'Not specified'}
											</p>
										</div>
										<div>
											<p className="text-sm font-medium text-muted-foreground">
												Bank Code
											</p>
											<p className="text-sm">
												{earnings?.bankCode || 'Not specified'}
											</p>
										</div>
										<div>
											<p className="text-sm font-medium text-muted-foreground">
												Swift BIC
											</p>
											<p className="text-sm">
												{earnings?.swiftBIC || 'Not specified'}
											</p>
										</div>
										<div>
											<p className="text-sm font-medium text-muted-foreground">
												Account Holder Name
											</p>
											<p className="text-sm">
												{earnings?.accountHolderName || 'Not specified'}
											</p>
										</div>
									</div>
								)}
							</div>
						)}
					</CardContent>
				</Card>

				{/* Additional Compensation Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<DollarSign className="h-5 w-5" />
							Additional Compensation
						</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
									<FormField
										control={form.control}
										name="isSalaryAdvanceEligible"
										render={({ field }) => (
											<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
												<div className="space-y-0.5">
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Eligible for Salary Advance
													</FormLabel>
													<FormDescription>
														Check this to enable salary advance
													</FormDescription>
												</div>
												<FormControl>
													<Switch
														checked={field.value}
														onCheckedChange={field.onChange}
													/>
												</FormControl>
											</FormItem>
										)}
									/>
									{form.watch('isSalaryAdvanceEligible') && (
										<FormField
											control={form.control}
											name="salaryAdvance"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Salary Advance Amount
													</FormLabel>
													<FormControl>
														<Input
															type="number"
															placeholder="Enter salary advance amount"
															value={field.value}
															onChange={(e) =>
																parseNumberInput(
																	'salaryAdvance',
																	e.target.value
																)
															}
															disabled={!isSalaryAdvanceEligible}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									)}
								</div>
							</Form>
						) : (
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Eligible for Salary Advance
									</p>
									<p className="text-sm">
										{earnings?.isSalaryAdvanceEligible ? 'Yes' : 'No'}
									</p>
								</div>
								{earnings?.isSalaryAdvanceEligible && (
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Salary Advance Amount
										</p>
										<p className="text-sm">{earnings?.salaryAdvance || 0}</p>
									</div>
								)}
							</div>
						)}
					</CardContent>
				</Card>
			</div>
		</>
	);
};

export default EditEmployeeEarningDetailsForm;
