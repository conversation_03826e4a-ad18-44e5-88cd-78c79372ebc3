'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X, PlusCircle } from 'lucide-react';
import { useState } from 'react';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';

const benefitsSchema = z.object({
	holiday: z.object({
		eligibleForOffInLieu: z.boolean().default(false),
		assigned: z.array(z.string()).optional(),
	}),
	leave: z.object({
		eligibleForOffInLieu: z.boolean().default(false),
		code: z.string().optional(),
		type: z.string().optional(),
		eligibility: z.string().optional(),
		leaveIncrement: z
			.number()
			.min(0, 'Leave increment must be positive')
			.optional(),
		hourlyTimeOff: z.boolean().default(false),
		prorate: z.boolean().default(false),
	}),
	health: z.object({
		eligibleForOffInLieu: z.boolean().default(false),
		assigned: z.array(z.string()).optional(),
	}),
});

export function EmployeeBenefitsForm() {
	const [holidayInput, setHolidayInput] = useState('');
	const [healthInput, setHealthInput] = useState('');

	// Mock data for dropdowns
	const leaveTypes = [
		{ id: 'annual', name: 'Annual Leave' },
		{ id: 'sick', name: 'Sick Leave' },
		{ id: 'maternity', name: 'Maternity Leave' },
		{ id: 'paternity', name: 'Paternity Leave' },
		{ id: 'compassionate', name: 'Compassionate Leave' },
		{ id: 'unpaid', name: 'Unpaid Leave' },
	];

	const eligibilityOptions = [
		{ id: 'immediate', name: 'Immediate' },
		{ id: 'after_probation', name: 'After Probation' },
		{ id: 'after_3_months', name: 'After 3 Months' },
		{ id: 'after_6_months', name: 'After 6 Months' },
		{ id: 'after_1_year', name: 'After 1 Year' },
	];

	const form = useForm({
		resolver: zodResolver(benefitsSchema),
		defaultValues: {
			holiday: {
				eligibleForOffInLieu: false,
				assigned: [],
			},
			leave: {
				eligibleForOffInLieu: false,
				code: '',
				type: '',
				eligibility: '',
				leaveIncrement: 0,
				hourlyTimeOff: false,
				prorate: false,
			},
			health: {
				eligibleForOffInLieu: false,
				assigned: [],
			},
		},
	});

	function onSubmit(data) {
		console.log(data);
		// Here you would typically send the data to your server
		// For example: await fetch('/api/submit', { method: 'POST', body: JSON.stringify(data) });
	}

	// Helper functions for tag inputs
	const addHolidayBenefit = () => {
		if (holidayInput.trim().length >= 2) {
			const currentAssigned = form.getValues('holiday.assigned') || [];
			form.setValue('holiday.assigned', [
				...currentAssigned,
				holidayInput.trim(),
			]);
			setHolidayInput('');
		}
	};

	const removeHolidayBenefit = (index) => {
		const currentAssigned = form.getValues('holiday.assigned') || [];
		form.setValue(
			'holiday.assigned',
			currentAssigned.filter((_, i) => i !== index)
		);
	};

	const addHealthBenefit = () => {
		if (healthInput.trim().length >= 2) {
			const currentAssigned = form.getValues('health.assigned') || [];
			form.setValue('health.assigned', [
				...currentAssigned,
				healthInput.trim(),
			]);
			setHealthInput('');
		}
	};

	const removeHealthBenefit = (index) => {
		const currentAssigned = form.getValues('health.assigned') || [];
		form.setValue(
			'health.assigned',
			currentAssigned.filter((_, i) => i !== index)
		);
	};

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
				{/* Holiday Benefits Section */}
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Holiday Benefits</h3>
					<Separator />

					<Card>
						<CardContent className="pt-6">
							<div className="grid gap-4 md:grid-cols-2">
								<FormField
									control={form.control}
									name="holiday.eligibleForOffInLieu"
									render={({ field }) => (
										<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
											<div className="space-y-0.5">
												<FormLabel className="text-base">
													Eligible for Off in Lieu
												</FormLabel>
												<FormDescription>
													Employee is eligible for time off in lieu of holiday
													work
												</FormDescription>
											</div>
											<FormControl>
												<Switch
													checked={field.value}
													onCheckedChange={field.onChange}
												/>
											</FormControl>
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="holiday.assigned"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Assigned Holiday Benefits</FormLabel>
											<div className="flex space-x-2">
												<FormControl>
													<Input
														placeholder="Enter holiday benefit"
														value={holidayInput}
														onChange={(e) => setHolidayInput(e.target.value)}
														onKeyDown={(e) => {
															if (e.key === 'Enter') {
																e.preventDefault();
																addHolidayBenefit();
															}
														}}
													/>
												</FormControl>
												<Button
													type="button"
													onClick={addHolidayBenefit}
													disabled={holidayInput.trim().length < 2}
												>
													<PlusCircle className="h-4 w-4 mr-2" />
													Add
												</Button>
											</div>
											<div className="flex flex-wrap gap-2 mt-2">
												{(field.value || []).map((benefit, index) => (
													<Badge key={index} variant="secondary">
														{benefit}
														<Button
															type="button"
															variant="ghost"
															size="sm"
															className="ml-2 h-4 w-4 p-0"
															onClick={() => removeHolidayBenefit(index)}
														>
															<X className="h-3 w-3" />
														</Button>
													</Badge>
												))}
											</div>
											<FormDescription>
												Add specific holiday benefits assigned to this employee
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Leave Benefits Section */}
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Leave Benefits</h3>
					<Separator />

					<Card>
						<CardContent className="pt-6">
							<div className="space-y-4">
								<FormField
									control={form.control}
									name="leave.eligibleForOffInLieu"
									render={({ field }) => (
										<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
											<div className="space-y-0.5">
												<FormLabel className="text-base">
													Eligible for Off in Lieu
												</FormLabel>
												<FormDescription>
													Employee is eligible for time off in lieu of leave
												</FormDescription>
											</div>
											<FormControl>
												<Switch
													checked={field.value}
													onCheckedChange={field.onChange}
												/>
											</FormControl>
										</FormItem>
									)}
								/>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
									<FormField
										control={form.control}
										name="leave.code"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Leave Code</FormLabel>
												<FormControl>
													<Input placeholder="Enter leave code" {...field} />
												</FormControl>
												<FormDescription>
													Internal code for leave type
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="leave.type"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Leave Type</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value || ''}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select leave type" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{leaveTypes.map((type) => (
															<SelectItem key={type.id} value={type.id}>
																{type.name}
															</SelectItem>
														))}
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="leave.eligibility"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Eligibility</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value || ''}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select eligibility" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{eligibilityOptions.map((option) => (
															<SelectItem key={option.id} value={option.id}>
																{option.name}
															</SelectItem>
														))}
													</SelectContent>
												</Select>
												<FormDescription>
													When the employee becomes eligible for leave
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="leave.leaveIncrement"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Leave Increment</FormLabel>
												<FormControl>
													<Input
														type="number"
														placeholder="Enter leave increment"
														{...field}
														onChange={(e) => {
															const value = Number.parseFloat(e.target.value);
															field.onChange(isNaN(value) ? 0 : value);
														}}
													/>
												</FormControl>
												<FormDescription>
													This is for after one year of service
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
									<FormField
										control={form.control}
										name="leave.hourlyTimeOff"
										render={({ field }) => (
											<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
												<div className="space-y-0.5">
													<FormLabel className="text-base">
														Hourly Time Off
													</FormLabel>
													<FormDescription>
														Allow leave to be taken in hourly increments
													</FormDescription>
												</div>
												<FormControl>
													<Switch
														checked={field.value}
														onCheckedChange={field.onChange}
													/>
												</FormControl>
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="leave.prorate"
										render={({ field }) => (
											<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
												<div className="space-y-0.5">
													<FormLabel className="text-base">
														Prorate Leave
													</FormLabel>
													<FormDescription>
														Prorate leave based on employment duration
													</FormDescription>
												</div>
												<FormControl>
													<Switch
														checked={field.value}
														onCheckedChange={field.onChange}
													/>
												</FormControl>
											</FormItem>
										)}
									/>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Health Benefits Section */}
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Health Benefits</h3>
					<Separator />

					<Card>
						<CardContent className="pt-6">
							<div className="grid gap-4 md:grid-cols-2">
								<FormField
									control={form.control}
									name="health.eligibleForOffInLieu"
									render={({ field }) => (
										<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
											<div className="space-y-0.5">
												<FormLabel className="text-base">
													Eligible for Off in Lieu
												</FormLabel>
												<FormDescription>
													Employee is eligible for time off in lieu of health
													benefits
												</FormDescription>
											</div>
											<FormControl>
												<Switch
													checked={field.value}
													onCheckedChange={field.onChange}
												/>
											</FormControl>
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="health.assigned"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Assigned Health Benefits</FormLabel>
											<div className="flex space-x-2">
												<FormControl>
													<Input
														placeholder="Enter health benefit"
														value={healthInput}
														onChange={(e) => setHealthInput(e.target.value)}
														onKeyDown={(e) => {
															if (e.key === 'Enter') {
																e.preventDefault();
																addHealthBenefit();
															}
														}}
													/>
												</FormControl>
												<Button
													type="button"
													onClick={addHealthBenefit}
													disabled={healthInput.trim().length < 2}
												>
													<PlusCircle className="h-4 w-4 mr-2" />
													Add
												</Button>
											</div>
											<div className="flex flex-wrap gap-2 mt-2">
												{(field.value || []).map((benefit, index) => (
													<Badge key={index} variant="secondary">
														{benefit}
														<Button
															type="button"
															variant="ghost"
															size="sm"
															className="ml-2 h-4 w-4 p-0"
															onClick={() => removeHealthBenefit(index)}
														>
															<X className="h-3 w-3" />
														</Button>
													</Badge>
												))}
											</div>
											<FormDescription>
												Add specific health benefits assigned to this employee
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</CardContent>
					</Card>
				</div>

				<div className="flex justify-end space-x-4">
					<Button type="submit">Save and Complete</Button>
				</div>
			</form>
		</Form>
	);
}
