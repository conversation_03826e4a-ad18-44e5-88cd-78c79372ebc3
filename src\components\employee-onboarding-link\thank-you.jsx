import {
	Card,
	CardHeader,
	CardTitle,
	CardContent,
	CardDescription,
} from '@/components/ui/card';
import { CheckCircle } from 'lucide-react';

export function ThankYouPage() {
	return (
		<div className="min-h-screen flex items-center justify-center">
			<Card className="w-full max-w-md shadow-lg rounded-2xl">
				<CardHeader className="text-center">
					<div className="flex justify-center mb-2">
						<CheckCircle className="h-12 w-12 text-green-600" />
					</div>
					<CardTitle className="text-2xl font-semibold">Thank You!</CardTitle>
					<CardDescription>We appreciate your time</CardDescription>
				</CardHeader>
				<CardContent className="text-center space-y-4">
					<p>
						Thanks for filling out the form. We’ve received your submission and
						our team will review your changes shortly.
					</p>
					<p>
						You’ll be contacted via the email you provided if we need more
						details.
					</p>
				</CardContent>
			</Card>
		</div>
	);
}
