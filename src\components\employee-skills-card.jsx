import React from 'react';
import {
	<PERSON>,
	GraduationC<PERSON>,
	<PERSON>hake,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>,
	Briefcase,
} from 'lucide-react';

const MiniSkillBlock = ({
	className = '',
	icon = <Mountain />,
	color = 'bg-white',
	upper_title = '',
	lower_title = '',
	rotate = '',
	count = 0,
}) => {
	const titleStyles =
		'absolute text-xs sm:text-sm md:text-base whitespace-nowrap';

	const bgStyle = color ? { backgroundColor: `hsl(var(${color}))` } : {};
	return (
		<div
			className={`relative flex p-2 sm:p-3 md:p-4 gap-1 sm:gap-2 items-center justify-evenly ${className}`}
		>
			<div className="flex items-center justify-center relative">
				{upper_title && <div className={`${titleStyles}`}>{upper_title}</div>}
				<div
					style={bgStyle}
					className={`h-10 w-10 sm:h-14 sm:w-14 md:h-16 md:w-16 flex items-center justify-center rounded-full border-white shadow-lg border-2`}
				>
					{lower_title && <div className={`${titleStyles}`}>{lower_title}</div>}
					{React.cloneElement(icon, {
						className: `h-5 w-5 sm:h-6 sm:w-6 md:h-7 md:w-7 ${icon.props.className || ''}`,
					})}
				</div>
			</div>
			<div className="flex gap-1 sm:gap-2 items-center justify-center">
				<div
					className={`h-12 w-12 rounded-full border-white border-2 shadow-lg ${rotate} flex items-center justify-center font-semibold whitespace-nowrap dark:text-white`}
				>
					{count}
				</div>
				<div className="flex items-center">
					<div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-black rounded-full" />
					<div className="h-0.5 w-10 sm:w-14 md:w-16 bg-black" />
					<div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-black rounded-full" />
				</div>
			</div>
		</div>
	);
};

const EmployeeSkillsCard = ({
	hardSkills = 0,
	softSkills = 0,
	underGraduate = 0,
	postGraduate = 0,
	noFormalEducation = 0,
}) => {
	return (
		<div className="w-full h-auto flex justify-center items-center rounded-xl border bg-card text-card-foreground shadow">
			<div className="origin-top w-full">
				<div className="pt-16 flex flex-col items-center justify-end scale-[.85] md:scale-[.80] lg:scale-[.70] xl:scale-90">
					<div>
						<MiniSkillBlock
							className="rotate-90 -mb-6 sm:-mb-8 md:-mb-12 mt-5"
							color="--chart-1"
							icon={<GraduationCap className="-rotate-90" />}
							rotate="-rotate-90"
							count={underGraduate}
							upper_title={
								<div className="-rotate-90 pb-14 sm:pb-20 md:pb-24">
									Under graduate{' '}
								</div>
							}
						/>
					</div>

					<div className="flex items-center justify-center gap-10 pb-2">
						<MiniSkillBlock
							className="rotate-45"
							color="--chart-2"
							icon={<Handshake className="-rotate-45" />}
							rotate="-rotate-45"
							count={softSkills}
							upper_title={
								<div className="-rotate-45 pb-14 sm:pb-20 md:pb-24">
									Soft skills
								</div>
							}
						/>
						<MiniSkillBlock
							className="rotate-135"
							color="--chart-3"
							icon={<BookOpenCheck className="-rotate-135" />}
							rotate="-rotate-135"
							count={postGraduate}
							upper_title={
								<div className="-rotate-135 pb-14 sm:pb-20 md:pb-24">
									Post graduate{' '}
								</div>
							}
						/>
					</div>

					<div className="flex items-center justify-center pb-5">
						<MiniSkillBlock
							color="--chart-4"
							icon={<Brain />}
							count={hardSkills}
							lower_title={
								<div className="pt-14 sm:pt-20 md:pt-24">Hard Skills</div>
							}
						/>
						<div className="w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 flex items-center justify-center rounded-full bg-white border-4 border-white shadow-lg relative">
							<div className="absolute inset-[2px] rounded-full border-2 border-dashed border-gray-400"></div>
							<div className="text-center text-xs sm:text-sm lg:text-base font-semibold text-gray-800 leading-tight">
								Skills <br /> Inventory
							</div>
						</div>

						<MiniSkillBlock
							className="rotate-180"
							color="--chart-5"
							icon={<Briefcase className="rotate-180" />}
							rotate="rotate-180"
							count={noFormalEducation}
							lower_title={
								<div className="rotate-180 pt-16 sm:pt-24 md:pt-28 text-wrap leading-tight">
									Experience
								</div>
							}
						/>
					</div>
				</div>
			</div>
		</div>
	);
};

export default EmployeeSkillsCard;
