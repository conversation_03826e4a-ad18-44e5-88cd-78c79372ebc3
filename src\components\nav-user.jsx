'use client';

import {
	BadgeCheck,
	Bell,
	ChevronsUpDown,
	CreditCard,
	LogOut,
	Sparkles,
} from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuGroup,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	useSidebar,
} from '@/components/ui/sidebar';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { Button } from './ui/button';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';
import { logoutUser, updateLogout } from '@/lib/features/auth/authSlice';
import Link from 'next/link';
import { userRoles } from '@/lib/utils';

export function NavUser() {
	const { isMobile } = useSidebar();
	const dispatch = useAppDispatch();
	const { authenticatedUser: user } = useAppSelector((store) => store.auth);
	const { companyData } = useAppSelector((store) => store.companyDetails);
	const router = useRouter();

	const logout = useCallback(async () => {
		const dispatchResult = await dispatch(logoutUser());
		if (dispatchResult?.payload?.success) {
			dispatch(updateLogout());
			router.push('/login');
		}
		// router.push('/login');
	}, [dispatch, router]);

	return (
		<SidebarMenu>
			<SidebarMenuItem>
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<SidebarMenuButton
							size="lg"
							className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground md:h-8 md:p-0 rounded-full"
						>
							<Avatar className="h-8 w-8 rounded-lg">
								<AvatarImage
									src={companyData?.companyDetails?.logo}
									alt={user?.name}
								/>
								<AvatarFallback className="rounded-lg">
									{user?.email?.split(' ')[0].charAt(0).toUpperCase()}
								</AvatarFallback>
							</Avatar>
							<div className="grid flex-1 text-left text-sm leading-tight">
								{/* <span className="truncate font-semibold">{user.name}</span> */}
								<span className="truncate text-xs">{user.email}</span>
							</div>
							<ChevronsUpDown className="ml-auto size-4" />
						</SidebarMenuButton>
					</DropdownMenuTrigger>
					<DropdownMenuContent
						className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
						side={isMobile ? 'bottom' : 'right'}
						align="end"
						sideOffset={4}
					>
						<DropdownMenuLabel className="p-0 font-normal">
							<div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
								<Avatar className="h-8 w-8 rounded-lg">
									<AvatarImage
										src={companyData?.companyDetails?.logo}
										alt={companyData?.companyDetails?.businessName}
									/>
									<AvatarFallback className="rounded-lg">
										{companyData?.companyDetails?.businessName
											?.split(' ')[0]
											.charAt(0)
											.toUpperCase()}
									</AvatarFallback>
								</Avatar>
								<div className="grid flex-1 text-left">
									<span className="truncate text-md  font-semibold">
										{companyData?.companyDetails?.businessName}
									</span>
									<span className="truncate text-xs text-gray-400 font-semibold">
										{companyData?.companyDetails?.businessCountry?.name}
									</span>
								</div>
							</div>
						</DropdownMenuLabel>
						<DropdownMenuSeparator />
						<DropdownMenuGroup>
							{/* <DropdownMenuItem>
								<Sparkles />
								Upgrade to Pro
							</DropdownMenuItem> */}
						</DropdownMenuGroup>
						{/* <DropdownMenuSeparator /> */}
						<DropdownMenuGroup>
							{user.role === userRoles.GLORIFIED_CLIENT_ADMIN && (
								<DropdownMenuItem>
									<BadgeCheck />
									<Link href="/client-admin/account">Account</Link>
								</DropdownMenuItem>
							)}
							{/* <DropdownMenuItem>
								<CreditCard />
								Billing
							</DropdownMenuItem>
							<DropdownMenuItem>
								<Bell />
								Notifications
							</DropdownMenuItem> */}
						</DropdownMenuGroup>
						<DropdownMenuSeparator />
						<Button variant="outline" className="w-full" onClick={logout}>
							<LogOut />
							Log out
						</Button>
						{/* <DropdownMenuItem>
						</DropdownMenuItem> */}
					</DropdownMenuContent>
				</DropdownMenu>
			</SidebarMenuItem>
		</SidebarMenu>
	);
}
