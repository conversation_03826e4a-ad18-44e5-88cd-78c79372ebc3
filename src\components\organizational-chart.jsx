'use client';

import { useState } from 'react';
import {
	ChevronDown,
	ChevronRight,
	Users,
	Briefcase,
	Search,
	Mail,
	Phone,
	Building,
	LayoutGrid,
	X,
	ChevronLeft,
	ChevronUp,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import { But<PERSON> } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardFooter,
} from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from '@/components/ui/tooltip';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
	Pagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from '@/components/ui/pagination';

// Sample data generator function to simulate large datasets
function generateSampleData(
	businessUnitCount = 6,
	departmentsPerBU = 10,
	employeesPerDept = 100
) {
	const colorSchemes = [
		'from-[hsl(var(--chart-1))] to-[hsl(var(--chart-3))]',
		'from-[hsl(var(--chart-2))] to-[hsl(var(--chart-4))]',
		'from-[hsl(var(--chart-5))] to-[hsl(var(--chart-6))]',
		'from-[hsl(var(--chart-3))] to-[hsl(var(--chart-5))]',
		'from-[hsl(var(--chart-4))] to-[hsl(var(--chart-6))]',
		'from-[hsl(var(--chart-1))] to-[hsl(var(--chart-2))]',
	];

	const businessUnits = [];

	for (let bu = 0; bu < businessUnitCount; bu++) {
		const departments = [];

		for (let dept = 0; dept < departmentsPerBU; dept++) {
			const employees = [];

			for (let emp = 0; emp < employeesPerDept; emp++) {
				employees.push({
					id: `emp-${bu}-${dept}-${emp}`,
					name: `Employee ${emp + 1}`,
					position: `Role ${(emp % 5) + 1}`,
					avatar: '/placeholder.svg?height=80&width=80',
					email: `employee${emp}@company.com`,
					skills: [`Skill ${(emp % 10) + 1}`, `Skill ${((emp + 5) % 10) + 1}`],
				});
			}

			departments.push({
				id: `dept-${bu}-${dept}`,
				name: `Department ${dept + 1}`,
				color: colorSchemes[dept % colorSchemes.length],
				head: {
					id: `head-${bu}-${dept}`,
					name: `Department Head ${dept + 1}`,
					position: `Department ${dept + 1} Director`,
					avatar: '/placeholder.svg?height=80&width=80',
					email: `dept${dept}@company.com`,
					skills: ['Leadership', 'Management', `Expertise ${dept + 1}`],
				},
				employees,
				expanded: false,
			});
		}

		businessUnits.push({
			id: `bu-${bu}`,
			name: `Business Unit ${bu + 1}`,
			head: {
				id: `bu-head-${bu}`,
				name: `BU Head ${bu + 1}`,
				position: `VP of Business Unit ${bu + 1}`,
				avatar: '/placeholder.svg?height=80&width=80',
				email: `buhead${bu}@company.com`,
				phone: `+1 (555) ${100 + bu}-${1000 + bu}`,
				skills: ['Executive Leadership', 'Strategy', 'Business Development'],
			},
			departments,
			expanded: false,
		});
	}

	return { businessUnits };
}

export default function OrganizationalChart({ userRole, userId = 'bu-0' }) {
	// Generate large sample data
	const [organization, setOrganization] = useState(() =>
		generateSampleData(6, 10, 100)
	);

	// Search and filter state
	const [searchTerm, setSearchTerm] = useState('');
	const [filterDepartment, setFilterDepartment] = useState(null);

	// Pagination state for different views
	const [buPage, setBuPage] = useState(1);
	const [deptPage, setDeptPage] = useState(1);
	const [empPage, setEmpPage] = useState(1);

	const ITEMS_PER_PAGE = {
		businessUnits: 4,
		departments: 6,
		employees: 10,
	};

	// Toggle business unit expansion (for admin view)
	const toggleBusinessUnit = (buId) => {
		setOrganization({
			...organization,
			businessUnits: organization.businessUnits.map((bu) =>
				bu.id === buId ? { ...bu, expanded: !bu.expanded } : bu
			),
		});
	};

	// Toggle department expansion
	const toggleDepartment = (buId, deptId) => {
		setOrganization({
			...organization,
			businessUnits: organization.businessUnits.map((bu) =>
				bu.id === buId
					? {
							...bu,
							departments: bu.departments.map((dept) =>
								dept.id === deptId
									? { ...dept, expanded: !dept.expanded }
									: dept
							),
						}
					: bu
			),
		});
	};

	// Find the relevant business unit or department based on user role and ID
	const findUserBusinessUnit = () => {
		return organization.businessUnits.find((bu) => bu.id === userId);
	};

	const findUserDepartment = () => {
		for (const bu of organization.businessUnits) {
			const dept = bu.departments.find((dept) => dept.head.id === userId);
			if (dept) return { businessUnit: bu, department: dept };
		}
		return null;
	};

	// Filter functions
	const filterBusinessUnits = (businessUnits) => {
		if (!searchTerm) return businessUnits;

		return businessUnits.filter(
			(bu) =>
				bu.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				bu.head.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				bu.departments.some(
					(dept) =>
						dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
						dept.head.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
						dept.employees.some((emp) =>
							emp.name.toLowerCase().includes(searchTerm.toLowerCase())
						)
				)
		);
	};

	const filterDepartments = (departments) => {
		let filtered = departments;

		if (searchTerm) {
			filtered = filtered.filter(
				(dept) =>
					dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
					dept.head.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
					dept.employees.some((emp) =>
						emp.name.toLowerCase().includes(searchTerm.toLowerCase())
					)
			);
		}

		if (filterDepartment) {
			filtered = filtered.filter((dept) => dept.id === filterDepartment);
		}

		return filtered;
	};

	const filterEmployees = (employees) => {
		if (!searchTerm) return employees;

		return employees.filter(
			(emp) =>
				emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				(emp.position &&
					emp.position.toLowerCase().includes(searchTerm.toLowerCase())) ||
				(emp.skills &&
					emp.skills.some((skill) =>
						skill.toLowerCase().includes(searchTerm.toLowerCase())
					))
		);
	};

	// Pagination helpers
	const paginateArray = (array, page, itemsPerPage) => {
		const startIndex = (page - 1) * itemsPerPage;
		return array.slice(startIndex, startIndex + itemsPerPage);
	};

	const calculateTotalPages = (totalItems, itemsPerPage) => {
		return Math.ceil(totalItems / itemsPerPage);
	};

	// Render the Admin View - Shows all business units and their departments with pagination
	const renderAdminView = () => {
		const filteredBusinessUnits = filterBusinessUnits(
			organization.businessUnits
		);
		const totalBUPages = calculateTotalPages(
			filteredBusinessUnits.length,
			ITEMS_PER_PAGE.businessUnits
		);
		const paginatedBusinessUnits = paginateArray(
			filteredBusinessUnits,
			buPage,
			ITEMS_PER_PAGE.businessUnits
		);

		return (
			<div className="w-full p-4 overflow-auto rounded-xl">
				<div className="max-w-7xl mx-auto">
					<div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
						<h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
							Organization Overview
						</h2>

						<div className="flex items-center gap-3 w-full md:w-auto">
							<div className="relative w-full md:w-64">
								<Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
								<Input
									type="search"
									placeholder="Search..."
									className="pl-9 w-full"
									value={searchTerm}
									onChange={(e) => {
										setSearchTerm(e.target.value);
										setBuPage(1); // Reset to first page on search
									}}
								/>
								{searchTerm && (
									<Button
										variant="ghost"
										size="icon"
										className="absolute right-0 top-0 h-9 w-9"
										onClick={() => setSearchTerm('')}
									>
										<X className="h-4 w-4" />
									</Button>
								)}
							</div>
						</div>
					</div>
					<Separator />
					{filteredBusinessUnits.length === 0 ? (
						<div className="flex flex-col items-center justify-center py-12 text-center">
							<Users className="h-12 w-12 text-muted-foreground mb-4" />
							<h3 className="text-lg font-medium">No results found</h3>
							<p className="text-muted-foreground mt-1">
								Try adjusting your search terms
							</p>
							{searchTerm && (
								<Button
									variant="outline"
									className="mt-4"
									onClick={() => setSearchTerm('')}
								>
									Clear Search
								</Button>
							)}
						</div>
					) : (
						<>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
								{paginatedBusinessUnits.map((bu, buIndex) => (
									<motion.div
										key={bu.id}
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.3, delay: 0.05 * buIndex }}
										className="flex flex-col"
									>
										{/* Business Unit Card */}
										<Card className="w-full overflow-hidden border-0 shadow-lg mb-6 relative">
											<div className="absolute top-0 left-0 right-0 h-1.5 bg-gradient-to-r from-primary to-primary/70"></div>
											<CardHeader className="flex flex-row items-center justify-between pb-2">
												<div>
													<div className="flex items-center gap-2">
														<Building className="h-4 w-4 text-primary" />
														<CardTitle className="text-lg font-bold">
															{bu.name}
														</CardTitle>
													</div>
													<p className="text-xs text-muted-foreground mt-1">
														Business Unit
													</p>
												</div>
												<Button
													variant="ghost"
													size="icon"
													className="h-8 w-8 rounded-full"
													onClick={() => toggleBusinessUnit(bu.id)}
												>
													{bu.expanded ? (
														<ChevronDown className="h-4 w-4" />
													) : (
														<ChevronRight className="h-4 w-4" />
													)}
												</Button>
											</CardHeader>
											<CardContent className="pt-0">
												<div className="flex items-center gap-4">
													<Avatar className="h-16 w-16 border-2 border-background shadow-sm">
														<AvatarImage
															src={bu.head.avatar}
															alt={bu.head.name}
														/>
														<AvatarFallback>
															{bu.head.name.charAt(0)}
														</AvatarFallback>
													</Avatar>
													<div>
														<p className="font-medium">{bu.head.name}</p>
														<p className="text-sm text-muted-foreground">
															{bu.head.position}
														</p>
														<div className="flex items-center gap-1.5 mt-1">
															<Mail className="h-3 w-3 text-muted-foreground" />
															<span className="text-xs text-muted-foreground">
																{bu.head.email}
															</span>
														</div>
														<div className="flex flex-wrap gap-1 mt-2">
															{bu.head.skills?.slice(0, 2).map((skill) => (
																<Badge
																	key={skill}
																	variant="outline"
																	className="text-xs font-normal"
																>
																	{skill}
																</Badge>
															))}
														</div>
													</div>
												</div>
												<div className="mt-3 flex items-center justify-between">
													<div className="flex items-center gap-2">
														<LayoutGrid className="h-3.5 w-3.5 text-muted-foreground" />
														<span className="text-xs text-muted-foreground">
															{bu.departments.length} departments
														</span>
													</div>
													<div className="flex items-center gap-2">
														<Users className="h-3.5 w-3.5 text-muted-foreground" />
														<span className="text-xs text-muted-foreground">
															{bu.departments.reduce(
																(total, dept) => total + dept.employees.length,
																0
															)}{' '}
															employees
														</span>
													</div>
												</div>
											</CardContent>
										</Card>

										{/* Departments */}
										<AnimatePresence>
											{bu.expanded && (
												<motion.div
													initial={{ opacity: 0, height: 0 }}
													animate={{ opacity: 1, height: 'auto' }}
													exit={{ opacity: 0, height: 0 }}
													transition={{ duration: 0.3 }}
													className="pl-6 border-l-2 border-border ml-8 mb-4"
												>
													<div className="flex items-center justify-between mb-4">
														<div className="flex items-center gap-2">
															<LayoutGrid className="h-4 w-4 text-muted-foreground" />
															<p className="text-sm font-medium">
																Departments ({bu.departments.length})
															</p>
														</div>

														{/* Department pagination controls */}
														{bu.departments.length >
															ITEMS_PER_PAGE.departments && (
															<div className="flex items-center gap-1">
																<Button
																	variant="ghost"
																	size="icon"
																	className="h-7 w-7"
																	disabled={deptPage === 1}
																	onClick={() =>
																		setDeptPage((prev) => Math.max(1, prev - 1))
																	}
																>
																	<ChevronLeft className="h-4 w-4" />
																</Button>
																<span className="text-xs text-muted-foreground">
																	{deptPage} /{' '}
																	{Math.ceil(
																		bu.departments.length /
																			ITEMS_PER_PAGE.departments
																	)}
																</span>
																<Button
																	variant="ghost"
																	size="icon"
																	className="h-7 w-7"
																	disabled={
																		deptPage >=
																		Math.ceil(
																			bu.departments.length /
																				ITEMS_PER_PAGE.departments
																		)
																	}
																	onClick={() =>
																		setDeptPage((prev) => prev + 1)
																	}
																>
																	<ChevronRight className="h-4 w-4" />
																</Button>
															</div>
														)}
													</div>

													<div className="grid grid-cols-1 gap-4">
														{paginateArray(
															filterDepartments(bu.departments),
															deptPage,
															ITEMS_PER_PAGE.departments
														).map((dept, deptIndex) => (
															<motion.div
																key={dept.id}
																initial={{ opacity: 0, x: -10 }}
																animate={{ opacity: 1, x: 0 }}
																transition={{
																	duration: 0.2,
																	delay: 0.05 * deptIndex,
																}}
															>
																<Card className="w-full border border-border hover:border-border/80 transition-all hover:shadow-md">
																	<div
																		className={`h-1 w-full bg-gradient-to-r ${dept.color}`}
																	></div>
																	<CardContent className="p-4">
																		<div className="flex items-center justify-between mb-3">
																			<h3 className="font-medium">
																				{dept.name}
																			</h3>
																			<Badge
																				variant="outline"
																				className="text-xs"
																			>
																				{dept.employees.length} members
																			</Badge>
																		</div>
																		<div className="flex items-center gap-3">
																			<Avatar className="h-12 w-12">
																				<AvatarImage
																					src={dept.head.avatar}
																					alt={dept.head.name}
																				/>
																				<AvatarFallback>
																					{dept.head.name.charAt(0)}
																				</AvatarFallback>
																			</Avatar>
																			<div>
																				<p className="font-medium text-sm">
																					{dept.head.name}
																				</p>
																				<p className="text-xs text-muted-foreground">
																					{dept.head.position}
																				</p>
																				<div className="flex items-center gap-1.5 mt-1">
																					<Mail className="h-3 w-3 text-muted-foreground" />
																					<span className="text-xs text-muted-foreground">
																						{dept.head.email}
																					</span>
																				</div>
																			</div>
																		</div>
																	</CardContent>
																</Card>
															</motion.div>
														))}
													</div>
												</motion.div>
											)}
										</AnimatePresence>
									</motion.div>
								))}
							</div>

							{/* Business Unit Pagination */}
							{totalBUPages > 1 && (
								<Pagination className="mt-6">
									<PaginationContent>
										<PaginationItem>
											<PaginationPrevious
												href="#"
												onClick={(e) => {
													e.preventDefault();
													setBuPage((prev) => Math.max(1, prev - 1));
												}}
												className={
													buPage === 1 ? 'pointer-events-none opacity-50' : ''
												}
											/>
										</PaginationItem>

										{Array.from({ length: totalBUPages }).map((_, i) => {
											// Show first page, last page, and pages around current page
											if (
												i === 0 ||
												i === totalBUPages - 1 ||
												(i >= buPage - 2 && i <= buPage)
											) {
												return (
													<PaginationItem key={i}>
														<PaginationLink
															href="#"
															isActive={buPage === i + 1}
															onClick={(e) => {
																e.preventDefault();
																setBuPage(i + 1);
															}}
														>
															{i + 1}
														</PaginationLink>
													</PaginationItem>
												);
											}

											// Show ellipsis for skipped pages
											if (
												(i === 1 && buPage > 3) ||
												(i === totalBUPages - 2 && buPage < totalBUPages - 2)
											) {
												return (
													<PaginationItem key={i}>
														<PaginationEllipsis />
													</PaginationItem>
												);
											}

											return null;
										})}

										<PaginationItem>
											<PaginationNext
												href="#"
												onClick={(e) => {
													e.preventDefault();
													setBuPage((prev) => Math.min(totalBUPages, prev + 1));
												}}
												className={
													buPage === totalBUPages
														? 'pointer-events-none opacity-50'
														: ''
												}
											/>
										</PaginationItem>
									</PaginationContent>
								</Pagination>
							)}
						</>
					)}
				</div>
			</div>
		);
	};

	// Render the Business Unit Head View - Shows departments and employees with pagination
	const renderBusinessUnitHeadView = () => {
		const businessUnit = findUserBusinessUnit();
		if (!businessUnit) return <div>Business unit not found</div>;

		const filteredDepartments = filterDepartments(businessUnit.departments);
		const totalDeptPages = calculateTotalPages(
			filteredDepartments.length,
			ITEMS_PER_PAGE.departments
		);
		const paginatedDepartments = paginateArray(
			filteredDepartments,
			deptPage,
			ITEMS_PER_PAGE.departments
		);

		return (
			<div className="w-full p-4 overflow-auto bg-muted/50 rounded-xl">
				<div className="max-w-7xl mx-auto">
					<div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
						<div>
							<h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
								{businessUnit.name}
							</h2>
							<p className="text-muted-foreground">Business Unit Overview</p>
						</div>

						<div className="flex items-center gap-3 w-full md:w-auto">
							<div className="relative w-full md:w-64">
								<Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
								<Input
									type="search"
									placeholder="Search departments or employees..."
									className="pl-9 w-full"
									value={searchTerm}
									onChange={(e) => {
										setSearchTerm(e.target.value);
										setDeptPage(1); // Reset to first page on search
									}}
								/>
								{searchTerm && (
									<Button
										variant="ghost"
										size="icon"
										className="absolute right-0 top-0 h-9 w-9"
										onClick={() => setSearchTerm('')}
									>
										<X className="h-4 w-4" />
									</Button>
								)}
							</div>
						</div>
					</div>

					{/* Business Unit Head */}
					<motion.div
						initial={{ opacity: 0, y: -20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5 }}
						className="mb-12 relative"
					>
						<div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/70 rounded-xl blur-xl opacity-20 -z-10 transform scale-105"></div>
						<Card className="w-full max-w-md mx-auto overflow-hidden border-0 shadow-xl relative z-10">
							<div className="absolute top-0 left-0 right-0 h-1.5 bg-gradient-to-r from-primary to-primary/70"></div>
							<div className="pt-6 pb-2 px-6 flex justify-center">
								<Avatar className="h-24 w-24 border-4 border-background shadow-md">
									<AvatarImage
										src={businessUnit.head.avatar}
										alt={businessUnit.head.name}
									/>
									<AvatarFallback>
										{businessUnit.head.name.charAt(0)}
									</AvatarFallback>
								</Avatar>
							</div>
							<CardHeader className="pt-2 pb-0 text-center">
								<CardTitle className="text-xl font-bold">
									{businessUnit.head.name}
								</CardTitle>
								<div className="flex items-center justify-center gap-1 mt-1">
									<Briefcase className="h-3.5 w-3.5 text-muted-foreground" />
									<p className="text-sm font-medium text-muted-foreground">
										{businessUnit.head.position}
									</p>
								</div>
							</CardHeader>
							<CardContent className="pb-6 pt-2">
								<div className="flex flex-col gap-3">
									<div className="flex items-center gap-2 text-sm">
										<Mail className="h-3.5 w-3.5 text-muted-foreground" />
										<span className="text-muted-foreground">
											{businessUnit.head.email}
										</span>
									</div>
									<div className="flex items-center gap-2 text-sm">
										<Phone className="h-3.5 w-3.5 text-muted-foreground" />
										<span className="text-muted-foreground">
											{businessUnit.head.phone}
										</span>
									</div>
									<div className="flex flex-wrap gap-1.5 mt-2">
										{businessUnit.head.skills?.map((skill) => (
											<Badge
												key={skill}
												variant="secondary"
												className="font-normal"
											>
												{skill}
											</Badge>
										))}
									</div>
								</div>
							</CardContent>
						</Card>
					</motion.div>

					{/* Vertical Line with decorative element */}
					<div className="relative w-px h-16 bg-gradient-to-b from-primary to-transparent mb-8 mx-auto">
						<div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-primary"></div>
					</div>

					{/* Department Stats */}
					<div className="mb-8 text-center">
						<p className="text-lg font-medium">
							Managing {businessUnit.departments.length} Departments with{' '}
							{businessUnit.departments.reduce(
								(total, dept) => total + dept.employees.length,
								0
							)}{' '}
							Employees
						</p>
					</div>

					{filteredDepartments.length === 0 ? (
						<div className="flex flex-col items-center justify-center py-12 text-center">
							<LayoutGrid className="h-12 w-12 text-muted-foreground mb-4" />
							<h3 className="text-lg font-medium">No departments found</h3>
							<p className="text-muted-foreground mt-1">
								Try adjusting your search terms
							</p>
							{searchTerm && (
								<Button
									variant="outline"
									className="mt-4"
									onClick={() => setSearchTerm('')}
								>
									Clear Search
								</Button>
							)}
						</div>
					) : (
						<>
							{/* Departments Container */}
							<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 w-full">
								{paginatedDepartments.map((dept, index) => (
									<motion.div
										key={dept.id}
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.3, delay: 0.05 * index }}
										className="flex flex-col items-center"
									>
										{/* Department Head */}
										<Card className="w-full overflow-hidden border-0 shadow-lg mb-6 relative">
											<div
												className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${dept.color}`}
											></div>
											<CardHeader className="flex flex-row items-center justify-between pb-2">
												<div>
													<CardTitle className="text-base font-bold">
														{dept.name}
													</CardTitle>
													<p className="text-xs text-muted-foreground mt-0.5">
														{dept.head.position}
													</p>
												</div>
												<Button
													variant="ghost"
													size="icon"
													className="h-8 w-8 rounded-full"
													onClick={() =>
														toggleDepartment(businessUnit.id, dept.id)
													}
												>
													{dept.expanded ? (
														<ChevronDown className="h-4 w-4" />
													) : (
														<ChevronRight className="h-4 w-4" />
													)}
												</Button>
											</CardHeader>
											<CardContent className="pt-0">
												<div className="flex items-center gap-4">
													<Avatar className="h-14 w-14 border-2 border-background shadow-sm">
														<AvatarImage
															src={dept.head.avatar}
															alt={dept.head.name}
														/>
														<AvatarFallback>
															{dept.head.name.charAt(0)}
														</AvatarFallback>
													</Avatar>
													<div>
														<p className="font-medium">{dept.head.name}</p>
														<div className="flex items-center gap-1.5 mt-1">
															<Mail className="h-3 w-3 text-muted-foreground" />
															<span className="text-xs text-muted-foreground">
																{dept.head.email}
															</span>
														</div>
														<div className="flex flex-wrap gap-1 mt-2">
															{dept.head.skills?.slice(0, 2).map((skill) => (
																<Badge
																	key={skill}
																	variant="outline"
																	className="text-xs font-normal"
																>
																	{skill}
																</Badge>
															))}
														</div>
													</div>
												</div>
												<div className="mt-3 flex items-center gap-2">
													<Users className="h-3.5 w-3.5 text-muted-foreground" />
													<span className="text-xs text-muted-foreground">
														{dept.employees.length} team members
													</span>
												</div>
											</CardContent>
											<CardFooter className="pt-0 pb-3 px-4">
												<Button
													variant="outline"
													size="sm"
													className="w-full text-xs"
													onClick={() =>
														toggleDepartment(businessUnit.id, dept.id)
													}
												>
													{dept.expanded ? 'Hide Employees' : 'View Employees'}
													{dept.expanded ? (
														<ChevronUp className="ml-2 h-3 w-3" />
													) : (
														<ChevronDown className="ml-2 h-3 w-3" />
													)}
												</Button>
											</CardFooter>
										</Card>

										{/* Employees */}
										<AnimatePresence>
											{dept.expanded && (
												<motion.div
													initial={{ opacity: 0, height: 0 }}
													animate={{ opacity: 1, height: 'auto' }}
													exit={{ opacity: 0, height: 0 }}
													transition={{ duration: 0.3 }}
													className="w-full overflow-hidden"
												>
													<div className="flex items-center justify-between mb-3">
														<div className="flex items-center gap-2">
															<div
																className={`h-5 w-5 rounded-full flex items-center justify-center bg-gradient-to-r ${dept.color}`}
															>
																<Users className="h-3 w-3 text-white" />
															</div>
															<p className="text-sm font-medium">
																Team Members ({dept.employees.length})
															</p>
														</div>

														{/* Employee pagination controls */}
														{dept.employees.length >
															ITEMS_PER_PAGE.employees && (
															<div className="flex items-center gap-1">
																<Button
																	variant="ghost"
																	size="icon"
																	className="h-7 w-7"
																	disabled={empPage === 1}
																	onClick={() =>
																		setEmpPage((prev) => Math.max(1, prev - 1))
																	}
																>
																	<ChevronLeft className="h-4 w-4" />
																</Button>
																<span className="text-xs text-muted-foreground">
																	{empPage} /{' '}
																	{Math.ceil(
																		dept.employees.length /
																			ITEMS_PER_PAGE.employees
																	)}
																</span>
																<Button
																	variant="ghost"
																	size="icon"
																	className="h-7 w-7"
																	disabled={
																		empPage >=
																		Math.ceil(
																			dept.employees.length /
																				ITEMS_PER_PAGE.employees
																		)
																	}
																	onClick={() => setEmpPage((prev) => prev + 1)}
																>
																	<ChevronRight className="h-4 w-4" />
																</Button>
															</div>
														)}
													</div>

													<div className="grid grid-cols-1 gap-3">
														{paginateArray(
															filterEmployees(dept.employees),
															empPage,
															ITEMS_PER_PAGE.employees
														).map((employee, empIndex) => (
															<motion.div
																key={employee.id}
																initial={{ opacity: 0, x: -10 }}
																animate={{ opacity: 1, x: 0 }}
																transition={{
																	duration: 0.2,
																	delay: 0.05 * empIndex,
																}}
															>
																<Tooltip>
																	<TooltipTrigger asChild>
																		<Card className="w-full border border-border hover:border-border/80 transition-all hover:shadow-md">
																			<CardContent className="p-3 flex items-center gap-3">
																				<Avatar className="h-10 w-10">
																					<AvatarImage
																						src={employee.avatar}
																						alt={employee.name}
																					/>
																					<AvatarFallback>
																						{employee.name.charAt(0)}
																					</AvatarFallback>
																				</Avatar>
																				<div>
																					<p className="font-medium text-sm">
																						{employee.name}
																					</p>
																					<p className="text-xs text-muted-foreground">
																						{employee.position}
																					</p>
																					<div className="flex flex-wrap gap-1 mt-1">
																						{employee.skills
																							?.slice(0, 1)
																							.map((skill) => (
																								<Badge
																									key={skill}
																									variant="secondary"
																									className="text-[10px] px-1 py-0 h-4 font-normal"
																								>
																									{skill}
																								</Badge>
																							))}
																						{employee.skills &&
																							employee.skills.length > 1 && (
																								<Badge
																									variant="outline"
																									className="text-[10px] px-1 py-0 h-4 font-normal"
																								>
																									+{employee.skills.length - 1}
																								</Badge>
																							)}
																					</div>
																				</div>
																			</CardContent>
																		</Card>
																	</TooltipTrigger>
																	<TooltipContent
																		side="right"
																		className="max-w-xs"
																	>
																		<div className="space-y-1.5">
																			<p className="font-semibold">
																				{employee.name}
																			</p>
																			<p className="text-xs">
																				{employee.position}
																			</p>
																			{employee.skills && (
																				<div className="flex flex-wrap gap-1 pt-1">
																					{employee.skills.map((skill) => (
																						<Badge
																							key={skill}
																							variant="secondary"
																							className="text-xs font-normal"
																						>
																							{skill}
																						</Badge>
																					))}
																				</div>
																			)}
																		</div>
																	</TooltipContent>
																</Tooltip>
															</motion.div>
														))}
													</div>
												</motion.div>
											)}
										</AnimatePresence>
									</motion.div>
								))}
							</div>

							{/* Department Pagination */}
							{totalDeptPages > 1 && (
								<Pagination className="mt-8">
									<PaginationContent>
										<PaginationItem>
											<PaginationPrevious
												href="#"
												onClick={(e) => {
													e.preventDefault();
													setDeptPage((prev) => Math.max(1, prev - 1));
												}}
												className={
													deptPage === 1 ? 'pointer-events-none opacity-50' : ''
												}
											/>
										</PaginationItem>

										{Array.from({ length: totalDeptPages }).map((_, i) => {
											if (
												i === 0 ||
												i === totalDeptPages - 1 ||
												(i >= deptPage - 2 && i <= deptPage)
											) {
												return (
													<PaginationItem key={i}>
														<PaginationLink
															href="#"
															isActive={deptPage === i + 1}
															onClick={(e) => {
																e.preventDefault();
																setDeptPage(i + 1);
															}}
														>
															{i + 1}
														</PaginationLink>
													</PaginationItem>
												);
											}

											if (
												(i === 1 && deptPage > 3) ||
												(i === totalDeptPages - 2 &&
													deptPage < totalDeptPages - 2)
											) {
												return (
													<PaginationItem key={i}>
														<PaginationEllipsis />
													</PaginationItem>
												);
											}

											return null;
										})}

										<PaginationItem>
											<PaginationNext
												href="#"
												onClick={(e) => {
													e.preventDefault();
													setDeptPage((prev) =>
														Math.min(totalDeptPages, prev + 1)
													);
												}}
												className={
													deptPage === totalDeptPages
														? 'pointer-events-none opacity-50'
														: ''
												}
											/>
										</PaginationItem>
									</PaginationContent>
								</Pagination>
							)}
						</>
					)}
				</div>
			</div>
		);
	};

	// Render the Department Head View - Shows only employees in their department with pagination
	const renderDepartmentHeadView = () => {
		const result = findUserDepartment();
		if (!result) return <div>Department not found</div>;

		const { businessUnit, department } = result;
		const filteredEmployees = filterEmployees(department.employees);
		const totalEmpPages = calculateTotalPages(
			filteredEmployees.length,
			ITEMS_PER_PAGE.employees
		);
		const paginatedEmployees = paginateArray(
			filteredEmployees,
			empPage,
			ITEMS_PER_PAGE.employees
		);

		return (
			<div className="w-full p-4 overflow-auto bg-muted/50 rounded-xl">
				<div className="max-w-3xl mx-auto">
					<div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
						<div>
							<h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
								{department.name} Department
							</h2>
							<p className="text-muted-foreground">
								{businessUnit.name} Business Unit
							</p>
						</div>

						<div className="flex items-center gap-3 w-full md:w-auto">
							<div className="relative w-full md:w-64">
								<Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
								<Input
									type="search"
									placeholder="Search employees..."
									className="pl-9 w-full"
									value={searchTerm}
									onChange={(e) => {
										setSearchTerm(e.target.value);
										setEmpPage(1); // Reset to first page on search
									}}
								/>
								{searchTerm && (
									<Button
										variant="ghost"
										size="icon"
										className="absolute right-0 top-0 h-9 w-9"
										onClick={() => setSearchTerm('')}
									>
										<X className="h-4 w-4" />
									</Button>
								)}
							</div>
						</div>
					</div>

					{/* Department Head */}
					<motion.div
						initial={{ opacity: 0, y: -20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5 }}
						className="mb-12 relative"
					>
						<div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/70 rounded-xl blur-xl opacity-20 -z-10 transform scale-105"></div>
						<Card className="w-full max-w-md mx-auto overflow-hidden border-0 shadow-xl relative z-10">
							<div
								className={`absolute top-0 left-0 right-0 h-1.5 bg-gradient-to-r ${department.color}`}
							></div>
							<CardContent className="p-6">
								<div className="flex items-center gap-4">
									<Avatar className="h-20 w-20 border-4 border-background shadow-md">
										<AvatarImage
											src={department.head.avatar}
											alt={department.head.name}
										/>
										<AvatarFallback>
											{department.head.name.charAt(0)}
										</AvatarFallback>
									</Avatar>
									<div>
										<h3 className="text-xl font-bold">
											{department.head.name}
										</h3>
										<div className="flex items-center gap-1 mt-1">
											<Briefcase className="h-3.5 w-3.5 text-muted-foreground" />
											<p className="text-sm text-muted-foreground">
												{department.head.position}
											</p>
										</div>
										<div className="flex items-center gap-1.5 mt-2">
											<Mail className="h-3.5 w-3.5 text-muted-foreground" />
											<span className="text-sm text-muted-foreground">
												{department.head.email}
											</span>
										</div>
										<div className="flex flex-wrap gap-1.5 mt-3">
											{department.head.skills?.map((skill) => (
												<Badge
													key={skill}
													variant="secondary"
													className="font-normal"
												>
													{skill}
												</Badge>
											))}
										</div>
									</div>
								</div>
							</CardContent>
						</Card>
					</motion.div>

					{/* Team Members */}
					<div className="mb-4">
						<div className="flex items-center justify-between mb-4">
							<div className="flex items-center gap-2">
								<div
									className={`h-6 w-6 rounded-full flex items-center justify-center bg-gradient-to-r ${department.color}`}
								>
									<Users className="h-4 w-4 text-white" />
								</div>
								<h3 className="text-lg font-medium">
									Your Team ({department.employees.length})
								</h3>
							</div>

							{filteredEmployees.length > 0 && (
								<Badge variant="outline">
									{searchTerm
										? `${filteredEmployees.length} matches`
										: `${filteredEmployees.length} total`}
								</Badge>
							)}
						</div>

						{filteredEmployees.length === 0 ? (
							<div className="flex flex-col items-center justify-center py-12 text-center">
								<Users className="h-12 w-12 text-muted-foreground mb-4" />
								<h3 className="text-lg font-medium">No employees found</h3>
								<p className="text-muted-foreground mt-1">
									Try adjusting your search terms
								</p>
								{searchTerm && (
									<Button
										variant="outline"
										className="mt-4"
										onClick={() => setSearchTerm('')}
									>
										Clear Search
									</Button>
								)}
							</div>
						) : (
							<>
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									{paginatedEmployees.map((employee, empIndex) => (
										<motion.div
											key={employee.id}
											initial={{ opacity: 0, y: 10 }}
											animate={{ opacity: 1, y: 0 }}
											transition={{ duration: 0.3, delay: 0.1 * empIndex }}
										>
											<Card className="w-full border border-border hover:border-border/80 transition-all hover:shadow-md">
												<CardContent className="p-4 flex items-center gap-4">
													<Avatar className="h-14 w-14">
														<AvatarImage
															src={employee.avatar}
															alt={employee.name}
														/>
														<AvatarFallback>
															{employee.name.charAt(0)}
														</AvatarFallback>
													</Avatar>
													<div>
														<p className="font-medium">{employee.name}</p>
														<p className="text-sm text-muted-foreground">
															{employee.position}
														</p>
														<div className="flex flex-wrap gap-1.5 mt-2">
															{employee.skills?.map((skill) => (
																<Badge
																	key={skill}
																	variant="secondary"
																	className="text-xs font-normal"
																>
																	{skill}
																</Badge>
															))}
														</div>
													</div>
												</CardContent>
											</Card>
										</motion.div>
									))}
								</div>

								{/* Employee Pagination */}
								{totalEmpPages > 1 && (
									<Pagination className="mt-8">
										<PaginationContent>
											<PaginationItem>
												<PaginationPrevious
													href="#"
													onClick={(e) => {
														e.preventDefault();
														setEmpPage((prev) => Math.max(1, prev - 1));
													}}
													className={
														empPage === 1
															? 'pointer-events-none opacity-50'
															: ''
													}
												/>
											</PaginationItem>

											{Array.from({ length: totalEmpPages }).map((_, i) => {
												if (
													i === 0 ||
													i === totalEmpPages - 1 ||
													(i >= empPage - 2 && i <= empPage)
												) {
													return (
														<PaginationItem key={i}>
															<PaginationLink
																href="#"
																isActive={empPage === i + 1}
																onClick={(e) => {
																	e.preventDefault();
																	setEmpPage(i + 1);
																}}
															>
																{i + 1}
															</PaginationLink>
														</PaginationItem>
													);
												}

												if (
													(i === 1 && empPage > 3) ||
													(i === totalEmpPages - 2 &&
														empPage < totalEmpPages - 2)
												) {
													return (
														<PaginationItem key={i}>
															<PaginationEllipsis />
														</PaginationItem>
													);
												}

												return null;
											})}

											<PaginationItem>
												<PaginationNext
													href="#"
													onClick={(e) => {
														e.preventDefault();
														setEmpPage((prev) =>
															Math.min(totalEmpPages, prev + 1)
														);
													}}
													className={
														empPage === totalEmpPages
															? 'pointer-events-none opacity-50'
															: ''
													}
												/>
											</PaginationItem>
										</PaginationContent>
									</Pagination>
								)}
							</>
						)}
					</div>
				</div>
			</div>
		);
	};

	// Render the appropriate view based on user role
	return (
		<TooltipProvider>
			{userRole === 'admin' && renderAdminView()}
			{userRole === 'businessUnitHead' && renderBusinessUnitHeadView()}
			{userRole === 'departmentHead' && renderDepartmentHeadView()}
		</TooltipProvider>
	);
}
