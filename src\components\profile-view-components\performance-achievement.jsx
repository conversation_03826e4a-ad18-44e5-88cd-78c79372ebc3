import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Trophy, Target, Clock, User, TrendingUp } from 'lucide-react';
import {
	calculatePerformanceScore,
	getAchievementDetails,
} from '@/lib/utils/performanceCalculator';
import oliveBranchRight from '@/assets/performance-olive-branch-right.png';
import oliveBranchLeft from '@/assets/performance-olive-branch-left.png';
import Image from 'next/image';

const PerformanceAchievement = ({ userProfile, attendanceLogs }) => {
	// Calculate performance score
	const performanceData = calculatePerformanceScore({
		attendanceLogs,
		userFlags: userProfile?.userFlags || {},
		personalDetails: userProfile?.personalDetails || {},
	});

	const { totalScore, achievementLevel, breakdown, metrics } = performanceData;
	const achievementDetails = getAchievementDetails(achievementLevel);

	// Laurel wreath SVG component
	const LaurelWreath = ({ className = 'w-8 h-8' }) => (
		<svg
			className={className}
			viewBox="0 0 100 100"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			{/* Left laurel branch */}
			<path
				d="M20 50 C15 45, 10 40, 15 35 C20 30, 25 35, 20 40 C15 35, 10 30, 15 25 C20 20, 25 25, 20 30 C15 25, 10 20, 15 15 C20 10, 25 15, 20 20"
				stroke={achievementDetails.color}
				strokeWidth="2"
				fill="none"
			/>
			{/* Right laurel branch */}
			<path
				d="M80 50 C85 45, 90 40, 85 35 C80 30, 75 35, 80 40 C85 35, 90 30, 85 25 C80 20, 75 25, 80 30 C85 25, 90 20, 85 15 C80 10, 75 15, 80 20"
				stroke={achievementDetails.color}
				strokeWidth="2"
				fill="none"
			/>
		</svg>
	);

	return (
		<Card className="w-full h-full shadow-none border-none mt-10">
			<CardContent>
				{/* Header with score and achievement level */}
				<div className="text-center mb-4">
					<div className="flex items-start justify-center mb-2 scale-110">
						<Image
							src={oliveBranchLeft}
							alt="Olive branch"
							width={64}
							height={64}
							className="drop-shadow-xl"
						/>

						<span className="text-4xl mt-4 font-bold text-foreground p-0 m-0 drop-shadow-lg">
							{totalScore}
						</span>
						<Image
							src={oliveBranchRight}
							alt="Olive branch"
							width={64}
							height={64}
							className="drop-shadow-xl"
						/>
					</div>

					{/* <div className="mb-2">
						<Badge
							className={`${achievementDetails.bgColor} ${achievementDetails.textColor} text-sm font-medium px-3 py-1`}
							variant="outline"
						>
							<Trophy className="w-4 h-4 mr-1" />
							{achievementDetails.name} Performer
						</Badge>
					</div> */}

					<p className="text-sm text-muted-foreground">
						{achievementDetails.description}
					</p>
				</div>

				{/* Performance metrics breakdown */}
				<div className="grid grid-cols-2 gap-2 w-full">
					{/* Attendance */}
					<div className="flex items-start flex-col p-2 border-r">
						<div className="flex flex-col items-start justify-between w-full">
							<p className="text-sm font-medium flex items-center gap-2 justify-between w-full">
								Attendance
								<Clock className="size-5" />
							</p>
							<span className="font-semibold">{breakdown.attendance}</span>
						</div>
					</div>

					{/* Profile Completion */}
					<div className="flex items-start flex-col p-2 border-l">
						<div className="flex flex-col items-start justify-between w-full">
							<p className="text-sm font-medium flex items-center gap-2 justify-between w-full">
								Profile
								<User className="size-5" />
							</p>
							<span className="font-semibold">
								{breakdown.profileCompletion}
							</span>
						</div>
					</div>

					{/* Consistency */}
					<div className="flex items-start flex-col p-2 border-r">
						<div className="flex flex-col items-start justify-between w-full">
							<p className="text-sm font-medium flex items-center gap-2 justify-between w-full">
								Consistency
								<Target className="size-5" />
							</p>
							<span className="font-semibold">{breakdown.consistency}</span>
						</div>
					</div>

					{/* Engagement */}
					<div className="flex items-start flex-col p-2 border-l">
						<div className="flex flex-col items-start justify-between w-full">
							<p className="text-sm font-medium flex items-center gap-2 justify-between w-full">
								Engagement
								<TrendingUp className="size-5" />
							</p>
							<span className="font-semibold">{breakdown.engagement}</span>
						</div>
					</div>
				</div>

				{/* Achievement level indicator */}
				<div className="mt-4 pt-3 border-t border-muted">
					<div className="flex items-center justify-between text-xs text-muted-foreground">
						<span>Performance Level</span>
						<div className="flex items-center gap-1">
							{['bronze', 'silver', 'gold', 'platinum'].map((level, index) => {
								const isActive =
									['bronze', 'silver', 'gold', 'platinum'].indexOf(
										achievementLevel
									) >= index;
								const levelDetails = getAchievementDetails(level);
								return (
									<div
										key={level}
										className={`w-2 h-2 rounded-full ${
											isActive ? 'bg-current' : 'bg-muted-foreground/20'
										}`}
										style={{
											color: isActive ? levelDetails.color : undefined,
										}}
									/>
								);
							})}
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
};

export default PerformanceAchievement;
