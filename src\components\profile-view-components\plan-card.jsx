'use client';
import React, { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, PlusIcon } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { format, addDays, subDays } from 'date-fns';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ScrollArea } from '../ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

export function PlanCard({
	plans = [
		{
			id: 1,
			fromTime: '09:00 AM',
			toTime: '10:30 AM',
			description:
				'Daily stand-up + sprint planning with the dev team to align on priorities and blockers.',
		},
		{
			id: 2,
			fromTime: '10:45 AM',
			toTime: '12:00 PM',
			description:
				'Work on authentication flow bugs, token expiry edge cases, and session handling improvements.',
		},
		{
			id: 3,
			fromTime: '12:15 PM',
			toTime: '01:30 PM',
			description:
				'Code review with backend team for PRs related to user service refactor and DB optimization logic.',
		},
		{
			id: 4,
			fromTime: '01:45 PM',
			toTime: '03:00 PM',
			description:
				'Work on frontend bug fixes and feature enhancements for the dashboard and user profile pages.',
		},
		{
			id: 5,
			fromTime: '03:15 PM',
			toTime: '04:30 PM',
			description:
				'Daily stand-up + sprint planning with the dev team to align on priorities and blockers.',
		},
	],
}) {
	const [currentDate, setCurrentDate] = useState(new Date());

	const handlePrev = () => setCurrentDate((prev) => subDays(prev, 1));
	const handleNext = () => setCurrentDate((prev) => addDays(prev, 1));

	const isToday = (date) => {
		const today = new Date();
		return (
			date.getDate() === today.getDate() &&
			date.getMonth() === today.getMonth() &&
			date.getFullYear() === today.getFullYear()
		);
	};

	return (
		<Card className="w-full h-[350px] flex flex-col">
			<CardHeader className="flex-shrink-0">
				<div className="flex items-center justify-between">
					<CardTitle>Plan My Day</CardTitle>
					<div className="h-8 flex items-center gap-2">
						<Button variant="outline" size="icon" onClick={handlePrev}>
							<ChevronLeftIcon />
						</Button>
						<div>
							<span className="text-sm text-card-foreground opacity-80">
								{isToday(currentDate)
									? 'Today'
									: format(currentDate, 'do MMM, yyyy')}
							</span>
						</div>
						<Button variant="outline" size="icon" onClick={handleNext}>
							<ChevronRightIcon />
						</Button>
						<Button
							variant="outline"
							size="icon"
							className="ml-2 bg-green-200 text-green-600"
						>
							<PlusIcon />
						</Button>
					</div>
				</div>
			</CardHeader>

			<CardContent className="flex-1 overflow-hidden">
				<ScrollArea className="h-full">
					{plans.map((plan) => (
						<div className="flex ml-1 align-middle" key={plan.id}>
							<div className="left-0 my-1 w-1.5 h-auto rounded-full bg-red-500"></div>
							<Alert className="pl-3 border-0 py-1 bg-transparent">
								<AlertTitle className="font-semibold">
									{plan.fromTime} - {plan.toTime}
								</AlertTitle>
								<AlertDescription>{plan.description}</AlertDescription>
							</Alert>
						</div>
					))}
				</ScrollArea>
			</CardContent>
		</Card>
	);
}
