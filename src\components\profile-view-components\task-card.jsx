import * as Icons from 'lucide-react';
import { ScrollArea } from '../ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '@/components/ui/badge';
import { fetchTaskAssignedToEmployee } from '@/lib/features/tasks/tasksSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { sanitizeHtml, userRoles } from '@/lib/utils';
import { useRouter } from 'next/navigation';

// Helper function to format date
const formatTimeAgo = (dateString) => {
	if (!dateString) return 'No date';

	const date = new Date(dateString);
	const now = new Date();
	const diffInMs = now - date;
	const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
	const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
	const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

	if (diffInDays > 0) {
		return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
	} else if (diffInHours > 0) {
		return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
	} else if (diffInMinutes > 0) {
		return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
	} else {
		return 'Just now';
	}
};

// Helper function to get icon based on status and priority
const getTaskIcon = (status, priority) => {
	switch (status) {
		case 'completed':
			return 'CheckCircle';
		case 'in progress':
			return 'Clock';
		case 'pending':
			return priority === 'high' ? 'AlertTriangle' : 'Circle';
		default:
			return 'FileText';
	}
};

// Helper function to get background color based on priority and status
const getIconBackgroundColor = (status, priority) => {
	if (status === 'completed') return '#91F6A8'; // Green
	if (status === 'in progress') return '#AFC7F9'; // Blue
	if (priority === 'high') return '#F9C5CD'; // Red
	if (priority === 'medium') return '#FFE4B5'; // Orange
	return '#E5E7EB'; // Gray for low priority
};

// Helper function to get status badge variant
const getStatusBadgeVariant = (status) => {
	switch (status) {
		case 'completed':
			return 'default';
		case 'in progress':
			return 'secondary';
		case 'pending':
			return 'outline';
		default:
			return 'outline';
	}
};

export function TaskCard() {
	const router = useRouter();
	const { tasks, isLoading } = useAppSelector((store) => store.tasks);
	const { authenticatedUser } = useAppSelector((store) => store.auth);
	const dispatch = useAppDispatch();
	const userType =
		authenticatedUser?.role === userRoles.GLORIFIED_CLIENT_ADMIN ||
		authenticatedUser?.role === userRoles.CLIENT_ADMIN
			? '/client-admin/projects-tasks-module/projects'
			: '/user/projects';

	useEffect(() => {
		dispatch(fetchTaskAssignedToEmployee(authenticatedUser.userId));
	}, [dispatch, authenticatedUser.userId]);

	return (
		<Card className="w-full h-[350px] flex flex-col">
			<CardHeader className="flex-shrink-0 flex flex-col gap-2">
				<div className="flex items-center justify-between">
					<CardTitle>Tasks</CardTitle>
					{tasks.length > 0 && (
						<Badge variant="secondary" className="text-xs">
							{tasks.length} task{tasks.length > 1 ? 's' : ''}
						</Badge>
					)}
				</div>
			</CardHeader>
			<CardContent className="flex-1 overflow-hidden">
				{isLoading ? (
					<div className="w-full h-full flex items-center justify-center">
						<Icons.Loader2 className="animate-spin" />
					</div>
				) : (
					<ScrollArea className="h-full">
						{tasks.length === 0 ? (
							<div className="text-center text-muted-foreground py-4">
								No tasks available.
							</div>
						) : (
							tasks.map((task) => {
								const iconName = getTaskIcon(task.status, task.priority);
								const Icon = Icons[iconName];
								const backgroundColor = getIconBackgroundColor(
									task.status,
									task.priority
								);
								const timeAgo = formatTimeAgo(task.updatedAt || task.createdAt);
								const description = sanitizeHtml(task.description);

								return (
									<div
										key={task._id}
										className="flex items-start gap-3 px-2 py-3 hover:bg-card-hover rounded-md  border border-border hover:border-primary cursor-pointer"
										onClick={() => {
											router.push(
												`${userType}/${task.projectId}`
											);
										}}
									>
										<div className="flex-shrink-0 mt-1">
											{task.coverImage ? (
												<Avatar className="h-10 w-10 rounded-lg">
													<AvatarImage
														src={task.coverImage || '/placeholder.svg'}
														alt={task.name}
														className="object-cover rounded-lg"
													/>
													<AvatarFallback
														className="rounded-lg text-sm font-medium"
														style={{ backgroundColor }}
													>
														{Icon ? (
															<Icon className="h-5 w-5 text-gray-700" />
														) : (
															<span className="text-xs font-bold">
																{task.code?.slice(0, 2) || '??'}
															</span>
														)}
													</AvatarFallback>
												</Avatar>
											) : (
												<div
													className="inline-flex items-center justify-center rounded-lg text-sm font-medium shadow-sm p-2 h-10 w-10"
													style={{ backgroundColor }}
												>
													{Icon ? (
														<Icon className="h-5 w-5 text-gray-700" />
													) : (
														<span className="text-xs font-bold">
															{task.code?.slice(0, 2) || '??'}
														</span>
													)}
												</div>
											)}
										</div>
										<div className="flex flex-col flex-1 min-w-0">
											<div className="flex items-start justify-between gap-2 mb-1">
												<div className="font-semibold text-card-foreground line-clamp-1">
													{task.name}
												</div>
												<div className="flex items-center gap-1 flex-shrink-0">
													<Badge
														variant={getStatusBadgeVariant(task.status)}
														className="text-xs capitalize"
													>
														{task.status}
													</Badge>
													{task.priority && (
														<Badge
															variant="outline"
															className={`text-xs capitalize ${
																task.priority === 'high'
																	? 'border-red-300 text-red-700'
																	: task.priority === 'medium'
																		? 'border-yellow-300 text-yellow-700'
																		: 'border-gray-300 text-gray-700'
															}`}
														>
															{task.priority}
														</Badge>
													)}
												</div>
											</div>

											{description && (
												<div className="text-sm text-muted-foreground mb-2">
													{description}
												</div>
											)}

											<div className="flex items-center justify-between text-xs text-muted-foreground">
												<span>
													Due: {new Date(task.dueDate).toLocaleDateString()}
												</span>
												<span>{timeAgo}</span>
											</div>

											{task.dueDate && (
												<div className="text-xs text-muted-foreground mt-1">
													Code: {task.code}
												</div>
											)}
										</div>
									</div>
								);
							})
						)}
					</ScrollArea>
				)}
			</CardContent>
		</Card>
	);
}
