'use client';

import { useState, useRef, useEffect } from 'react';
import { Camera, Edit } from 'lucide-react';
import { Button } from '../ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Badge } from '../ui/badge';
import { AspectRatio } from '../ui/aspect-ratio';
import Image from 'next/image';
import { Mail, Phone, Calendar } from 'lucide-react';
import { formatDate, capitalize } from './utils/profileUtils';
import {
	coverImages,
	storedCoverImages,
} from '../project/data/background-images';
import {
	updateEmployeeCoverImage,
	updateEmployeeProfileImage,
} from '@/lib/features/employees/employeeSlice';
import { useDispatch } from 'react-redux';

export function ProfileHeader({
	personalDetails,
	designation,
	email,
	nationality,
}) {
	const [coverImage, setCoverImage] = useState('');
	const dispatch = useDispatch();

	useEffect(() => {
		const randomCoverImage =
			coverImages[Math.floor(Math.random() * coverImages.length)];
		setCoverImage(randomCoverImage.url);
	}, []);
	const coverImageFallback = 'https://picsum.photos/1200/600?random=18';

	const [isUploadingCover, setIsUploadingCover] = useState(false);
	const [isUploadingProfile, setIsUploadingProfile] = useState(false);
	const coverImageInputRef = useRef(null);
	const profileImageInputRef = useRef(null);
	const [profilePreview, setProfilePreview] = useState(null); // new state
	const [coverPreview, setCoverPreview] = useState(null);

	// Placeholder function - Add your dispatch call here
	const handleCoverImageChange = async (event) => {
		const file = event.target.files[0];
		if (!file) return;

		const previewURL = URL.createObjectURL(file);
		setCoverPreview(previewURL); // set preview
		setIsUploadingCover(true);

		try {
			// TODO: Add your dispatch call here
			// Example: await dispatch(uploadCoverImage(file))
			await dispatch(
				updateEmployeeCoverImage({
					coverImage: file,
				})
			);

			console.log('Cover image selected:', file);
		} catch (error) {
			console.error('Error uploading cover image:', error);
		} finally {
			setIsUploadingCover(false);
			// Optional cleanup
			// URL.revokeObjectURL(previewURL);
		}
	};

	// Placeholder function - Add your dispatch call here
	const handleProfileImageChange = async (event) => {
		const file = event.target.files[0];
		if (!file) return;

		const previewURL = URL.createObjectURL(file);
		setProfilePreview(previewURL); // set local preview
		setIsUploadingProfile(true);

		try {
			// TODO: Upload to API
			await dispatch(updateEmployeeProfileImage({ profilePhoto: file }));
			console.log('Selected profile image:', file);
		} catch (error) {
			console.error('Error uploading profile image:', error);
		} finally {
			setIsUploadingProfile(false);
			// Cleanup optional (if you're replacing preview later with server URL)
			// URL.revokeObjectURL(previewURL);
		}
	};

	return (
		<div className="flex flex-col md:flex-row gap-6 mb-5">
			{/* Profile Info Section (right side) */}
			<div className="relative flex-1 rounded-xl overflow-hidden p-6 max-h-72 min-w-[300px]">
				<input
					type="file"
					ref={profileImageInputRef}
					onChange={handleProfileImageChange}
					accept="image/*"
					className="hidden"
				/>

				<div className="flex flex-col md:flex-row items-center gap-6 dark:text-white">
					{/* Avatar */}
					<div className="relative group">
						<Avatar className="h-32 w-32 border-4 border-white shadow-lg">
							<AvatarImage
								src={
									profilePreview ||
									personalDetails?.profilePhoto ||
									'/placeholder.svg'
								}
								alt={personalDetails?.name || 'User'}
								className="object-cover"
							/>
							<AvatarFallback className="text-3xl bg-indigo-700">
								{personalDetails?.name
									? personalDetails.name
											.split(' ')
											.map((n) => n[0])
											.join('')
									: 'U'}
							</AvatarFallback>
						</Avatar>

						{/* Avatar Edit */}
						<div
							onClick={() => profileImageInputRef.current?.click()}
							className="absolute inset-0 bg-black/40 hidden group-hover:flex items-center justify-center cursor-pointer rounded-full"
						>
							<Edit className="h-6 w-6 text-white" />
						</div>

						<Badge className="absolute -bottom-2 right-0 bg-green-500 border-2 border-white">
							Active
						</Badge>
					</div>

					{/* Info */}
					<div className="text-center md:text-left">
						<h1 className="text-3xl font-bold">
							{personalDetails?.name || 'User Name'}
						</h1>

						<div className="flex flex-wrap justify-center md:justify-start gap-2 mt-2">
							<Badge
								variant="outline"
								className="bg-black/20 dark:bg-black/20 dark:text-gray-300 border-white/40 dark:border-gray-700"
							>
								ID: {personalDetails?.employeeOrgId || 'N/A'}
							</Badge>
							<Badge
								variant="outline"
								className="bg-black/20 dark:bg-black/20 dark:text-gray-300 border-white/40 dark:border-gray-700"
							>
								{designation}
							</Badge>
							<Badge
								variant="outline"
								className="bg-black/20 dark:bg-black/20 dark:text-gray-300 border-white/40 dark:border-gray-700"
							>
								{capitalize(nationality)}
							</Badge>
						</div>

						<div className="mt-4 flex flex-wrap justify-center md:justify-start gap-4">
							<div className="flex items-center gap-1">
								<Mail className="h-4 w-4" />
								<span>{email || 'N/A'}</span>
							</div>
							<div className="flex items-center gap-1">
								<Phone className="h-4 w-4" />
								<span>
									{personalDetails?.countryDialCode || ''}{' '}
									{personalDetails?.mobile || 'N/A'}
								</span>
							</div>
							<div className="flex items-center gap-1">
								<Calendar className="h-4 w-4" />
								<span>
									Joined: {formatDate(personalDetails?.dateOfJoining)}
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
			{/* Cover Image Section (left side) */}
			<div className="relative flex-1 rounded-xl overflow-hidden max-h-72 min-w-[300px]">
				<input
					type="file"
					ref={coverImageInputRef}
					onChange={handleCoverImageChange}
					accept="image/*"
					className="hidden"
				/>
				<AspectRatio ratio={16 / 9} className="w-full max-h-72">
					<Image
						src={
							coverPreview ||
							personalDetails?.coverImage ||
							coverImage ||
							coverImageFallback
						}
						alt={personalDetails?.name || 'Employee Name'}
						className="object-cover grayscale dark:grayscale-0 transition duration-300"
						fill
					/>
				</AspectRatio>

				<Button
					size="sm"
					variant="secondary"
					className="absolute top-4 right-4 z-40 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/40"
					onClick={() => coverImageInputRef.current?.click()}
					disabled={isUploadingCover}
				>
					{isUploadingCover ? (
						<>
							<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
							Uploading...
						</>
					) : (
						<>
							<Edit className="h-4 w-4 mr-2" />
							Edit Cover
						</>
					)}
				</Button>

				<div className="absolute inset-0 dark:bg-black/40 z-20"></div>
			</div>
		</div>
	);
}
