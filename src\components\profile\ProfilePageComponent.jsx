'use client';

import { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	fetchEditRequests,
	addEditRequest,
	deleteEditRequests,
	updateEditRequest,
} from '@/lib/features/employees/editRequestSlice';
import { findPendingRequest } from '@/lib/utils/editRequestUtils';
import { ProfileHeader } from './ProfileHeader';
import { PendingRequests } from './PendingRequests';
import { ProfileTabs } from './ProfileTabs';
import { SectionEditDialog } from './dialogs/SectionEditDialog';
import { AddChildDialog } from './dialogs/AddChildDialog';
import { getSectionData } from './utils/profileUtils';

export function ProfilePageComponent() {
	// Get user profile data from Redux store
	const dispatch = useAppDispatch();
	const { userProfile, isLoading } = useAppSelector((state) => state.employee);
	const { editRequests } = useAppSelector((state) => state.editRequest);
	const { authenticatedUser } = useAppSelector((state) => state.auth);

	// React hooks must be called before any early returns
	const [showPendingRequests, setShowPendingRequests] = useState(false);
	const [editingSection, setEditingSection] = useState(null);
	const [showAddChildDialog, setShowAddChildDialog] = useState(false);

	// Extract data from userProfile or use fallback values
	const data = userProfile || {};
	const {
		personalDetails = {},
		education = [],
		family = {},
		experience = [],
		contact = [],
	} = data;

	// Get pending edit requests for the current user
	const pendingChanges = editRequests || [];

	// Fetch edit requests for the current user on component mount
	useEffect(() => {
		if (authenticatedUser?.userId) {
			dispatch(fetchEditRequests());
		}
	}, [dispatch, authenticatedUser?.userId]);

	// Show loading state
	if (isLoading) {
		return (
			<div className="container mx-auto p-4 flex items-center justify-center min-h-[400px]">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
					<p className="text-muted-foreground">Loading profile...</p>
				</div>
			</div>
		);
	}

	// Show message if no profile data
	if (!userProfile) {
		return (
			<div className="container mx-auto p-4 flex items-center justify-center min-h-[400px]">
				<div className="text-center">
					<p className="text-muted-foreground">No profile data available.</p>
				</div>
			</div>
		);
	}

	const handleSectionEditRequest = async (
		section,
		oldData,
		newData,
		reason
	) => {
		try {
			console.log(section, oldData, newData, reason);
			// Check for existing pending request
			const pendingRequest = findPendingRequest(
				editRequests,
				authenticatedUser?.userId,
				`${section.toLowerCase().replace(' ', '-')}-details`
			);
			console.log(pendingRequest);

			let result;
			if (pendingRequest) {
				// Update existing pending request
				const updatedRequestData = {
					id: pendingRequest._id,
					oldData,
					newData,
					reason,
				};

				result = await dispatch(updateEditRequest(updatedRequestData));
			} else {
				// Create new request
				const editRequestData = {
					userId: authenticatedUser?.userId,
					section: `${section.toLowerCase().replace(' ', '-')}-details`,
					oldData,
					newData,
					reason,
				};

				result = await dispatch(addEditRequest(editRequestData));
			}

			// Check if the dispatch was successful
			return result.type && !result.type.endsWith('/rejected');
		} catch (error) {
			console.error('Error submitting edit request:', error);
			return false;
		}
	};

	const handleFamilyUpdateRequest = async (
		section,
		oldData,
		newData,
		reason
	) => {
		// Use the existing section edit request handler
		const success = await handleSectionEditRequest(
			section,
			oldData,
			newData,
			reason
		);

		// Only close dialog if submission was successful
		if (success) {
			setShowAddChildDialog(false);
		}

		return success;
	};

	const handleCancelRequest = (id) => {
		// Delete edit request using Redux action
		dispatch(deleteEditRequests([id]));
	};

	return (
		<div className="container mx-auto p-4">
			{/* Hero Section with Profile Overview */}
			<ProfileHeader
				personalDetails={personalDetails}
				experience={experience}
				email={data?.email}
			/>

			{/* Pending Changes Section */}
			<PendingRequests
				pendingChanges={pendingChanges}
				onCancelRequest={handleCancelRequest}
				showPendingRequests={showPendingRequests}
				setShowPendingRequests={setShowPendingRequests}
			/>

			{/* Main Content with Tabs */}
			<ProfileTabs
				data={data}
				onEditSection={setEditingSection}
				onAddChild={() => setShowAddChildDialog(true)}
			/>

			{/* Section Edit Dialog */}
			<SectionEditDialog
				section={editingSection}
				currentData={getSectionData(data, editingSection)}
				onSubmit={handleSectionEditRequest}
				onClose={() => setEditingSection(null)}
			/>

			{/* Family Update Dialog */}
			<AddChildDialog
				open={showAddChildDialog}
				onSubmit={handleFamilyUpdateRequest}
				onClose={() => setShowAddChildDialog(false)}
				currentFamilyData={getSectionData(data, 'family')}
			/>
		</div>
	);
}
