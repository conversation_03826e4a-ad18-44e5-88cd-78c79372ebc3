import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../ui/tabs';
import { User, Users, GraduationCap, Briefcase, Phone } from 'lucide-react';
import { PersonalDetailsTab } from './tabs/PersonalDetailsTab';
import { EducationTab } from './tabs/EducationTab';
import { ExperienceTab } from './tabs/ExperienceTab';
import { FamilyTab } from './tabs/FamilyTab';
import { ContactTab } from './tabs/ContactTab';

export function ProfileTabs({ data, onEditSection, onAddChild }) {
	const {
		personalDetails = {},
		education = [],
		family = {},
		experience = [],
		contact = [],
		nationality,
	} = data;

	return (
		<Tabs defaultValue="personal" className="w-full">
			<TabsList className="grid grid-cols-5 mb-8">
				<TabsTrigger value="personal" className="flex items-center gap-2">
					<User className="h-4 w-4" />
					<span className="hidden sm:inline">Personal</span>
				</TabsTrigger>
				<TabsTrigger value="family" className="flex items-center gap-2">
					<Users className="h-4 w-4" />
					<span className="hidden sm:inline">Family</span>
				</TabsTrigger>
				<TabsTrigger value="education" className="flex items-center gap-2">
					<GraduationCap className="h-4 w-4" />
					<span className="hidden sm:inline">Education</span>
				</TabsTrigger>
				<TabsTrigger value="experience" className="flex items-center gap-2">
					<Briefcase className="h-4 w-4" />
					<span className="hidden sm:inline">Experience</span>
				</TabsTrigger>
				<TabsTrigger value="contact" className="flex items-center gap-2">
					<Phone className="h-4 w-4" />
					<span className="hidden sm:inline">Contact</span>
				</TabsTrigger>
			</TabsList>

			{/* Personal Details Tab */}
			<TabsContent value="personal">
				<PersonalDetailsTab
					personalDetails={personalDetails}
					onEditSection={onEditSection}
					nationality={nationality}
				/>
			</TabsContent>

			{/* Education Tab */}
			<TabsContent value="education">
				<EducationTab education={education} onEditSection={onEditSection} />
			</TabsContent>

			{/* Experience Tab */}
			<TabsContent value="experience">
				<ExperienceTab experience={experience} onEditSection={onEditSection} />
			</TabsContent>

			{/* Family Tab */}
			<TabsContent value="family">
				<FamilyTab
					family={family}
					onEditSection={onEditSection}
					onAddChild={onAddChild}
				/>
			</TabsContent>

			{/* Contact Tab */}
			<TabsContent value="contact">
				<ContactTab contact={contact} onEditSection={onEditSection} />
			</TabsContent>
		</Tabs>
	);
}
