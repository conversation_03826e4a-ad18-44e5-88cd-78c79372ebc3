import { useState } from 'react';
import { Button } from '../../ui/button';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '../../ui/select';
import { Plus, Pencil } from 'lucide-react';
import { formatDate, capitalize } from '../utils/profileUtils';

// Helper component for managing multiple entries (education, experience, contact)
export function MultipleEntriesManager({
	section,
	entries,
	onEntriesChange,
	form,
}) {
	const [editingIndex, setEditingIndex] = useState(null);
	const [showAddForm, setShowAddForm] = useState(false);

	const getEmptyEntry = () => {
		switch (section) {
			case 'education':
				return {
					instituteName: '',
					qualification: '',
					startDate: '',
					endDate: '',
					grade: '',
				};
			case 'experience':
				return {
					companyName: '',
					designation: '',
					periodFrom: '',
					periodTo: '',
					location: '',
					reasonForLeaving: '',
				};
			case 'contact':
				return {
					name: '',
					relationship: '',
					countryDialCode: '+65',
					phone: '',
					email: '',
					type: '',
				};
			default:
				return {};
		}
	};

	const addEntry = () => {
		const newEntry = getEmptyEntry();
		const updatedEntries = [...entries, newEntry];
		onEntriesChange(updatedEntries);
		setEditingIndex(updatedEntries.length - 1);
		setShowAddForm(true);
	};

	const updateEntry = (index, updatedEntry) => {
		const updatedEntries = [...entries];
		updatedEntries[index] = updatedEntry;
		onEntriesChange(updatedEntries);
	};

	const removeEntry = (index) => {
		const updatedEntries = entries.filter((_, i) => i !== index);
		onEntriesChange(updatedEntries);
		setEditingIndex(null);
		setShowAddForm(false);
	};

	const renderEntryForm = (entry, index) => {
		switch (section) {
			case 'education':
				return (
					<div className="space-y-4 p-4 border rounded-md">
						<div className="flex justify-between items-center">
							<h5 className="font-medium">
								{index === entries.length - 1 && showAddForm
									? 'Add New Education'
									: 'Edit Education'}
							</h5>
							<div className="flex gap-2">
								<Button
									type="button"
									size="sm"
									onClick={() => {
										setEditingIndex(null);
										setShowAddForm(false);
									}}
								>
									Cancel
								</Button>
								{index !== entries.length - 1 && (
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeEntry(index)}
									>
										Remove
									</Button>
								)}
							</div>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Label className="text-sm font-medium">Institution Name</Label>
								<Input
									value={entry.instituteName}
									onChange={(e) =>
										updateEntry(index, {
											...entry,
											instituteName: e.target.value,
										})
									}
									placeholder="Enter institution name"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Qualification</Label>
								<Select
									value={entry.qualification}
									onValueChange={(value) =>
										updateEntry(index, { ...entry, qualification: value })
									}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select qualification" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="UNDER_GRADUATE">
											Under Graduate
										</SelectItem>
										<SelectItem value="POST_GRADUATE">Post Graduate</SelectItem>
										<SelectItem value="NO_FORMAL_EDUCATION">
											No Formal Education
										</SelectItem>
									</SelectContent>
								</Select>
							</div>
							<div>
								<Label className="text-sm font-medium">Start Date</Label>
								<Input
									type="date"
									value={entry.startDate.split('T')[0]}
									onChange={(e) =>
										updateEntry(index, { ...entry, startDate: e.target.value })
									}
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">End Date</Label>
								<Input
									type="date"
									value={entry.endDate.split('T')[0]}
									onChange={(e) =>
										updateEntry(index, { ...entry, endDate: e.target.value })
									}
								/>
							</div>
							<div className="md:col-span-2">
								<Label className="text-sm font-medium">Grade (Optional)</Label>
								<Input
									value={entry.grade || ''}
									onChange={(e) =>
										updateEntry(index, { ...entry, grade: e.target.value })
									}
									placeholder="Enter grade/GPA"
								/>
							</div>
						</div>
					</div>
				);
			case 'experience':
				return (
					<div className="space-y-4 p-4 border rounded-md">
						<div className="flex justify-between items-center">
							<h5 className="font-medium">
								{index === entries.length - 1 && showAddForm
									? 'Add New Experience'
									: 'Edit Experience'}
							</h5>
							<div className="flex gap-2">
								<Button
									type="button"
									size="sm"
									onClick={() => {
										setEditingIndex(null);
										setShowAddForm(false);
									}}
								>
									Cancel
								</Button>
								{index !== entries.length - 1 && (
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeEntry(index)}
									>
										Remove
									</Button>
								)}
							</div>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Label className="text-sm font-medium">Company Name</Label>
								<Input
									value={entry.companyName}
									onChange={(e) =>
										updateEntry(index, {
											...entry,
											companyName: e.target.value,
										})
									}
									placeholder="Enter company name"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Job Title</Label>
								<Input
									value={entry.designation}
									onChange={(e) =>
										updateEntry(index, {
											...entry,
											designation: e.target.value,
										})
									}
									placeholder="Enter job title"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Start Date</Label>
								<Input
									type="date"
									value={entry.periodFrom.split('T')[0]}
									onChange={(e) =>
										updateEntry(index, { ...entry, periodFrom: e.target.value })
									}
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">End Date</Label>
								<Input
									type="date"
									value={entry.periodTo.split('T')[0]}
									onChange={(e) =>
										updateEntry(index, { ...entry, periodTo: e.target.value })
									}
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Location</Label>
								<Input
									value={entry.location}
									onChange={(e) =>
										updateEntry(index, { ...entry, location: e.target.value })
									}
									placeholder="Enter location"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">
									Reason for Leaving (Optional)
								</Label>
								<Input
									value={entry.reasonForLeaving || ''}
									onChange={(e) =>
										updateEntry(index, {
											...entry,
											reasonForLeaving: e.target.value,
										})
									}
									placeholder="Enter reason for leaving"
								/>
							</div>
						</div>
					</div>
				);
			case 'contact':
				return (
					<div className="space-y-4 p-4 border rounded-md">
						<div className="flex justify-between items-center">
							<h5 className="font-medium">
								{index === entries.length - 1 && showAddForm
									? 'Add New Contact'
									: 'Edit Contact'}
							</h5>
							<div className="flex gap-2">
								<Button
									type="button"
									size="sm"
									onClick={() => {
										setEditingIndex(null);
										setShowAddForm(false);
									}}
								>
									Cancel
								</Button>
								{index !== entries.length - 1 && (
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeEntry(index)}
									>
										Remove
									</Button>
								)}
							</div>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Label className="text-sm font-medium">Contact Name</Label>
								<Input
									value={entry.name}
									onChange={(e) =>
										updateEntry(index, { ...entry, name: e.target.value })
									}
									placeholder="Enter contact name"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Relationship</Label>
								<Select
									value={entry.relationship}
									onValueChange={(value) =>
										updateEntry(index, { ...entry, relationship: value })
									}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select relationship" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="Spouse">Spouse</SelectItem>
										<SelectItem value="Parent">Parent</SelectItem>
										<SelectItem value="Child">Child</SelectItem>
										<SelectItem value="Sibling">Sibling</SelectItem>
										<SelectItem value="Friend">Friend</SelectItem>
										<SelectItem value="Other">Other</SelectItem>
									</SelectContent>
								</Select>
							</div>
							<div>
								<Label className="text-sm font-medium">Country Code</Label>
								<Input
									value={entry.countryDialCode}
									onChange={(e) =>
										updateEntry(index, {
											...entry,
											countryDialCode: e.target.value,
										})
									}
									placeholder="+65"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Phone Number</Label>
								<Input
									value={entry.phone}
									onChange={(e) =>
										updateEntry(index, { ...entry, phone: e.target.value })
									}
									placeholder="Enter phone number"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Email (Optional)</Label>
								<Input
									type="email"
									value={entry.email || ''}
									onChange={(e) =>
										updateEntry(index, { ...entry, email: e.target.value })
									}
									placeholder="Enter email address"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Contact Type</Label>
								<Select
									value={entry.type}
									onValueChange={(value) =>
										updateEntry(index, { ...entry, type: value })
									}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select contact type" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="emergency">Emergency</SelectItem>
										<SelectItem value="reference">Reference</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>
					</div>
				);
			default:
				return null;
		}
	};

	const renderEntryDisplay = (entry, index) => {
		switch (section) {
			case 'education':
				return (
					<div key={index} className="p-3 border rounded-md">
						<div className="flex justify-between items-start">
							<div className="space-y-1">
								<h5 className="font-medium">{entry.instituteName}</h5>
								<p className="text-sm text-muted-foreground">
									{entry.qualification} • {formatDate(entry.startDate)} -{' '}
									{formatDate(entry.endDate)}
								</p>
								{entry.grade && <p className="text-sm">Grade: {entry.grade}</p>}
							</div>
							<Button
								type="button"
								size="sm"
								variant="outline"
								onClick={() => setEditingIndex(index)}
							>
								<Pencil className="h-4 w-4" />
							</Button>
						</div>
					</div>
				);
			case 'experience':
				return (
					<div key={index} className="p-3 border rounded-md">
						<div className="flex justify-between items-start">
							<div className="space-y-1">
								<h5 className="font-medium">{entry.companyName}</h5>
								<p className="text-sm font-medium">{entry.designation}</p>
								<p className="text-sm text-muted-foreground">
									{formatDate(entry.periodFrom)} - {formatDate(entry.periodTo)}{' '}
									• {entry.location}
								</p>
								{entry.reasonForLeaving && (
									<p className="text-xs text-muted-foreground">
										Reason: {entry.reasonForLeaving}
									</p>
								)}
							</div>
							<Button
								type="button"
								size="sm"
								variant="outline"
								onClick={() => setEditingIndex(index)}
							>
								<Pencil className="h-4 w-4" />
							</Button>
						</div>
					</div>
				);
			case 'contact':
				return (
					<div key={index} className="p-3 border rounded-md">
						<div className="flex justify-between items-start">
							<div className="space-y-1">
								<h5 className="font-medium">{entry.name}</h5>
								<p className="text-sm text-muted-foreground">
									{entry.relationship} • {capitalize(entry.type)}
								</p>
								<p className="text-sm">
									{entry.countryDialCode} {entry.phone}
								</p>
								{entry.email && (
									<p className="text-sm text-muted-foreground">{entry.email}</p>
								)}
							</div>
							<Button
								type="button"
								size="sm"
								variant="outline"
								onClick={() => setEditingIndex(index)}
							>
								<Pencil className="h-4 w-4" />
							</Button>
						</div>
					</div>
				);
			default:
				return null;
		}
	};

	return (
		<div className="space-y-4">
			<div className="flex justify-between items-center">
				<h4 className="text-sm font-medium capitalize">{section} Entries</h4>
				<Button
					type="button"
					size="sm"
					onClick={addEntry}
					className="flex items-center gap-2"
				>
					<Plus className="h-4 w-4" />
					Add {section}
				</Button>
			</div>

			{entries.length === 0 ? (
				<p className="text-sm text-muted-foreground italic">
					No {section} entries added yet
				</p>
			) : (
				<div className="space-y-3">
					{entries.map((entry, index) =>
						editingIndex === index
							? renderEntryForm(entry, index)
							: renderEntryDisplay(entry, index)
					)}
				</div>
			)}
		</div>
	);
}
