import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '../../ui/card';
import { Phone, Mail, ClipboardList, Pencil } from 'lucide-react';
import { capitalize } from '../utils/profileUtils';

export function ContactTab({ contact, onEditSection }) {
	return (
		<Card>
			<CardHeader>
				<div className="flex justify-between items-center">
					<div>
						<CardTitle className="flex items-center gap-2">
							<Phone className="h-5 w-5 text-indigo-600" />
							Contact Information
						</CardTitle>
						<CardDescription>
							Your emergency contact information. Click edit to request changes.
						</CardDescription>
					</div>
					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() => onEditSection('contact')}
							className="flex items-center self-start gap-2"
						>
							<Pencil className="h-4 w-4" />
							Edit Section
						</Button>
					</div>
				</div>
			</CardHeader>
			<CardContent className="pt-6">
				{contact && contact.length > 0 ? (
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						{contact.map((cont) => (
							<div key={cont._id} className="p-4 rounded-lg border">
								<h3 className="font-semibold text-lg mb-4">{cont.name}</h3>
								<div className="space-y-3">
									<Badge className="bg-indigo-100 text-indigo-800">
										{cont.relationship}
									</Badge>
									<div className="flex items-center gap-2">
										<Phone className="h-4 w-4 text-muted-foreground" />
										<span>
											{cont.countryDialCode} {cont.phone}
										</span>
									</div>
									<div className="flex items-center gap-2">
										<Mail className="h-4 w-4 text-muted-foreground" />
										<span>{cont.email}</span>
									</div>
									<div className="flex items-center gap-2">
										<ClipboardList className="h-4 w-4 text-muted-foreground" />
										<span>{capitalize(cont.type)}</span>
									</div>
								</div>
							</div>
						))}
					</div>
				) : (
					<p className="text-muted-foreground">
						No contact information available.
					</p>
				)}
			</CardContent>
		</Card>
	);
}
