import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '../../ui/card';
import { GraduationCap, Award, Pencil } from 'lucide-react';
import { formatDate, calculateDuration } from '../utils/profileUtils';

export function EducationTab({ education, onEditSection }) {
	return (
		<Card>
			<CardHeader>
				<div className="flex justify-between items-center">
					<div>
						<CardTitle className="flex items-center gap-2">
							<GraduationCap className="h-5 w-5 text-indigo-600" />
							Educational Background
						</CardTitle>
						<CardDescription>
							Your educational history. Click edit to request changes.
						</CardDescription>
					</div>
					<Button
						variant="outline"
						size="sm"
						onClick={() => onEditSection('education')}
						className="flex items-center self-start gap-2"
					>
						<Pencil className="h-4 w-4" />
						Edit Section
					</Button>
				</div>
			</CardHeader>
			<CardContent className="pt-6">
				{education && education.length > 0 ? (
					<div className="space-y-6">
						{education.map((edu) => (
							<div
								key={edu._id}
								className="relative pl-8 border-l-2 border-indigo-200 pb-6"
							>
								<div className="absolute -left-3 top-0 bg-indigo-600 rounded-full p-1">
									<GraduationCap className="h-4 w-4 text-white" />
								</div>
								<div className="mb-2">
									<Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
										{formatDate(edu.startDate)} - {formatDate(edu.endDate)}
									</Badge>
									<Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-200">
										{calculateDuration(edu.startDate, edu.endDate)}
									</Badge>
								</div>
								<h3 className="text-xl font-bold">{edu.instituteName}</h3>
								<p className="text-muted-foreground">
									{edu.qualification.replace('_', ' ')}
								</p>
								<div className="mt-2 flex items-center">
									<Award className="h-4 w-4 text-amber-500 mr-1" />
									<span className="font-medium">Grade: {edu.grade}</span>
								</div>
							</div>
						))}
					</div>
				) : (
					<p className="text-muted-foreground">
						No education information available.
					</p>
				)}
			</CardContent>
		</Card>
	);
}
