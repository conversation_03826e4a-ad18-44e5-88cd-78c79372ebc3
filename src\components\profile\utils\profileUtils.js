// Helper function to format dates
export const formatDate = (dateString) => {
	if (!dateString) return 'N/A';
	try {
		const date = new Date(dateString);
		return date.toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
		});
	} catch (error) {
		return 'Invalid Date';
	}
};

// Helper function to calculate duration
export const calculateDuration = (startDate, endDate) => {
	const start = new Date(startDate);
	const end = new Date(endDate);
	const yearDiff = end.getFullYear() - start.getFullYear();
	const monthDiff = end.getMonth() - start.getMonth();

	let years = yearDiff;
	let months = monthDiff;

	if (monthDiff < 0) {
		years--;
		months += 12;
	}

	return `${years} years${months > 0 ? `, ${months} months` : ''}`;
};

// Helper function to capitalize words
export const capitalize = (str) => {
	if (!str) return '';
	return str
		.split(' ')
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
		.join(' ');
};

// Helper function to format time ago
export const timeAgo = (dateString) => {
	const date = new Date(dateString);
	const now = new Date();
	const seconds = Math.floor((now - date) / 1000);

	let interval = Math.floor(seconds / 31536000);
	if (interval >= 1) {
		return interval === 1 ? '1 year ago' : `${interval} years ago`;
	}

	interval = Math.floor(seconds / 2592000);
	if (interval >= 1) {
		return interval === 1 ? '1 month ago' : `${interval} months ago`;
	}

	interval = Math.floor(seconds / 86400);
	if (interval >= 1) {
		return interval === 1 ? '1 day ago' : `${interval} days ago`;
	}

	interval = Math.floor(seconds / 3600);
	if (interval >= 1) {
		return interval === 1 ? '1 hour ago' : `${interval} hours ago`;
	}

	interval = Math.floor(seconds / 60);
	if (interval >= 1) {
		return interval === 1 ? '1 minute ago' : `${interval} minutes ago`;
	}

	return 'Just now';
};

// Helper function to format data for display
export const formatDataForDisplay = (data) => {
	if (!data || typeof data !== 'object') return data || 'N/A';

	// If it's an object with a single key-value pair, return just the value
	const keys = Object.keys(data);
	if (keys.length === 1) {
		return data[keys[0]] || 'N/A';
	}

	// For complex objects, format as key: value pairs
	return keys.map((key) => `${key}: ${data[key]}`).join(', ');
};

// Helper function to format field values
export const formatFieldValue = (value) => {
	if (value === null || value === undefined) return 'N/A';
	if (typeof value === 'boolean') return value ? 'Yes' : 'No';
	if (typeof value === 'string' && value.includes('T') && value.includes('Z')) {
		// Likely an ISO date string
		return formatDate(value);
	}
	if (Array.isArray(value)) return `${value.length} items`;
	if (typeof value === 'object') return 'Complex data';
	return String(value);
};

// Helper function to get section data
export const getSectionData = (data, section) => {
	const {
		personalDetails = {},
		education = [],
		family = {},
		experience = [],
		contact = [],
	} = data;

	switch (section) {
		case 'personal':
			return personalDetails;
		case 'family':
			return family;
		case 'education':
			return education;
		case 'experience':
			return experience;
		case 'contact':
			return contact;
		default:
			return {};
	}
};
