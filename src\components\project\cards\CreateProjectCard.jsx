'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * CreateProjectCard Component
 * Displays a card for creating new projects with consistent styling
 */
export const CreateProjectCard = ({ onClick }) => {
	return (
		<Card
			className={cn(
				'group cursor-pointer transition-all duration-300',
				'hover:shadow-xl hover:scale-[1.02]',
				'border-dashed border-2 hover:border-primary/50',
				'aspect-video', // 16:9 aspect ratio to match other cards
				'flex items-center justify-center'
			)}
			onClick={onClick}
		>
			<CardContent className="flex flex-col items-center justify-center p-6 text-center">
				<div className="rounded-full bg-primary/10 flex items-center justify-center mb-3 group-hover:bg-primary/20 transition-colors">
					<Plus className="h-6 w-6 text-primary" />
				</div>
				<h3 className="font-semibold mb-1">Create New Project</h3>
				<p className="text-sm text-muted-foreground">
					Start organizing your tasks
				</p>
			</CardContent>
		</Card>
	);
};
