/**
 * Background images data for project cards and kanban boards
 * Using high-resolution landscape images (1920x1080, 16:9 ratio) for optimal quality
 */

import coverImage1 from '@/assets/cover-images/IMG_2662.jpeg';
import coverImage2 from '@/assets/cover-images/IMG_2641.jpeg';
import coverImage3 from '@/assets/cover-images/IMG_2640.jpeg';
import coverImage4 from '@/assets/cover-images/IMG_2504.jpeg';
import coverImage5 from '@/assets/cover-images/IMG_2496.jpeg';
import coverImage6 from '@/assets/cover-images/IMG_2493.jpeg';
import coverImage7 from '@/assets/cover-images/IMG_2491.jpeg';
import coverImage8 from '@/assets/cover-images/IMG_2472.jpeg';
import coverImage9 from '@/assets/cover-images/IMG_2469.jpeg';
import coverImage10 from '@/assets/cover-images/IMG_2467.jpeg';
import coverImage11 from '@/assets/cover-images/IMG_2466.jpeg';
import coverImage12 from '@/assets/cover-images/IMG_2442.jpeg';
import coverImage13 from '@/assets/cover-images/IMG_2441.jpeg';
import coverImage14 from '@/assets/cover-images/IMG_2440.jpeg';
import coverImage15 from '@/assets/cover-images/IMG_2439.jpeg';
import coverImage16 from '@/assets/cover-images/IMG_2435.jpeg';
import coverImage17 from '@/assets/cover-images/IMG_2434.jpeg';
import coverImage18 from '@/assets/cover-images/IMG_2013.jpeg';
import coverImage19 from '@/assets/cover-images/IMG_1860.jpeg';
import coverImage20 from '@/assets/cover-images/IMG_1781.jpeg';

export const backgroundImages = [
	{
		id: 1,
		name: 'By the Sea',
		url: 'https://picsum.photos/id/77/3840/2160',
		thumbnail: 'https://picsum.photos/id/77/400/225',
	},
	{
		id: 2,
		name: 'Blossoming Violets',
		url: 'https://picsum.photos/id/82/3840/2160',
		thumbnail: 'https://picsum.photos/id/82/400/225',
	},
	{
		id: 3,
		name: 'Snowy Mountain',
		url: 'https://picsum.photos/id/256/3840/2160',
		thumbnail: 'https://picsum.photos/id/256/400/225',
	},
	{
		id: 4,
		name: 'Rustic Cart',
		url: 'https://picsum.photos/id/99/3840/2160',
		thumbnail: 'https://picsum.photos/id/99/400/225',
	},
	{
		id: 5,
		name: 'Meadow Barley',
		url: 'https://picsum.photos/id/98/3840/2160',
		thumbnail: 'https://picsum.photos/id/98/400/225',
	},
	{
		id: 6,
		name: 'Misty Morning Trees',
		url: 'https://picsum.photos/id/95/3840/2160',
		thumbnail: 'https://picsum.photos/id/95/400/225',
	},
	{
		id: 7,
		name: 'Whispers of Light',
		url: 'https://picsum.photos/id/94/3840/2160',
		thumbnail: 'https://picsum.photos/id/94/400/225',
	},
	{
		id: 8,
		name: 'Sunset Meadow',
		url: 'https://picsum.photos/id/93/3840/2160',
		thumbnail: 'https://picsum.photos/id/93/400/225',
	},
	{
		id: 9,
		name: 'Urban Bridge',
		url: 'https://picsum.photos/id/84/3840/2160',
		thumbnail: 'https://picsum.photos/id/84/400/225',
	},
	{
		id: 10,
		name: 'Rustic Cabin',
		url: 'https://picsum.photos/id/76/3840/2160',
		thumbnail: 'https://picsum.photos/id/76/400/225',
	},
	{
		id: 11,
		name: 'City Skyline',
		url: 'https://picsum.photos/id/74/3840/2160',
		thumbnail: 'https://picsum.photos/id/74/400/225',
	},
	{
		id: 12,
		name: 'Whitby Pier',
		url: 'https://picsum.photos/id/68/3840/2160',
		thumbnail: 'https://picsum.photos/id/68/400/225',
	},
	{
		id: 13,
		name: 'Salzburg Mountains',
		url: 'https://picsum.photos/id/61/3840/2160',
		thumbnail: 'https://picsum.photos/id/61/400/225',
	},
	{
		id: 14,
		name: 'Canyon Landscape',
		url: 'https://picsum.photos/id/46/3840/2160',
		thumbnail: 'https://picsum.photos/id/46/400/225',
	},
	{
		id: 15,
		name: 'Misty Coast',
		url: 'https://picsum.photos/id/44/3840/2160',
		thumbnail: 'https://picsum.photos/id/44/400/225',
	},
	{
		id: 16,
		name: 'Forest Creek',
		url: 'https://picsum.photos/id/28/3840/2160',
		thumbnail: 'https://picsum.photos/id/28/400/225',
	},
	{
		id: 17,
		name: 'Rays of Hope',
		url: 'https://picsum.photos/id/25/3840/2160',
		thumbnail: 'https://picsum.photos/id/25/400/225',
	},
	{
		id: 18,
		name: 'Grassy Forest Edge',
		url: 'https://picsum.photos/id/17/3840/2160',
		thumbnail: 'https://picsum.photos/id/17/400/225',
	},
	{
		id: 19,
		name: 'Calm Coast',
		url: 'https://picsum.photos/id/13/3840/2160',
		thumbnail: 'https://picsum.photos/id/13/400/225',
	},
	{
		id: 20,
		name: 'Forest Waterfall',
		url: 'https://picsum.photos/id/15/3840/2160',
		thumbnail: 'https://picsum.photos/id/15/400/225',
	},
	{
		id: 21,
		name: 'Hillside',
		url: 'https://picsum.photos/id/198/3840/2160',
		thumbnail: 'https://picsum.photos/id/198/400/225',
	},
];

export const coverImages = [
	{
		id: 1,
		name: 'By the Sea',
		url: 'https://picsum.photos/id/77/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/77/400/225?grayscale',
	},
	{
		id: 2,
		name: 'Blossoming Violets',
		url: 'https://picsum.photos/id/82/3840/2160',
		thumbnail: 'https://picsum.photos/id/82/400/225',
	},
	{
		id: 3,
		name: 'Snowy Mountain',
		url: 'https://picsum.photos/id/256/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/256/400/225?grayscale',
	},
	{
		id: 4,
		name: 'Rustic Cart',
		url: 'https://picsum.photos/id/99/3840/2160',
		thumbnail: 'https://picsum.photos/id/99/400/225',
	},
	{
		id: 5,
		name: 'Meadow Barley',
		url: 'https://picsum.photos/id/98/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/98/400/225?grayscale',
	},
	{
		id: 6,
		name: 'Misty Morning Trees',
		url: 'https://picsum.photos/id/95/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/95/400/225?grayscale',
	},
	{
		id: 7,
		name: 'Whispers of Light',
		url: 'https://picsum.photos/id/94/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/94/400/225?grayscale',
	},
	{
		id: 8,
		name: 'Sunset Meadow',
		url: 'https://picsum.photos/id/93/3840/2160',
		thumbnail: 'https://picsum.photos/id/93/400/225',
	},
	{
		id: 9,
		name: 'Urban Bridge',
		url: 'https://picsum.photos/id/84/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/84/400/225?grayscale',
	},
	{
		id: 10,
		name: 'Rustic Cabin',
		url: 'https://picsum.photos/id/76/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/76/400/225?grayscale',
	},
	{
		id: 11,
		name: 'City Skyline',
		url: 'https://picsum.photos/id/74/3840/2160',
		thumbnail: 'https://picsum.photos/id/74/400/225',
	},
	{
		id: 12,
		name: 'Whitby Pier',
		url: 'https://picsum.photos/id/68/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/68/400/225?grayscale',
	},
	{
		id: 13,
		name: 'Salzburg Mountains',
		url: 'https://picsum.photos/id/61/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/61/400/225?grayscale',
	},
	{
		id: 14,
		name: 'Canyon Landscape',
		url: 'https://picsum.photos/id/46/3840/2160',
		thumbnail: 'https://picsum.photos/id/46/400/225',
	},
	{
		id: 15,
		name: 'Misty Coast',
		url: 'https://picsum.photos/id/44/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/44/400/225?grayscale',
	},
	{
		id: 16,
		name: 'Forest Creek',
		url: 'https://picsum.photos/id/28/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/28/400/225?grayscale',
	},
	{
		id: 17,
		name: 'Rays of Hope',
		url: 'https://picsum.photos/id/25/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/25/400/225?grayscale',
	},
	{
		id: 18,
		name: 'Grassy Forest Edge',
		url: 'https://picsum.photos/id/17/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/17/400/225?grayscale',
	},
	{
		id: 19,
		name: 'Calm Coast',
		url: 'https://picsum.photos/id/13/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/13/400/225?grayscale',
	},
	{
		id: 20,
		name: 'Forest Waterfall',
		url: 'https://picsum.photos/id/15/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/15/400/225?grayscale',
	},
	{
		id: 21,
		name: 'Hillside',
		url: 'https://picsum.photos/id/198/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/198/400/225?grayscale',
	},
];

export const storedCoverImages = [
	{
		id: 1,
		name: 'Cover Image 1',
		url: coverImage1,
	},
	// {
	// 	id: 2,
	// 	name: 'Cover Image 2',
	// 	url: coverImage2,
	// },
	// {
	// 	id: 3,
	// 	name: 'Cover Image 3',
	// 	url: coverImage3,
	// },
	{
		id: 4,
		name: 'Cover Image 4',
		url: coverImage4,
	},
	{
		id: 5,
		name: 'Cover Image 5',
		url: coverImage5,
	},
	{
		id: 6,
		name: 'Cover Image 6',
		url: coverImage6,
	},
	{
		id: 7,
		name: 'Cover Image 7',
		url: coverImage7,
	},
	{
		id: 8,
		name: 'Cover Image 8',
		url: coverImage8,
	},
	{
		id: 9,
		name: 'Cover Image 9',
		url: coverImage9,
	},
	{
		id: 10,
		name: 'Cover Image 10',
		url: coverImage10,
	},
	{
		id: 11,
		name: 'Cover Image 11',
		url: coverImage11,
	},
	{
		id: 12,
		name: 'Cover Image 12',
		url: coverImage12,
	},
	{
		id: 13,
		name: 'Cover Image 13',
		url: coverImage13,
	},
	{
		id: 14,
		name: 'Cover Image 14',
		url: coverImage14,
	},
	// {
	// 	id: 15,
	// 	name: 'Cover Image 15',
	// 	url: coverImage15,
	// },
	{
		id: 16,
		name: 'Cover Image 16',
		url: coverImage16,
	},
	{
		id: 17,
		name: 'Cover Image 17',
		url: coverImage17,
	},
	// {
	// 	id: 18,
	// 	name: 'Cover Image 18',
	// 	url: coverImage18,
	// },
	{
		id: 19,
		name: 'Cover Image 19',
		url: coverImage19,
	},
	// {
	// 	id: 20,
	// 	name: 'Cover Image 20',
	// 	url: coverImage20,
	// },
];

/**
 * Get background image by ID
 * @param {number} id - Image ID
 * @returns {object|null} Background image object or null if not found
 */
export const getBackgroundImageById = (id) => {
	return backgroundImages.find((image) => image.id === id) || null;
};
