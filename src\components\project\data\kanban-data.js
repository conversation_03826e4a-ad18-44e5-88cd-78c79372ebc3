/**
 * Kanban board data structure and sample data
 * Following Trello-inspired design system specifications
 */

export const kanbanStatuses = [
	{ id: 'backlog', title: 'Backlog', color: '#94a3b8' },
	{ id: 'todo', title: 'To Do', color: '#3b82f6' },
	{ id: 'in-progress', title: 'In Progress', color: '#f59e0b' },
	{ id: 'review', title: 'Review', color: '#8b5cf6' },
	{ id: 'done', title: 'Done', color: '#10b981' },
];

export const kanbanPriorities = [
	{ id: 'low', label: 'Low', color: '#10b981', bgColor: '#dcfce7' },
	{ id: 'medium', label: 'Medium', color: '#f59e0b', bgColor: '#fef3c7' },
	{ id: 'high', label: 'High', color: '#ef4444', bgColor: '#fee2e2' },
	{ id: 'urgent', label: 'Urgent', color: '#dc2626', bgColor: '#fecaca' },
];

export const sampleKanbanData = {
	// Website Redesign Project (ID: 1)
	1: {
		projectId: 1,
		groups: [
			{
				_id: 'backlog',
				name: 'Backlog',
				tasks: [
					{
						_id: 'task-1',
						name: 'User Research & Analysis',
						description:
							'Conduct user interviews and analyze current website usage patterns',
						priority: 'medium',
						assignees: [
							{ id: 1, name: 'John Doe', initials: 'JD', avatar: null },
							{ id: 2, name: 'Jane Smith', initials: 'JS', avatar: null },
						],
						dueDate: '2024-02-20',
						tags: ['research', 'ux'],
						attachments: 2,
						comments: 5,
						checklist: { completed: 0, total: 8 },
						coverImage: 'https://picsum.photos/300/150?random=101',
					},
					{
						id: 'card-2',
						title: 'Competitor Analysis',
						description:
							'Research competitor websites and identify best practices',
						priority: 'low',
						assignees: [
							{ id: 3, name: 'Mike Johnson', initials: 'MJ', avatar: null },
						],
						dueDate: '2024-02-25',
						tags: ['research', 'analysis'],
						attachments: 0,
						comments: 2,
						checklist: { completed: 2, total: 5 },
					},
				],
			},
			{
				id: 'todo',
				title: 'To Do',
				cards: [
					{
						id: 'card-3',
						title: 'Homepage Wireframes',
						description:
							'Create low-fidelity wireframes for the new homepage layout',
						priority: 'high',
						assignees: [
							{ id: 2, name: 'Jane Smith', initials: 'JS', avatar: null },
						],
						dueDate: '2024-02-18',
						tags: ['design', 'wireframes'],
						attachments: 1,
						comments: 8,
						checklist: { completed: 3, total: 6 },
						coverImage: 'https://picsum.photos/300/150?random=102',
					},
					{
						id: 'card-4',
						title: 'Brand Guidelines Update',
						description:
							'Update brand guidelines to reflect new design direction',
						priority: 'medium',
						assignees: [
							{ id: 1, name: 'John Doe', initials: 'JD', avatar: null },
							{ id: 2, name: 'Jane Smith', initials: 'JS', avatar: null },
						],
						dueDate: '2024-02-22',
						tags: ['branding', 'guidelines'],
						attachments: 3,
						comments: 4,
						checklist: { completed: 1, total: 4 },
					},
				],
			},
			{
				id: 'in-progress',
				title: 'In Progress',
				cards: [
					{
						id: 'card-5',
						title: 'Navigation Menu Design',
						description: 'Design responsive navigation menu with improved UX',
						priority: 'high',
						assignees: [
							{ id: 2, name: 'Jane Smith', initials: 'JS', avatar: null },
							{ id: 3, name: 'Mike Johnson', initials: 'MJ', avatar: null },
						],
						dueDate: '2024-02-16',
						tags: ['design', 'navigation', 'responsive'],
						attachments: 4,
						comments: 12,
						checklist: { completed: 4, total: 7 },
						coverImage: 'https://picsum.photos/300/150?random=103',
					},
					{
						id: 'card-6',
						title: 'Color Palette Selection',
						description: 'Finalize color palette for the new design system',
						priority: 'medium',
						assignees: [
							{ id: 1, name: 'John Doe', initials: 'JD', avatar: null },
						],
						dueDate: '2024-02-19',
						tags: ['design', 'colors'],
						attachments: 2,
						comments: 6,
						checklist: { completed: 5, total: 5 },
					},
				],
			},
			{
				id: 'review',
				title: 'Review',
				cards: [
					{
						id: 'card-7',
						title: 'Hero Section Mockup',
						description: 'High-fidelity mockup of the homepage hero section',
						priority: 'high',
						assignees: [
							{ id: 2, name: 'Jane Smith', initials: 'JS', avatar: null },
						],
						dueDate: '2024-02-15',
						tags: ['design', 'mockup', 'hero'],
						attachments: 5,
						comments: 15,
						checklist: { completed: 8, total: 8 },
						coverImage: 'https://picsum.photos/300/150?random=104',
					},
				],
			},
			{
				id: 'done',
				title: 'Done',
				cards: [
					{
						id: 'card-8',
						title: 'Typography System',
						description: 'Define typography hierarchy and font selections',
						priority: 'medium',
						assignees: [
							{ id: 1, name: 'John Doe', initials: 'JD', avatar: null },
							{ id: 2, name: 'Jane Smith', initials: 'JS', avatar: null },
						],
						dueDate: '2024-02-10',
						tags: ['design', 'typography'],
						attachments: 3,
						comments: 9,
						checklist: { completed: 6, total: 6 },
					},
					{
						id: 'card-9',
						title: 'Logo Redesign',
						description: 'Updated company logo with modern aesthetic',
						priority: 'high',
						assignees: [
							{ id: 1, name: 'John Doe', initials: 'JD', avatar: null },
						],
						dueDate: '2024-02-08',
						tags: ['branding', 'logo'],
						attachments: 8,
						comments: 22,
						checklist: { completed: 10, total: 10 },
						coverImage: 'https://picsum.photos/300/150?random=105',
					},
				],
			},
		],
	},

	// Mobile App Development Project (ID: 2)
	2: {
		projectId: 2,
		columns: [
			{
				id: 'backlog',
				title: 'Backlog',
				cards: [
					{
						id: 'card-10',
						title: 'App Store Optimization',
						description:
							'Prepare app store listings and optimize for discovery',
						priority: 'low',
						assignees: [
							{ id: 4, name: 'Sarah Wilson', initials: 'SW', avatar: null },
						],
						dueDate: '2024-03-15',
						tags: ['marketing', 'aso'],
						attachments: 1,
						comments: 3,
						checklist: { completed: 0, total: 12 },
					},
				],
			},
			{
				id: 'todo',
				title: 'To Do',
				cards: [
					{
						id: 'card-11',
						title: 'Push Notifications Setup',
						description:
							'Implement push notification system for both iOS and Android',
						priority: 'medium',
						assignees: [
							{ id: 5, name: 'Tom Brown', initials: 'TB', avatar: null },
						],
						dueDate: '2024-03-05',
						tags: ['development', 'notifications'],
						attachments: 2,
						comments: 7,
						checklist: { completed: 2, total: 8 },
						coverImage: 'https://picsum.photos/300/150?random=106',
					},
				],
			},
			{
				id: 'in-progress',
				title: 'In Progress',
				cards: [
					{
						id: 'card-12',
						title: 'User Authentication',
						description: 'Implement secure user login and registration system',
						priority: 'high',
						assignees: [
							{ id: 4, name: 'Sarah Wilson', initials: 'SW', avatar: null },
							{ id: 5, name: 'Tom Brown', initials: 'TB', avatar: null },
						],
						dueDate: '2024-02-28',
						tags: ['development', 'security', 'auth'],
						attachments: 6,
						comments: 18,
						checklist: { completed: 6, total: 10 },
						coverImage: 'https://picsum.photos/300/150?random=107',
					},
				],
			},
			{
				id: 'review',
				title: 'Review',
				cards: [
					{
						id: 'card-13',
						title: 'UI Component Library',
						description: 'Reusable UI components for consistent design',
						priority: 'medium',
						assignees: [
							{ id: 4, name: 'Sarah Wilson', initials: 'SW', avatar: null },
						],
						dueDate: '2024-02-20',
						tags: ['design', 'components'],
						attachments: 4,
						comments: 11,
						checklist: { completed: 15, total: 18 },
					},
				],
			},
			{
				id: 'done',
				title: 'Done',
				cards: [
					{
						id: 'card-14',
						title: 'App Architecture Planning',
						description: 'Define overall app architecture and tech stack',
						priority: 'high',
						assignees: [
							{ id: 4, name: 'Sarah Wilson', initials: 'SW', avatar: null },
							{ id: 5, name: 'Tom Brown', initials: 'TB', avatar: null },
						],
						dueDate: '2024-02-05',
						tags: ['planning', 'architecture'],
						attachments: 7,
						comments: 25,
						checklist: { completed: 12, total: 12 },
						coverImage: 'https://picsum.photos/300/150?random=108',
					},
				],
			},
		],
	},

	// E-commerce Platform Project (ID: 5)
	5: {
		projectId: 5,
		columns: [
			{
				id: 'backlog',
				title: 'Backlog',
				cards: [
					{
						id: 'card-20',
						title: 'Payment Gateway Integration',
						description:
							'Integrate multiple payment gateways including Stripe, PayPal, and Apple Pay',
						priority: 'high',
						assignees: [
							{ id: 9, name: 'David Lee', initials: 'DL', avatar: null },
							{ id: 10, name: 'Maria Garcia', initials: 'MG', avatar: null },
						],
						dueDate: '2024-04-20',
						tags: ['payment', 'integration', 'security'],
						attachments: 3,
						comments: 8,
						checklist: { completed: 2, total: 12 },
						coverImage: 'https://picsum.photos/300/150?random=120',
					},
				],
			},
			{
				id: 'todo',
				title: 'To Do',
				cards: [
					{
						id: 'card-21',
						title: 'Product Catalog System',
						description:
							'Build comprehensive product catalog with categories, filters, and search',
						priority: 'high',
						assignees: [
							{ id: 11, name: 'James Wilson', initials: 'JW', avatar: null },
						],
						dueDate: '2024-04-15',
						tags: ['catalog', 'search', 'database'],
						attachments: 5,
						comments: 12,
						checklist: { completed: 1, total: 8 },
						coverImage: 'https://picsum.photos/300/150?random=121',
					},
				],
			},
			{
				id: 'in-progress',
				title: 'In Progress',
				cards: [
					{
						id: 'card-22',
						title: 'Shopping Cart & Checkout',
						description:
							'Implement shopping cart functionality with secure checkout process',
						priority: 'high',
						assignees: [
							{ id: 9, name: 'David Lee', initials: 'DL', avatar: null },
							{ id: 10, name: 'Maria Garcia', initials: 'MG', avatar: null },
						],
						dueDate: '2024-04-10',
						tags: ['cart', 'checkout', 'ux'],
						attachments: 7,
						comments: 20,
						checklist: { completed: 8, total: 15 },
						coverImage: 'https://picsum.photos/300/150?random=122',
					},
				],
			},
			{
				id: 'review',
				title: 'Review',
				cards: [
					{
						id: 'card-23',
						title: 'User Authentication System',
						description:
							'Complete user registration, login, and profile management',
						priority: 'medium',
						assignees: [
							{ id: 11, name: 'James Wilson', initials: 'JW', avatar: null },
						],
						dueDate: '2024-04-08',
						tags: ['auth', 'security', 'user-management'],
						attachments: 4,
						comments: 15,
						checklist: { completed: 10, total: 12 },
					},
				],
			},
			{
				id: 'done',
				title: 'Done',
				cards: [
					{
						id: 'card-24',
						title: 'Database Schema Design',
						description:
							'Complete database schema for products, users, orders, and inventory',
						priority: 'high',
						assignees: [
							{ id: 9, name: 'David Lee', initials: 'DL', avatar: null },
						],
						dueDate: '2024-03-25',
						tags: ['database', 'schema', 'planning'],
						attachments: 8,
						comments: 25,
						checklist: { completed: 15, total: 15 },
						coverImage: 'https://picsum.photos/300/150?random=123',
					},
				],
			},
		],
	},

	// Database Migration Project (ID: 3)
	3: {
		projectId: 3,
		columns: [
			{
				id: 'backlog',
				title: 'Backlog',
				cards: [],
			},
			{
				id: 'todo',
				title: 'To Do',
				cards: [],
			},
			{
				id: 'in-progress',
				title: 'In Progress',
				cards: [],
			},
			{
				id: 'review',
				title: 'Review',
				cards: [],
			},
			{
				id: 'done',
				title: 'Done',
				cards: [
					{
						id: 'card-25',
						title: 'Legacy Data Analysis',
						description:
							'Complete analysis of legacy database structure and data integrity',
						priority: 'high',
						assignees: [
							{ id: 6, name: 'Alex Chen', initials: 'AC', avatar: null },
							{ id: 7, name: 'Lisa Park', initials: 'LP', avatar: null },
						],
						dueDate: '2024-01-15',
						tags: ['analysis', 'legacy', 'data'],
						attachments: 12,
						comments: 30,
						checklist: { completed: 20, total: 20 },
						coverImage: 'https://picsum.photos/300/150?random=124',
					},
					{
						id: 'card-26',
						title: 'Cloud Infrastructure Setup',
						description:
							'Set up new cloud database infrastructure with proper security and backups',
						priority: 'high',
						assignees: [
							{ id: 6, name: 'Alex Chen', initials: 'AC', avatar: null },
						],
						dueDate: '2024-01-20',
						tags: ['cloud', 'infrastructure', 'security'],
						attachments: 8,
						comments: 18,
						checklist: { completed: 12, total: 12 },
					},
					{
						id: 'card-27',
						title: 'Data Migration Scripts',
						description:
							'Develop and test comprehensive data migration scripts',
						priority: 'high',
						assignees: [
							{ id: 7, name: 'Lisa Park', initials: 'LP', avatar: null },
						],
						dueDate: '2024-01-25',
						tags: ['migration', 'scripts', 'testing'],
						attachments: 15,
						comments: 22,
						checklist: { completed: 18, total: 18 },
						coverImage: 'https://picsum.photos/300/150?random=125',
					},
					{
						id: 'card-28',
						title: 'Production Migration',
						description:
							'Execute final production migration with zero downtime',
						priority: 'urgent',
						assignees: [
							{ id: 6, name: 'Alex Chen', initials: 'AC', avatar: null },
							{ id: 7, name: 'Lisa Park', initials: 'LP', avatar: null },
						],
						dueDate: '2024-01-30',
						tags: ['production', 'migration', 'critical'],
						attachments: 5,
						comments: 35,
						checklist: { completed: 25, total: 25 },
					},
				],
			},
		],
	},
};

/**
 * Create default kanban structure for a project
 * @param {number} projectId - Project ID
 * @returns {object} Default kanban structure
 */
const createDefaultKanbanData = (projectId) => {
	return {
		projectId,
		groups: [
			{
				_id: 'backlog',
				name: 'Backlog',
				tasks: [],
			},
			{
				_id: 'todo',
				name: 'To Do',
				tasks: [],
			},
			{
				_id: 'in-progress',
				name: 'In Progress',
				tasks: [],
			},
			{
				_id: 'review',
				name: 'Review',
				tasks: [],
			},
			{
				_id: 'done',
				name: 'Done',
				tasks: [],
			},
		],
	};
};

/**
 * Get kanban data for a specific project
 * @param {number} projectId - Project ID
 * @returns {object} Kanban data (existing or default structure)
 */
export const getKanbanDataByProjectId = (projectId) => {
	return sampleKanbanData[projectId] || createDefaultKanbanData(projectId);
};

/**
 * Get priority configuration by ID
 * @param {string} priorityId - Priority ID
 * @returns {object|null} Priority configuration or null if not found
 */
export const getPriorityById = (priorityId) => {
	return (
		kanbanPriorities.find((priority) => priority.id === priorityId) || null
	);
};

/**
 * Get status configuration by ID
 * @param {string} statusId - Status ID
 * @returns {object|null} Status configuration or null if not found
 */
export const getStatusById = (statusId) => {
	return kanbanStatuses.find((status) => status.id === statusId) || null;
};
