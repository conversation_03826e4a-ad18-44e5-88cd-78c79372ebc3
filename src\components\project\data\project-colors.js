/**
 * Project colors data with beautiful gradients and fancy color combinations
 */

export const projectColors = [
	{
		name: 'Ocean Breeze',
		value: 'bg-gradient-to-br from-blue-400 via-blue-500 to-cyan-600',
		class: 'bg-gradient-to-br from-blue-400 via-blue-500 to-cyan-600',
	},
	{
		name: 'Sunset Glow',
		value: 'bg-gradient-to-br from-orange-400 via-pink-500 to-red-600',
		class: 'bg-gradient-to-br from-orange-400 via-pink-500 to-red-600',
	},
	{
		name: 'Forest Magic',
		value: 'bg-gradient-to-br from-green-400 via-emerald-500 to-teal-600',
		class: 'bg-gradient-to-br from-green-400 via-emerald-500 to-teal-600',
	},
	{
		name: 'Purple Dreams',
		value: 'bg-gradient-to-br from-purple-400 via-violet-500 to-indigo-600',
		class: 'bg-gradient-to-br from-purple-400 via-violet-500 to-indigo-600',
	},
	{
		name: 'Golden Hour',
		value: 'bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500',
		class: 'bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500',
	},
	{
		name: 'Rose Garden',
		value: 'bg-gradient-to-br from-pink-400 via-rose-500 to-red-500',
		class: 'bg-gradient-to-br from-pink-400 via-rose-500 to-red-500',
	},
	{
		name: 'Midnight Sky',
		value: 'bg-gradient-to-br from-slate-800 via-blue-900 to-indigo-900',
		class: 'bg-gradient-to-br from-slate-800 via-blue-900 to-indigo-900',
	},
	{
		name: 'Tropical Mint',
		value: 'bg-gradient-to-br from-teal-400 via-cyan-500 to-blue-500',
		class: 'bg-gradient-to-br from-teal-400 via-cyan-500 to-blue-500',
	},
	{
		name: 'Cherry Blossom',
		value: 'bg-gradient-to-br from-pink-300 via-pink-400 to-rose-500',
		class: 'bg-gradient-to-br from-pink-300 via-pink-400 to-rose-500',
	},
	{
		name: 'Electric Lime',
		value: 'bg-gradient-to-br from-lime-400 via-green-500 to-emerald-600',
		class: 'bg-gradient-to-br from-lime-400 via-green-500 to-emerald-600',
	},
	{
		name: 'Royal Purple',
		value: 'bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-600',
		class: 'bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-600',
	},
	{
		name: 'Arctic Aurora',
		value: 'bg-gradient-to-br from-cyan-300 via-blue-400 to-purple-500',
		class: 'bg-gradient-to-br from-cyan-300 via-blue-400 to-purple-500',
	},
	{
		name: 'Desert Mirage',
		value: 'bg-gradient-to-br from-amber-300 via-orange-400 to-purple-600',
		class: 'bg-gradient-to-br from-amber-300 via-orange-400 to-purple-600',
	},
	{
		name: 'Cosmic Fusion',
		value: 'bg-gradient-to-br from-fuchsia-400 via-violet-600 to-blue-700',
		class: 'bg-gradient-to-br from-fuchsia-400 via-violet-600 to-blue-700',
	},
];

/**
 * Get project color by value
 * @param {string} value - Color value (e.g., 'bg-blue-500')
 * @returns {object|null} Project color object or null if not found
 */
export const getProjectColorByValue = (value) => {
	return projectColors.find((color) => color.value === value) || null;
};
