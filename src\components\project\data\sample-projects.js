/**
 * Sample project data with support for both background images and solid colors
 */

export const sampleProjects = [
	{
		id: 1,
		title: 'Website Redesign',
		description: 'Complete overhaul of company website with modern UI/UX',
		status: 'active',
		priority: 'high',
		dueDate: '2024-02-15',
		progress: 65,
		members: [
			{ id: 1, name: '<PERSON>', avatar: null },
			{ id: 2, name: '<PERSON>', avatar: null },
			{ id: 3, name: '<PERSON>', avatar: null },
		],
		tasksCount: 24,
		completedTasks: 16,
		color: 'bg-gradient-to-br from-blue-400 via-blue-500 to-cyan-600',
		backgroundImage: {
			id: 1,
			url: 'https://picsum.photos/1920/1080?random=1',
			thumbnail: 'https://picsum.photos/400/225?random=1',
		},
		backgroundType: 'image', // 'image' or 'color'
		isStarred: true,
	},
	{
		id: 2,
		title: 'Mobile App Development',
		description: 'Native mobile application for iOS and Android platforms',
		status: 'active',
		priority: 'medium',
		dueDate: '2024-03-20',
		progress: 30,
		members: [
			{ id: 4, name: '<PERSON>', avatar: null },
			{ id: 5, name: '<PERSON>', avatar: null },
		],
		tasksCount: 18,
		completedTasks: 5,
		color: 'bg-gradient-to-br from-green-400 via-emerald-500 to-teal-600',
		backgroundImage: null,
		backgroundType: 'color',
		isStarred: false,
	},
	{
		id: 3,
		title: 'Database Migration',
		description: 'Migrate legacy database to new cloud infrastructure',
		status: 'completed',
		priority: 'high',
		dueDate: '2024-01-30',
		progress: 100,
		members: [
			{ id: 6, name: 'Alex Chen', avatar: null },
			{ id: 7, name: 'Lisa Park', avatar: null },
		],
		tasksCount: 12,
		completedTasks: 12,
		color: 'bg-gradient-to-br from-purple-400 via-violet-500 to-indigo-600',
		backgroundImage: {
			id: 3,
			url: 'https://picsum.photos/1920/1080?random=3',
			thumbnail: 'https://picsum.photos/400/225?random=3',
		},
		backgroundType: 'image',
		isStarred: true,
	},
	{
		id: 4,
		title: 'Marketing Campaign',
		description: 'Q1 digital marketing campaign across all channels',
		status: 'planning',
		priority: 'low',
		dueDate: '2024-04-10',
		progress: 10,
		members: [{ id: 8, name: 'Emma Davis', avatar: null }],
		tasksCount: 8,
		completedTasks: 1,
		color: 'bg-gradient-to-br from-orange-400 via-pink-500 to-red-600',
		backgroundImage: null,
		backgroundType: 'color',
		isStarred: false,
	},
	{
		id: 5,
		title: 'E-commerce Platform',
		description:
			'Build comprehensive online shopping platform with payment integration',
		status: 'active',
		priority: 'high',
		dueDate: '2024-04-25',
		progress: 45,
		members: [
			{ id: 9, name: 'David Lee', avatar: null },
			{ id: 10, name: 'Maria Garcia', avatar: null },
			{ id: 11, name: 'James Wilson', avatar: null },
		],
		tasksCount: 32,
		completedTasks: 14,
		color: 'bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-600',
		backgroundImage: {
			id: 4,
			url: 'https://picsum.photos/1920/1080?random=4',
			thumbnail: 'https://picsum.photos/400/225?random=4',
		},
		backgroundType: 'image',
		isStarred: true,
	},
	{
		id: 6,
		title: 'API Documentation',
		description: 'Comprehensive API documentation and developer portal',
		status: 'planning',
		priority: 'medium',
		dueDate: '2024-03-15',
		progress: 5,
		members: [
			{ id: 12, name: 'Robert Taylor', avatar: null },
			{ id: 13, name: 'Jennifer Brown', avatar: null },
		],
		tasksCount: 15,
		completedTasks: 1,
		color: 'bg-gradient-to-br from-teal-400 via-cyan-500 to-blue-500',
		backgroundImage: null,
		backgroundType: 'color',
		isStarred: false,
	},
];
