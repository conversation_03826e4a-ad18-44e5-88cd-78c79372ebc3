'use client';

import React from 'react';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Calendar,
	CheckCircle2,
	Clock,
	AlertCircle,
	Users,
	Star,
	Folder,
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * ProjectSummaryDialog Component
 * Displays comprehensive project information including all stats removed from cards
 */
export const ProjectSummaryDialog = ({ project, open, onOpenChange }) => {
	if (!project) return null;

	const getStatusIcon = (status) => {
		switch (status) {
			case 'completed':
				return <CheckCircle2 className="h-4 w-4 text-green-600" />;
			case 'active':
				return <Clock className="h-4 w-4 text-blue-600" />;
			case 'planning':
				return <AlertCircle className="h-4 w-4 text-orange-600" />;
			default:
				return <Folder className="h-4 w-4 text-muted-foreground" />;
		}
	};

	const getPriorityColor = (priority) => {
		switch (priority) {
			case 'high':
				return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
			case 'medium':
				return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
			case 'low':
				return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
		}
	};

	const formatDate = (dateString) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
		});
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<DialogTitle className="text-xl font-bold flex items-center gap-2">
								{project.title}
								{project.isStarred && (
									<Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
								)}
							</DialogTitle>
							<DialogDescription className="mt-2">
								{project.description}
							</DialogDescription>
						</div>
					</div>
				</DialogHeader>

				<div className="space-y-6">
					{/* Status and Priority Row */}
					<div className="grid grid-cols-2 gap-4">
						<Card>
							<CardHeader className="pb-2">
								<CardTitle className="text-sm font-medium flex items-center gap-2">
									{getStatusIcon(project.status)}
									Status
								</CardTitle>
							</CardHeader>
							<CardContent>
								<span className="text-lg font-semibold capitalize">
									{project.status}
								</span>
							</CardContent>
						</Card>

						<Card>
							<CardHeader className="pb-2">
								<CardTitle className="text-sm font-medium">Priority</CardTitle>
							</CardHeader>
							<CardContent>
								<Badge
									variant="secondary"
									className={getPriorityColor(project.priority)}
								>
									{project.priority}
								</Badge>
							</CardContent>
						</Card>
					</div>

					{/* Progress Section */}
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">Progress</CardTitle>
						</CardHeader>
						<CardContent className="space-y-3">
							<div className="flex justify-between text-sm">
								<span className="text-muted-foreground">Overall Progress</span>
								<span className="font-medium">{project.progress}%</span>
							</div>
							<Progress value={project.progress} className="h-2" />
							<div className="flex justify-between text-sm text-muted-foreground">
								<span>
									{project.completedTasks} of {project.tasksCount} tasks
									completed
								</span>
								<span>
									{project.tasksCount - project.completedTasks} remaining
								</span>
							</div>
						</CardContent>
					</Card>

					{/* Due Date and Tasks */}
					<div className="grid grid-cols-2 gap-4">
						<Card>
							<CardHeader className="pb-2">
								<CardTitle className="text-sm font-medium flex items-center gap-2">
									<Calendar className="h-4 w-4" />
									Due Date
								</CardTitle>
							</CardHeader>
							<CardContent>
								<span className="text-sm font-medium">
									{formatDate(project.dueDate)}
								</span>
							</CardContent>
						</Card>

						<Card>
							<CardHeader className="pb-2">
								<CardTitle className="text-sm font-medium flex items-center gap-2">
									<CheckCircle2 className="h-4 w-4" />
									Tasks
								</CardTitle>
							</CardHeader>
							<CardContent>
								<span className="text-sm font-medium">
									{project.completedTasks}/{project.tasksCount}
								</span>
							</CardContent>
						</Card>
					</div>

					{/* Team Members */}
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium flex items-center gap-2">
								<Users className="h-4 w-4" />
								Team Members ({project.members.length})
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{project.members.map((member) => (
									<div key={member.id} className="flex items-center gap-3">
										<Avatar className="h-8 w-8">
											<AvatarImage src={member.avatar} />
											<AvatarFallback className="text-xs">
												{member.name
													.split(' ')
													.map((n) => n[0])
													.join('')}
											</AvatarFallback>
										</Avatar>
										<div>
											<div className="font-medium text-sm">{member.name}</div>
											{member.email && (
												<div className="text-xs text-muted-foreground">
													{member.email}
												</div>
											)}
										</div>
									</div>
								))}
							</div>
						</CardContent>
					</Card>
				</div>
			</DialogContent>
		</Dialog>
	);
};
