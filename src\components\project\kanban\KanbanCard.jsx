'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Draggable } from '@hello-pangea/dnd';
import { CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Calendar,
	MessageCircle,
	Paperclip,
	CheckSquare,
	Clock,
	AlertCircle,
	Check,
} from 'lucide-react';
import { cn, sanitizeHtml } from '@/lib/utils';
import { updateTask } from '@/lib/features/tasks/tasksSlice';
import { useAppDispatch } from '@/lib/hooks';

/**
 * KanbanCard Component
 * Individual task card within a kanban group following Trello-inspired design
 */
export const KanbanCard = ({
	card,
	index,
	onClick,
	className,
	isGlassMode,
	...props
}) => {
	const dispatch = useAppDispatch();
	const [isHovered, setIsHovered] = useState(false);
	const isCompleted = card.status === 'completed';

	const handleCompletionToggle = (e) => {
		e.stopPropagation(); // Prevent card click
		const newStatus = isCompleted ? 'pending' : 'completed';

		dispatch(
			updateTask({
				taskId: card._id,
				projectId: card.projectId,
				status: newStatus,
			})
		);
	};

	return (
		<Draggable draggableId={card._id} index={index}>
			{(provided, snapshot) => (
				<div
					ref={provided.innerRef}
					{...provided.draggableProps}
					{...provided.dragHandleProps}
					className={cn(
						'cursor-pointer group relative',
						'rounded-lg shadow-sm transition-all duration-200',
						isGlassMode
							? 'backdrop-blur-sm bg-card/80 text-card-foreground border border-border/40'
							: 'bg-card text-card-foreground border-2 border-transparent',
						'border-border hover:border-muted-foreground',
						snapshot.isDragging && 'opacity-90 shadow-xl scale-105',
						className
					)}
					onClick={() => onClick(card)}
					onMouseEnter={() => setIsHovered(true)}
					onMouseLeave={() => setIsHovered(false)}
					tabIndex={0}
					role="article"
					aria-label={`Task: ${card.name}${card.description ? `. ${card.description}` : ''}`}
					{...props}
				>
					{/* Cover Image */}
					{card.coverImage && (
						<div className="relative w-full h-32 overflow-hidden rounded-t-lg">
							<Image
								src={card.coverImage}
								alt={card.name}
								width={400}
								height={128}
								className="w-full h-full object-cover transition-all duration-300"
								sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
								priority={false}
							/>
							<div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 transition-opacity duration-300" />
						</div>
					)}

					<CardContent className="p-3 space-y-2">
						{/* Priority Badge */}
						{card.priority && (
							<div
								className={cn(
									'text-xs p-1 w-10',
									card.priority === 'high' &&
										'rounded-full bg-red-500 dark:bg-red-400',
									card.priority === 'medium' &&
										'rounded-full bg-yellow-500 dark:bg-yellow-400',
									card.priority === 'low' &&
										'rounded-full bg-green-500 dark:bg-green-400'
								)}
							></div>
						)}

						{/* Title with Completion Radio Button */}
						<div className="relative flex items-center">
							{/* Custom Radio Button for Completion */}
							<div
								className={cn(
									'absolute left-0 flex-shrink-0 w-4 h-4 rounded-full border-2 cursor-pointer transition-all duration-500 flex items-center justify-center z-10',
									isCompleted
										? 'bg-green-500 border-green-500'
										: isHovered || isCompleted
											? 'border-gray-400 hover:border-green-400'
											: 'border-transparent',
									isHovered || isCompleted ? 'opacity-100' : 'opacity-0'
								)}
								onClick={handleCompletionToggle}
								title={isCompleted ? 'Mark as incomplete' : 'Mark as complete'}
							>
								{isCompleted && <Check className="w-2.5 h-2.5 text-white" />}
							</div>

							{/* Title */}
							<h3
								className={cn(
									'text-sm font-medium line-clamp-2 leading-tight transition-all duration-500 w-full',
									isHovered || isCompleted ? 'pl-6' : 'pl-0'
								)}
							>
								{card.name}
							</h3>
						</div>

						{/* Description */}
						{card.description && (
							<p className="text-xs text-muted-foreground line-clamp-2 leading-relaxed">
								{sanitizeHtml(card.description)}
							</p>
						)}

						{/* Task Meta Information */}
						<div className="flex items-center justify-between text-xs text-muted-foreground">
							{/* Creation Time */}
							{card.createdAt && (
								<div className="flex items-center gap-1">
									<Clock className="h-3 w-3" />
									<span>{new Date(card.createdAt).toLocaleDateString()}</span>
								</div>
							)}

							{/* Task Stats */}
							<div className="flex items-center gap-2">
								{/* Attachments Count */}
								{card.media && card.media.length > 0 && (
									<div
										className="flex items-center gap-1"
										title={`${card.media.length} attachments`}
									>
										<Paperclip className="h-3 w-3" />
										<span>{card.media.length}</span>
									</div>
								)}

								{/* Comments Count */}
								{card.comments && card.comments.length > 0 && (
									<div
										className="flex items-center gap-1"
										title={`${card.comments.length} comments`}
									>
										<MessageCircle className="h-3 w-3" />
										<span>{card.comments.length}</span>
									</div>
								)}

								{/* Due Date Warning */}
								{card.dueDate && new Date(card.dueDate) < new Date() && (
									<div
										className="flex items-center gap-1 text-red-500 dark:text-red-400"
										title="Overdue"
									>
										<AlertCircle className="h-3 w-3" />
									</div>
								)}

								{/* Assignee Avatar */}
								{card.assignedTo && (
									<div
										className="flex items-center gap-1"
										title={`Assigned to ${card.assignedTo.name}`}
									>
										<Avatar
											key={card.assignedTo.userId}
											src={card.assignedTo.profilePhoto}
											className={cn(
												'w-8 h-8 border-2 border-muted-foreground bg-muted transition-colors duration-300'
											)}
										>
											<AvatarImage
												src={card.assignedTo.profilePhoto || '/placeholder.svg'}
												alt={card.assignedTo.name || 'Profile'}
											/>
											<AvatarFallback className={cn('text-xs bg-white/20')}>
												{card.assignedTo.name
													.split(' ')
													.map((n) => n[0])
													.join('')}
											</AvatarFallback>
										</Avatar>
									</div>
								)}
							</div>
						</div>
					</CardContent>
				</div>
			)}
		</Draggable>
	);
};
