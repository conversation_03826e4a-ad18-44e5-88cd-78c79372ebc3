'use client';

import React, { useState } from 'react';
import { Droppable } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import {
	Plus,
	MoreHorizontal,
	Check,
	ChevronLeft,
	ChevronRight,
} from 'lucide-react';
import { cn, generateRandomSixCharCode } from '@/lib/utils';
import { KanbanCard } from './KanbanCard';
import { ScrollArea } from '@/components/ui/scroll-area';
import { TaskModal } from './TaskModal';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
	createTask,
	fetchSingleTaskDetails,
} from '@/lib/features/tasks/tasksSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';

// Task creation schema (simplified for quick task creation)
const createTaskSchema = z.object({
	name: z
		.string()
		.nonempty('Task name is required')
		.max(50, 'Task name must be at most 50 characters long'),
});

/**
 * KanbanGroup Component
 * Represents a group in the kanban board (e.g., To Do, In Progress, Done)
 */
export const KanbanColumn = ({
	group,
	tasks = [],
	isDropTarget = false,
	isGlassMode = false,
	isDropdownOpen = false,
	onDropdownOpenChange,
	className,
	...props
}) => {
	const dispatch = useAppDispatch();
	const { taskDetails } = useAppSelector((store) => store.tasks);
	const [isAddingTask, setIsAddingTask] = useState(false);
	const [isEditing, setIsEditing] = useState(false);
	const [editTitle, setEditTitle] = useState(group.name);
	const [isCollapsed, setIsCollapsed] = useState(false);
	const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
	const [taskId, setTaskId] = useState(false);
	const [selectedColor, setSelectedColor] = useState(
		group.bgColor ||
			(isGlassMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.9)')
	);

	// React Hook Form for quick task creation
	const taskForm = useForm({
		resolver: zodResolver(createTaskSchema),
		defaultValues: {
			name: '',
		},
	});

	// Color options for group - 8 pastel colors
	const colorOptions = [
		{ name: 'Header White', value: 'rgba(255, 255, 255, 0.9)' },
		{ name: 'Soft Pink', value: '#fce7f3' },
		{ name: 'Soft Orange', value: '#fed7aa' },
		{ name: 'Soft Yellow', value: '#fef3c7' },
		{ name: 'Soft Green', value: '#d1fae5' },
		{ name: 'Soft Blue', value: '#dbeafe' },
		{ name: 'Soft Purple', value: '#e9d5ff' },
		{ name: 'Soft Teal', value: '#ccfbf1' },
	];

	// Form submission handler
	const onSubmitTask = async (data) => {
		console.log(group);
		const taskData = {
			...data,
			projectId: group.projectId,
			code: generateRandomSixCharCode(),
			groupId: group._id,
		};
		const result = await dispatch(createTask(taskData));

		if (createTask.fulfilled.match(result)) {
			setIsAddingTask(false);
			taskForm.reset();
		}
	};

	const handleSaveEdit = () => {
		if (editTitle.trim() && editTitle !== group.name) {
			// TODO: Call onGroupUpdate callback with new title
			console.log('Update group title:', editTitle);
		}
		setIsEditing(false);
	};

	const handleColorChange = (color) => {
		setSelectedColor(color);
		// TODO: Call onGroupUpdate callback with new color
		console.log('Update group color:', color);
	};

	const handleTaskClick = (task) => {
		console.log('opening task modal', task);
		setIsTaskModalOpen(true);
		setTaskId(task._id);
	};

	const handleTaskModalClose = () => {
		console.log('closing task modal');
		setIsTaskModalOpen(false);
		setTaskId(null);
	};

	const handleTaskSave = (updatedTask) => {
		// TODO: Update task in the group data
		console.log('Task saved:', updatedTask);
		// In a real app, this would call an API or update state
	};

	return (
		<div
			className={cn(
				'flex-shrink-0 h-fit transition-all duration-300',
				isCollapsed ? 'w-12' : 'w-[18rem]',
				className
			)}
			{...props}
		>
			<Card
				className={cn(
					'shadow-sm transition-all duration-300 flex flex-col gap-2',
					isGlassMode
						? 'bg-white/10 backdrop-blur-md border border-white/20 text-white'
						: 'bg-card text-card-foreground border border-border',
					isCollapsed ? 'p-1' : 'p-2'
				)}
				role="region"
				aria-label={`${group.name} group with ${tasks.length} tasks`}
			>
				<CardHeader className="transition-all duration-300 p-2">
					{isCollapsed ? (
						<div className="flex flex-col items-center gap-2 h-full">
							<Button
								variant="ghost"
								size="sm"
								onClick={() => setIsCollapsed(false)}
								className={cn(
									'h-6 w-6 p-0 transition-all duration-200',
									isGlassMode
										? 'text-white/70 hover:text-white hover:bg-white/20'
										: 'text-muted-foreground hover:text-foreground hover:bg-muted'
								)}
								title="Expand group"
							>
								<ChevronRight className="h-4 w-4" />
							</Button>

							<div className="flex flex-col items-center gap-2 flex-1">
								<div
									className={cn(
										'text-sm font-semibold writing-mode-vertical-rl text-orientation-mixed transition-colors duration-200',
										isGlassMode ? 'text-white' : 'text-foreground'
									)}
									style={{
										writingMode: 'vertical-rl',
										textOrientation: 'mixed',
									}}
									title={group.name}
								>
									{group.name}
								</div>
								<Badge
									variant="secondary"
									className={cn(
										'text-xs px-1 py-0.5 min-w-[20px] text-center',
										isGlassMode
											? 'bg-white/20 text-white/80'
											: 'bg-muted text-muted-foreground'
									)}
								>
									{tasks.length}
								</Badge>
							</div>
						</div>
					) : (
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<Button
									variant="ghost"
									size="sm"
									onClick={() => setIsCollapsed(true)}
									className={cn(
										'h-6 w-6 p-0 transition-all duration-200',
										isGlassMode
											? 'text-white/70 hover:text-white hover:bg-white/20'
											: 'text-muted-foreground hover:text-foreground hover:bg-muted'
									)}
									title="Collapse group"
								>
									<ChevronLeft className="h-4 w-4" />
								</Button>

								<h3
									className={cn(
										'text-sm font-semibold transition-colors duration-200 group-hover:text-foreground',
										isGlassMode ? 'text-white' : 'text-foreground'
									)}
								>
									{group.name}
								</h3>
								<Badge
									variant="secondary"
									className={cn(
										'text-xs px-2 py-0.5 transition-colors duration-200',
										isGlassMode
											? 'bg-white/20 text-white/80 hover:bg-white/30'
											: 'bg-muted text-muted-foreground hover:bg-border'
									)}
								>
									{tasks.length}
								</Badge>
							</div>

							{/* <DropdownMenu
								open={isDropdownOpen}
								onOpenChange={onDropdownOpenChange}
							>
								<DropdownMenuTrigger asChild>
									<Button
										variant="ghost"
										size="sm"
										className={cn(
											'h-6 w-6 p-0 transition-all duration-200',
											isGlassMode
												? 'text-white/70 hover:text-white hover:bg-white/20'
												: 'text-muted-foreground hover:text-foreground hover:bg-muted'
										)}
									>
										<MoreHorizontal className="h-4 w-4" />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align="end" className="w-64">
									<div className="px-3 py-2">
										{isEditing ? (
											<div className="space-y-2">
												<Input
													value={editTitle}
													onChange={(e) => setEditTitle(e.target.value)}
													onKeyDown={handleEditKeyPress}
													onBlur={handleSaveEdit}
													className="h-8 text-sm"
													autoFocus
												/>
												<div className="flex gap-1">
													<Button
														size="sm"
														onClick={handleSaveEdit}
														className="h-6 px-2 text-xs"
													>
														<Check className="h-3 w-3" />
													</Button>
													<Button
														variant="ghost"
														size="sm"
														onClick={() => {
															setIsEditing(false);
															setEditTitle(group.name);
														}}
														className="h-6 px-2 text-xs"
													>
														Cancel
													</Button>
												</div>
											</div>
										) : (
											<Button
												variant="ghost"
												size="sm"
												onClick={() => setIsEditing(true)}
												className="h-8 w-full justify-start text-sm px-2"
											>
												{group.name}
											</Button>
										)}
									</div>
									<DropdownMenuSeparator />
									<div className="p-2">
										<div className="grid grid-cols-4 gap-2">
											{colorOptions.map((color) => (
												<button
													key={color.value}
													onClick={() => handleColorChange(color.value)}
													className={cn(
														'w-12 h-6 rounded border-2 transition-all duration-200 hover:scale-105',
														selectedColor === color.value
															? 'border-foreground ring-2 ring-border'
															: 'border-border hover:border-muted-foreground'
													)}
													style={{ backgroundColor: color.value }}
													title={color.name}
												/>
											))}
										</div>
									</div>
								</DropdownMenuContent>
							</DropdownMenu> */}
						</div>
					)}
				</CardHeader>

				{!isCollapsed && (
					<>
						<CardContent className="pt-0 p-0">
							<Droppable droppableId={group._id}>
								{(provided, snapshot) => (
									<div
										ref={provided.innerRef}
										{...provided.droppableProps}
										className={cn(
											'min-h-[1px] gap-2 flex flex-col',
											snapshot.isDraggingOver &&
												(isGlassMode
													? 'bg-white/20 rounded-md'
													: 'bg-muted/50 rounded-md')
										)}
									>
										{tasks.map((task, index) => (
											<KanbanCard
												key={task._id}
												card={task}
												index={index}
												onClick={handleTaskClick}
												isGlassMode={isGlassMode}
											/>
										))}
										{provided.placeholder}
									</div>
								)}
							</Droppable>
						</CardContent>

						<div className="flex-shrink-0">
							{isAddingTask ? (
								<Card className="bg-card border-border border-2 shadow-sm">
									<CardContent className="p-3">
										<Form {...taskForm}>
											<form
												onSubmit={taskForm.handleSubmit(onSubmitTask)}
												className="space-y-2"
											>
												<FormField
													control={taskForm.control}
													name="name"
													render={({ field }) => (
														<FormItem>
															<FormControl>
																<textarea
																	{...field}
																	placeholder="Enter a title for this task..."
																	className="w-full text-sm border-none outline-none resize-none bg-transparent placeholder-muted-foreground focus:placeholder-foreground transition-colors"
																	rows={2}
																	autoFocus
																	onKeyDown={(e) => {
																		if (e.key === 'Enter' && !e.shiftKey) {
																			e.preventDefault();
																			taskForm.handleSubmit(onSubmitTask)();
																		} else if (e.key === 'Escape') {
																			setIsAddingTask(false);
																			taskForm.reset();
																		}
																	}}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
												<div className="flex items-center gap-2">
													<Button
														type="submit"
														size="sm"
														disabled={!taskForm.watch('name')?.trim()}
														className="h-7 px-3 text-xs bg-primary hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 text-primary-foreground"
													>
														Add task
													</Button>
													<Button
														type="button"
														variant="ghost"
														size="sm"
														onClick={() => {
															setIsAddingTask(false);
															taskForm.reset();
														}}
														className="h-7 px-3 text-xs text-muted-foreground hover:text-foreground transition-colors duration-200"
													>
														Cancel
													</Button>
												</div>
											</form>
										</Form>
									</CardContent>
								</Card>
							) : (
								<Button
									variant="ghost"
									className={cn(
										'w-full justify-start text-left p-2 h-auto border-2 border-dashed transition-all duration-200 hover:text-muted-foreground',
										isGlassMode
											? 'text-white/70 border-white/30 hover:border-white/50 hover:bg-white/10'
											: 'text-muted-foreground border-border hover:border-muted-foreground hover:bg-muted'
									)}
									onClick={() => setIsAddingTask(true)}
								>
									<Plus className="h-4 w-4 mr-2 transition-transform duration-200 group-hover:rotate-90" />
									<span className="text-sm">Add a task</span>
								</Button>
							)}
						</div>
					</>
				)}
			</Card>

			<TaskModal
				isOpen={isTaskModalOpen}
				onClose={handleTaskModalClose}
				taskId={taskId}
				onSave={handleTaskSave}
				isGlassMode={isGlassMode}
			/>
		</div>
	);
};
