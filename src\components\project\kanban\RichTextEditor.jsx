'use client';

import React, { useState, useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import Placeholder from '@tiptap/extension-placeholder';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
	Bold,
	Italic,
	List,
	ListOrdered,
	Link as LinkIcon,
	Quote,
	Strikethrough,
	Code,
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * RichTextEditor Component
 * TipTap-based rich text editor for descriptions and comments
 */
export const RichTextEditor = ({
	content = '',
	onChange,
	placeholder = 'Start typing...',
	compact = false,
	className,
}) => {
	const editor = useEditor({
		extensions: [
			StarterKit,
			Link.configure({
				openOnClick: false,
			}),
			Placeholder.configure({
				placeholder,
			}),
		],
		content,
		onUpdate: ({ editor }) => {
			onChange?.(editor.getHTML());
		},
	});

	// Update editor content when content prop changes
	useEffect(() => {
		if (editor && content !== editor.getHTML()) {
			editor.commands.setContent(content);
		}
	}, [content, editor]);

	if (!editor) {
		return null;
	}

	const toggleBold = () => editor.chain().focus().toggleBold().run();
	const toggleItalic = () => editor.chain().focus().toggleItalic().run();
	const toggleStrike = () => editor.chain().focus().toggleStrike().run();
	const toggleCode = () => editor.chain().focus().toggleCode().run();
	const toggleBulletList = () =>
		editor.chain().focus().toggleBulletList().run();
	const toggleOrderedList = () =>
		editor.chain().focus().toggleOrderedList().run();
	const toggleBlockquote = () =>
		editor.chain().focus().toggleBlockquote().run();

	return (
		<div className={cn('border rounded-md overflow-hidden', className)}>
			{/* Toolbar */}
			{!compact && (
				<div className="flex items-center gap-1 p-2 border-b bg-muted">
					<Button
						variant="ghost"
						size="sm"
						className={cn('h-6 w-6 p-0', editor.isActive('bold') && 'bg-muted')}
						onClick={toggleBold}
					>
						<Bold className="h-3 w-3" />
					</Button>
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							'h-6 w-6 p-0',
							editor.isActive('italic') && 'bg-muted'
						)}
						onClick={toggleItalic}
					>
						<Italic className="h-3 w-3" />
					</Button>
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							'h-6 w-6 p-0',
							editor.isActive('strike') && 'bg-muted'
						)}
						onClick={toggleStrike}
					>
						<Strikethrough className="h-3 w-3" />
					</Button>
					<Button
						variant="ghost"
						size="sm"
						className={cn('h-6 w-6 p-0', editor.isActive('code') && 'bg-muted')}
						onClick={toggleCode}
					>
						<Code className="h-3 w-3" />
					</Button>
					<div className="w-px h-4 bg-border mx-1" />
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							'h-6 w-6 p-0',
							editor.isActive('bulletList') && 'bg-muted'
						)}
						onClick={toggleBulletList}
					>
						<List className="h-3 w-3" />
					</Button>
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							'h-6 w-6 p-0',
							editor.isActive('orderedList') && 'bg-muted'
						)}
						onClick={toggleOrderedList}
					>
						<ListOrdered className="h-3 w-3" />
					</Button>
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							'h-6 w-6 p-0',
							editor.isActive('blockquote') && 'bg-muted'
						)}
						onClick={toggleBlockquote}
					>
						<Quote className="h-3 w-3" />
					</Button>
				</div>
			)}

			{/* Editor Content */}
			<EditorContent
				editor={editor}
				className={cn(
					'prose prose-sm max-w-none focus:outline-none',
					compact ? 'min-h-[60px]' : 'min-h-[120px]',
					'[&_.ProseMirror]:p-3 [&_.ProseMirror]:focus:outline-none',
					'[&_.ProseMirror]:text-sm [&_.ProseMirror]:text-card-foreground',
					'[&_.ProseMirror_p.is-editor-empty:first-child::before]:content-[attr(data-placeholder)]',
					'[&_.ProseMirror_p.is-editor-empty:first-child::before]:text-muted-foreground',
					'[&_.ProseMirror_p.is-editor-empty:first-child::before]:float-left',
					'[&_.ProseMirror_p.is-editor-empty:first-child::before]:pointer-events-none',
					'[&_.ProseMirror_p.is-editor-empty:first-child::before]:h-0',

					// Inline code
					'[&_.ProseMirror_code]:bg-muted',
					'[&_.ProseMirror_code]:text-muted-foreground',
					'[&_.ProseMirror_code]:rounded',
					'[&_.ProseMirror_code]:px-1.5',
					'[&_.ProseMirror_code]:py-0.5',
					'[&_.ProseMirror_code]:font-mono',
					'[&_.ProseMirror_code]:text-xs',

					// Block code
					'[&_.ProseMirror pre]:bg-muted',
					'[&_.ProseMirror pre]:text-muted-foreground',
					'[&_.ProseMirror pre]:rounded-md',
					'[&_.ProseMirror pre]:p-3',
					'[&_.ProseMirror pre]:font-mono',
					'[&_.ProseMirror pre]:text-sm'
				)}
			/>
		</div>
	);
};
