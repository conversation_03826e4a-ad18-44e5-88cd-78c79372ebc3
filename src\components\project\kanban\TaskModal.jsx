'use client';

import React, { useState } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';

// Import custom components
import {
	TaskHeader,
	TaskTitle,
	TaskControls,
	TaskDescription,
	TaskAttachments,
	TaskComments,
	AttachmentPreviewModal,
} from './components';

// Import custom hooks
import { useTaskForms, useTaskHandlers, useTaskData } from './hooks';

// Import Redux actions
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	removeTaskAttachment,
	updateTaskAttachments,
} from '@/lib/features/tasks/tasksSlice';
import { ScrollArea } from '@/components/ui/scroll-area';

/**
 * TaskModal Component
 * Modal for adding/editing tasks with react-hook-form and Zod validation
 * Now refactored into separate components and custom hooks for better maintainability
 */
export const TaskModal = ({
	isOpen,
	onClose,
	taskId = null,
	onSave,
	isGlassMode,
}) => {
	const isEditMode = !!taskId;
	const [previewAttachment, setPreviewAttachment] = useState(null);
	const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
	const [pendingFiles, setPendingFiles] = useState([]); // Files waiting to be uploaded
	const dispatch = useAppDispatch();

	// Use custom hooks for better organization
	const { taskForm, commentForm, descriptionForm, updateFormsWithTaskData } =
		useTaskForms(taskId, isEditMode);

	const { handleUpdateTask, handleAddComment, handleUpdateDescription } =
		useTaskHandlers(taskId, commentForm, onSave, onClose);

	const {
		task,
		isLoading,
		getImageAttachments,
		getCoverImage,
		handleRemoveCoverImage,
		handleSetCoverImage,
	} = useTaskData(taskId, taskForm, updateFormsWithTaskData);

	// Attachment handlers
	const handleAttachmentPreview = (attachment) => {
		setPreviewAttachment(attachment);
		setIsPreviewModalOpen(true);
	};

	const handleAttachmentDownload = (attachment) => {
		const link = document.createElement('a');
		link.href = attachment.url;
		link.download = attachment.public_id || attachment.name || 'download';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	};

	const handleAttachmentUpload = (files) => {
		try {
			// Create preview objects for the selected files
			const newPreviews = Array.from(files).map((file) => ({
				id: Date.now() + Math.random(), // Temporary ID for preview
				file: file, // Original file object
				name: file.name,
				size: file.size,
				type: file.type,
				url: URL.createObjectURL(file), // Preview URL
				isImage: file.type.startsWith('image/'),
				isPending: true, // Mark as pending upload
			}));

			// Add to pending files for preview
			setPendingFiles((prev) => [...prev, ...newPreviews]);

			// Check if any new file is an image and set first image as cover if no cover exists
			let coverImage = taskForm.watch('coverImage');
			if (!coverImage) {
				const firstImageFile = newPreviews.find((preview) => preview.isImage);
				if (firstImageFile) {
					coverImage = firstImageFile.name; // Use filename with extension as cover
					taskForm.setValue('coverImage', coverImage);
				}
			}
		} catch (error) {
			console.error('Error creating file previews:', error);
		}
	};

	const handlePendingFileRemove = (fileId) => {
		// Remove file from pending list
		setPendingFiles((prev) => {
			const updated = prev.filter((file) => file.id !== fileId);

			// Clean up object URL to prevent memory leaks
			const removedFile = prev.find((file) => file.id === fileId);
			if (removedFile?.url) {
				URL.revokeObjectURL(removedFile.url);
			}

			return updated;
		});

		// If removed file was set as cover, clear cover or set next available image
		const removedFile = pendingFiles.find((file) => file.id === fileId);
		const currentCover = taskForm.watch('coverImage');

		if (removedFile && currentCover === removedFile.name) {
			// Find next available image (from existing attachments or remaining pending files)
			const remainingPendingImages = pendingFiles.filter(
				(file) => file.id !== fileId && file.isImage
			);
			const existingImages = getImageAttachments();

			let newCover = '';
			if (remainingPendingImages.length > 0) {
				newCover = remainingPendingImages[0].name;
			} else if (existingImages.length > 0) {
				newCover = existingImages[0].public_id || existingImages[0].name;
			}

			taskForm.setValue('coverImage', newCover);
		}
	};

	const handleAttachmentDelete = async (attachment) => {
		try {
			console.log('running', attachment);
			// Check if deleted attachment was the cover image
			let updateData = {
				taskId: task._id,
				projectId: task.projectId,
				mediaId: attachment._id,
				mediaUrl: attachment.url,
			};

			await dispatch(removeTaskAttachment(updateData));
		} catch (error) {
			console.error('Error deleting attachment:', error);
		}
	};

	const handleSubmitPendingFiles = async () => {
		if (pendingFiles.length === 0) return;

		try {
			// Create FormData for file upload
			const formData = new FormData();

			// Add task information
			formData.append('taskId', task._id);
			formData.append('projectId', task.projectId);

			// Add files to FormData
			pendingFiles.forEach((filePreview) => {
				formData.append('media', filePreview.file);
			});

			// Add cover image if it was set from pending files
			const coverImage = taskForm.watch('coverImage');
			const coverFromPending = pendingFiles.find(
				(file) => file.name === coverImage
			);
			if (coverFromPending) {
				formData.append('coverImage', coverImage);
			}

			// Send FormData to backend
			await dispatch(
				updateTaskAttachments({
					taskId: task._id,
					projectId: task.projectId,
					taskData: formData,
				})
			);

			// Clear pending files after successful upload
			pendingFiles.forEach((file) => {
				if (file.url) {
					URL.revokeObjectURL(file.url); // Clean up object URLs
				}
			});
			setPendingFiles([]);
		} catch (error) {
			console.error('Error uploading pending files:', error);
		}
	};

	const handlePreviewModalClose = () => {
		setIsPreviewModalOpen(false);
		setPreviewAttachment(null);
	};

	return (
		<>
			<Dialog open={isOpen} onOpenChange={onClose}>
				<DialogContent
					hideClose={true}
					className="min-w-[64rem] h-[90vh] overflow-hidden p-0"
				>
					<Form {...taskForm}>
						<form onSubmit={taskForm.handleSubmit(handleUpdateTask)}>
							{/* Task Header Component */}
							<TaskHeader
								coverImage={getCoverImage()}
								onRemoveCoverImage={handleRemoveCoverImage}
								taskForm={taskForm}
								onClose={onClose}
							/>

							{/* Main Content - Two Columns */}
							<div className="flex h-[calc(90vh-10rem)]">
								<ScrollArea className="h-[calc(90vh-10rem)] flex-1">
									{/* Left Section - Larger */}
									<div className="p-6 pl-3 pr-3">
										{/* Task Title Component */}
										<TaskTitle taskForm={taskForm} />

										{/* Task Controls Component */}
										<TaskControls taskForm={taskForm} />

										{/* Task Description Component */}
										<TaskDescription
											descriptionForm={descriptionForm}
											onUpdateDescription={handleUpdateDescription}
											isGlassMode={isGlassMode}
										/>

										{/* Task Attachments Component */}
										<TaskAttachments
											attachments={getImageAttachments()}
											pendingFiles={pendingFiles}
											coverImageUrl={taskForm.watch('coverImage')}
											onSetCoverImage={handleSetCoverImage}
											onRemoveCoverImage={handleRemoveCoverImage}
											onAttachmentUpload={handleAttachmentUpload}
											onAttachmentPreview={handleAttachmentPreview}
											onAttachmentDownload={handleAttachmentDownload}
											onAttachmentDelete={handleAttachmentDelete}
											onPendingFileRemove={handlePendingFileRemove}
											onSubmitPendingFiles={handleSubmitPendingFiles}
										/>
									</div>
								</ScrollArea>

								{/* Task Comments Component */}
								<TaskComments
									task={task}
									commentForm={commentForm}
									onAddComment={handleAddComment}
								/>
							</div>
						</form>
					</Form>
				</DialogContent>
			</Dialog>

			{/* Attachment Preview Modal */}
			<AttachmentPreviewModal
				isOpen={isPreviewModalOpen}
				onClose={handlePreviewModalClose}
				attachment={previewAttachment}
				onDownload={handleAttachmentDownload}
			/>
		</>
	);
};
