'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import { CheckCircle, Flag, CalendarIcon, X } from 'lucide-react';
import { format } from 'date-fns';
import { cn, userRoles } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateTask } from '@/lib/features/tasks/tasksSlice';

/**
 * TaskControls Component
 * Renders assignee, status, priority, and due date controls in a single line
 */
export const TaskControls = ({ taskForm, onUpdateDueDate }) => {
	const { authenticatedUser } = useAppSelector((store) => store.auth);
	const { projectDetails } = useAppSelector((store) => store.projects);
	const { taskDetails } = useAppSelector((store) => store.tasks);
	const dispatch = useAppDispatch();

	const onStatusChange = (value) => {
		dispatch(
			updateTask({
				status: value,
				taskId: taskDetails._id,
				projectId: taskDetails.projectId,
			})
		);
	};

	const onPriorityChange = (value) => {
		dispatch(
			updateTask({
				priority: value,
				taskId: taskDetails._id,
				projectId: taskDetails.projectId,
			})
		);
	};

	const getPriorityColor = (priority) => {
		switch (priority) {
			case 'high':
				return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/30';
			case 'medium':
				return 'text-yellow-600 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/30';
			case 'low':
				return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/30';
			default:
				return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/30';
		}
	};

	const getStatusColor = (status) => {
		switch (status) {
			case 'completed':
				return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/30';
			case 'in-progress':
				return 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/30';
			case 'pending':
				return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/30';
			case 'cancelled':
				return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/30';
			case 'overdue':
				return 'text-orange-600 bg-orange-50 dark:text-orange-400 dark:bg-orange-900/30';
			default:
				return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/30';
		}
	};

	const handleDateSelect = (date) => {
		const formattedDate = date ? format(date, 'yyyy-MM-dd') : '';
		taskForm.setValue('dueDate', formattedDate);
		dispatch(
			updateTask({
				dueDate: formattedDate,
				taskId: taskDetails._id,
				projectId: taskDetails.projectId,
			})
		);
	};

	const handleClearDate = () => {
		taskForm.setValue('dueDate', '');
		dispatch(
			updateTask({
				dueDate: null,
				taskId: taskDetails._id,
				projectId: taskDetails.projectId,
			})
		);
	};

	const parseDate = (dateString) => {
		if (!dateString) return undefined;
		return new Date(dateString);
	};

	return (
		<div className="mb-6">
			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
				{/* Status */}
				<div className="flex items-center gap-2">
					<CheckCircle className="h-4 w-4 text-gray-500 dark:text-gray-400 flex-shrink-0" />
					<FormField
						control={taskForm.control}
						name="status"
						render={({ field }) => (
							<FormItem className="flex-1">
								<Select
									onValueChange={onStatusChange}
									value={taskDetails?.status || field.value}
								>
									<FormControl>
										<SelectTrigger
											className={`h-8 ${getStatusColor(taskDetails?.status || field.value)}`}
										>
											<SelectValue placeholder="Status" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{[
											'pending',
											'in-progress',
											'completed',
											'cancelled',
											'overdue',
										].map((status) => (
											<SelectItem key={status} value={status}>
												<span className="capitalize">
													{status.replace('-', ' ')}
												</span>
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				{authenticatedUser.role <= userRoles.CLIENT_ADMIN ||
				projectDetails.isProjectAdmin ? (
					<>
						{/* Priority */}
						<div className="flex items-center gap-2">
							<Flag className="h-4 w-4 text-gray-500 dark:text-gray-400 flex-shrink-0" />
							<FormField
								control={taskForm.control}
								name="priority"
								render={({ field }) => (
									<FormItem className="flex-1">
										<Select
											onValueChange={onPriorityChange}
											value={taskDetails?.priority || field.value}
										>
											<FormControl>
												<SelectTrigger
													className={`h-8 ${getPriorityColor(taskDetails?.priority || field.value)}`}
												>
													<SelectValue placeholder="Priority" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{['low', 'medium', 'high'].map((priority) => (
													<SelectItem key={priority} value={priority}>
														<span className="capitalize">{priority}</span>
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Due Date */}
						<div className="flex items-center gap-2">
							<CalendarIcon className="h-4 w-4 text-gray-500 dark:text-gray-400 flex-shrink-0" />
							<FormField
								control={taskForm.control}
								name="dueDate"
								render={({ field }) => (
									<FormItem className="flex-1">
										<div className="flex items-center gap-1">
											<Popover>
												<PopoverTrigger asChild>
													<FormControl>
														<Button
															variant="outline"
															className={cn(
																'h-8 flex-1 pl-3 text-left font-normal',
																!field.value && 'text-muted-foreground'
															)}
														>
															{field.value ? (
																format(parseDate(field.value), 'MMM dd')
															) : (
																<span>Due date</span>
															)}
															<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
														</Button>
													</FormControl>
												</PopoverTrigger>
												<PopoverContent className="w-auto p-0" align="start">
													<Calendar
														mode="single"
														selected={parseDate(field.value)}
														onSelect={handleDateSelect}
														disabled={(date) =>
															date < new Date(new Date().setHours(0, 0, 0, 0))
														}
														initialFocus
													/>
												</PopoverContent>
											</Popover>
											{field.value && (
												<Button
													type="button"
													variant="ghost"
													size="sm"
													onClick={handleClearDate}
													className="h-8 w-8 p-0"
												>
													<X className="h-4 w-4" />
												</Button>
											)}
										</div>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</>
				) : (
					<></>
				)}
			</div>
		</div>
	);
};
