import { useEffect, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	fetchSingleTaskDetails,
	updateTask,
} from '@/lib/features/tasks/tasksSlice';

/**
 * Custom hook for managing task data and attachments
 * Handles data fetching and attachment management
 */
export const useTaskData = (taskId, taskForm, updateFormsWithTaskData) => {
	const dispatch = useAppDispatch();
	const { taskDetails: task, isLoading } = useAppSelector(
		(store) => store.tasks
	);

	// Fetch task details when taskId changes
	useEffect(() => {
		if (taskId) {
			dispatch(fetchSingleTaskDetails(taskId));
		}
	}, [dispatch, taskId]);

	// Update forms when task data is loaded
	useEffect(() => {
		if (task && updateFormsWithTaskData) {
			updateFormsWithTaskData(task);
		}
	}, [task, updateFormsWithTaskData]);

	// Helper functions for cover image management
	const getImageAttachments = useCallback(() => {
		// return task?.media?.filter((att) => att.resource_type === 'image');
		return task?.media;
	}, [task]);

	const getCoverImage = useCallback(() => {
		const coverImageName = taskForm.watch('coverImage');
		if (!coverImageName) return null;
		return getImageAttachments().find(
			(att) => att.public_id === coverImageName || att.url === coverImageName
		);
	}, [taskForm, getImageAttachments]);

	const handleRemoveCoverImage = useCallback(() => {
		taskForm.setValue('coverImage', '');
		if (task?._id) {
			dispatch(
				updateTask({
					projectId: task.projectId,
					taskId: task._id,
					coverImage: '',
				})
			);
		}
	}, [taskForm, dispatch, task]);

	const handleSetCoverImage = useCallback(
		(attachmentName) => {
			taskForm.setValue('coverImage', attachmentName);
			if (task?._id) {
				dispatch(
					updateTask({
						projectId: task.projectId,
						taskId: task._id,
						coverImage: attachmentName,
					})
				);
			}
		},
		[taskForm, dispatch, task, getImageAttachments]
	);

	return {
		task,
		isLoading,
		getImageAttachments,
		getCoverImage,
		handleRemoveCoverImage,
		handleSetCoverImage,
	};
};
