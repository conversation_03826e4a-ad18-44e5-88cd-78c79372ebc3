import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback } from 'react';
import {
	updateTaskSchema,
	addCommentSchema,
	updateTaskDescriptionSchema,
} from '@/lib/schemas/taskSchemas';

/**
 * Custom hook for managing task-related forms
 * Handles form initialization, validation, and data synchronization
 */
export const useTaskForms = (taskId, isEditMode) => {
	// Form for updating task details
	const taskForm = useForm({
		resolver: zodResolver(updateTaskSchema),
		defaultValues: {
			taskId: taskId || '',
			name: '',
			assignedTo: '',
			dueDate: '',
			priority: 'medium',
			coverImage: '',
			color: '',
		},
	});

	// Form for adding comments
	const commentForm = useForm({
		resolver: zodResolver(addCommentSchema),
		defaultValues: {
			taskId: taskId || '',
			comment: '',
		},
	});

	// Form for updating description
	const descriptionForm = useForm({
		resolver: zodResolver(updateTaskDescriptionSchema),
		defaultValues: {
			taskId: taskId || '',
			description: '',
		},
	});

	// Function to update forms when task data is available
	// Using useCallback to prevent infinite re-renders
	const updateFormsWithTaskData = useCallback(
		(task) => {
			if (task && isEditMode) {
				taskForm.reset({
					taskId: task._id || taskId,
					name: task.name || '',
					assignedTo: task.assignedTo || '',
					dueDate: task.dueDate || '',
					priority: task.priority || 'medium',
					coverImage: task.coverImage || '',
					color: task.color || '',
				});

				descriptionForm.reset({
					taskId: task._id || taskId,
					description: task.description || '',
				});

				commentForm.reset({
					taskId: task._id || taskId,
					comment: '',
				});
			}
		},
		[taskId, isEditMode, taskForm, descriptionForm, commentForm]
	);

	return {
		taskForm,
		commentForm,
		descriptionForm,
		updateFormsWithTaskData,
	};
};
