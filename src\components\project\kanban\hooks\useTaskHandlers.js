import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateTask, addTaskComment } from '@/lib/features/tasks/tasksSlice';

/**
 * Custom hook for managing task-related handlers
 * Handles form submissions and API calls
 */
export const useTaskHandlers = (taskId, commentForm, onSave, onClose) => {
	const dispatch = useAppDispatch();
	const { projectDetails: currentProject } = useAppSelector(
		(store) => store.projects
	);

	// Form submission handlers
	const handleUpdateTask = useCallback(
		async (data) => {
			try {
				await dispatch(
					updateTask({
						projectId: currentProject?.details?._id,
						taskData: data,
					})
				);
				onSave?.();
				onClose();
			} catch (error) {
				console.error('Error updating task:', error);
			}
		},
		[dispatch, currentProject, onSave, onClose]
	);

	const handleAddComment = useCallback(
		async (data) => {
			try {
				await dispatch(
					addTaskComment({ ...data, projectId: currentProject?.details?._id })
				);
				commentForm.reset({
					taskId: taskId,
					comment: '',
				});
			} catch (error) {
				console.error('Error adding comment:', error);
			}
		},
		[dispatch, currentProject, commentForm, taskId]
	);

	const handleUpdateDescription = useCallback(
		async (data) => {
			try {
				await dispatch(
					updateTask({ projectId: currentProject?.details?._id, ...data })
				);
			} catch (error) {
				console.error('Error updating description:', error);
			}
		},
		[dispatch, currentProject]
	);

	return {
		handleUpdateTask,
		handleAddComment,
		handleUpdateDescription,
	};
};
