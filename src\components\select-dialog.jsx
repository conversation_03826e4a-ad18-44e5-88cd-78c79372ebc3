import { Button } from '@/components/ui/button';
import { useState } from 'react';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	Select,
	SelectContent,
	SelectGroup,
	SelectItem,
	SelectLabel,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';

export function SelectDialog({
	dialogTrigger = <Button variant="outline">Edit Profile</Button>,
	dialogTitle = 'Edit profile',
	dialogDescription = 'Edit you profile',
	dialogSubmitText = 'Save changes',
	selectLabel = 'Select User',
	selectItemList = [{}],
	open = true,
	setOpen = () => {},
	onSubmit = () => {
		console.log('submit');
	},
}) {
	const [selectedValue, setSelectedValue] = useState('');
	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>{dialogTrigger}</DialogTrigger>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>{dialogTitle}</DialogTitle>
					<DialogDescription>{dialogDescription}</DialogDescription>
				</DialogHeader>
				<div className="grid gap-4 py-4">
					<Select onValueChange={setSelectedValue}>
						<SelectTrigger className="w-full">
							<SelectValue placeholder={selectLabel} />
						</SelectTrigger>
						<SelectContent>
							<SelectGroup>
								{selectItemList.map((item) => (
									<SelectItem key={item.value} value={item.value}>
										{item.label}
									</SelectItem>
								))}
							</SelectGroup>
						</SelectContent>
					</Select>
				</div>
				<DialogFooter>
					<Button
						type="submit"
						onClick={() => onSubmit(selectedValue)}
						disabled={!selectedValue}
					>
						{dialogSubmitText}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
