'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm, useWatch } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
	PlusCircle,
	Trash2,
	MapPin,
	Wifi,
	QrCode,
	Eye,
	EthernetPort,
} from 'lucide-react';
import { useState, useEffect, useCallback, useMemo } from 'react';
import { Badge } from '@/components/ui/badge';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { LoadingSubmitButton } from './loading-component';
import {
	createShift,
	updateShift,
	fetchShiftById,
} from '@/lib/features/attendance/shiftSettingsSlice';
import axios from 'axios';
import { toast } from 'sonner';

const createShiftSettingSchema = z.object({
	type: z.enum(['generic', 'roster']).default('generic'),
	code: z.string().min(1, 'Attendance code is required'),
	name: z.string().min(1, 'Setting name is required'),
	isApprovalStatusEnabled: z.boolean().default(false),

	startTime: z
		.string()
		.regex(/^([0-1]\d|2[0-3]):([0-5]\d)$/, 'Invalid time format'),
	endTime: z
		.string()
		.regex(/^([0-1]\d|2[0-3]):([0-5]\d)$/, 'Invalid time format'),

	clockInLimit: z.number().int().nonnegative().default(1),
	clockOutLimit: z.number().int().nonnegative().default(1),
	breakLimit: z.number().int().nonnegative().default(3),
	clockInDelay: z.number().int().nonnegative().default(15),
	clockOutDelay: z.number().int().nonnegative().default(120),

	tardinessMinutes: z.number().int().nonnegative().default(0),
	tardinessMinutesPerMonth: z.number().int().nonnegative().default(0),

	workLocationCheckEnabled: z.boolean().default(false),

	weekStartsFrom: z
		.enum([
			'sunday',
			'monday',
			'tuesday',
			'wednesday',
			'thursday',
			'friday',
			'saturday',
		])
		.default('monday'),

	allowedAttendanceDays: z
		.array(
			z.enum([
				'monday',
				'tuesday',
				'wednesday',
				'thursday',
				'friday',
				'saturday',
				'sunday',
			])
		)
		.default(['monday', 'tuesday', 'wednesday', 'thursday', 'friday']),

	halfDayMark: z
		.string()
		// .regex(/^([0-1]\d|2[0-3]):([0-5]\d)$/)
		.optional(),
	maxLoginHours: z.number().optional(),
	minLoginHours: z.number().optional(),
	maxClockInTime: z.number().optional(),

	workLocation: z
		.array(
			z.object({
				latitude: z.number(),
				longitude: z.number(),
				radiusInMeters: z.number(),
			})
		)
		.optional(),

	wifiCheckEnabled: z.boolean().default(false),
	OfficeWifiIPAddress: z.array(z.string()).optional(),

	qrClockInCheckEnabled: z.boolean().default(false),
	qrClockInToken: z.string().optional(),
	facialCheckEnabled: z.boolean().default(false),
});

export function ShiftSettingForm({ mode = 'add', shiftId = null }) {
	const [wifiInput, setWifiInput] = useState('');
	const [locationInput, setLocationInput] = useState({
		latitude: '',
		longitude: '',
	});
	const dispatch = useAppDispatch();
	const { isLoading, currentShift } = useAppSelector((store) => store.shifts);

	// Determine if form should be readonly
	const isReadOnly = mode === 'view';
	const isEditMode = mode === 'edit';
	const isAddMode = mode === 'add';

	const defaultValues = useMemo(() => {
		return {
			type: 'generic',
			code: '',
			name: '',
			isApprovalStatusEnabled: false,
			startTime: '09:00',
			endTime: '17:00',
			clockInLimit: 1,
			clockOutLimit: 1,
			breakLimit: 3,
			clockInDelay: 15,
			clockOutDelay: 120,
			tardinessMinutes: 0,
			tardinessMinutesPerMonth: 0,
			workLocationCheckEnabled: false,
			weekStartsFrom: 'monday',
			allowedAttendanceDays: [
				'monday',
				'tuesday',
				'wednesday',
				'thursday',
				'friday',
			],
			halfDayMark: '',
			maxLoginHours: undefined,
			minLoginHours: undefined,
			maxClockInTime: undefined,
			workLocation: [],
			wifiCheckEnabled: false,
			OfficeWifiIPAddress: [],
			qrClockInCheckEnabled: false,
			// qrClockInToken: '',
			facialCheckEnabled: false,
		};
	}, []);

	const form = useForm({
		resolver: zodResolver(createShiftSettingSchema),
		defaultValues,
	});

	const {
		fields: locationFields,
		append: appendLocation,
		remove: removeLocation,
	} = useFieldArray({
		control: form.control,
		name: 'workLocation',
	});

	const watchFields = useWatch({
		control: form.control,
		name: [
			'workLocationCheckEnabled',
			'wifiCheckEnabled',
			'qrClockInCheckEnabled',
		],
	});

	const [workLocationCheckEnabled, wifiCheckEnabled, qrClockInCheckEnabled] =
		watchFields;
	const wifiAddresses = form.watch('OfficeWifiIPAddress') || [];

	const weekDays = [
		{ value: 'monday', label: 'Monday' },
		{ value: 'tuesday', label: 'Tuesday' },
		{ value: 'wednesday', label: 'Wednesday' },
		{ value: 'thursday', label: 'Thursday' },
		{ value: 'friday', label: 'Friday' },
		{ value: 'saturday', label: 'Saturday' },
		{ value: 'sunday', label: 'Sunday' },
	];

	// Fetch shift data for edit/view mode
	useEffect(() => {
		if ((isEditMode || isReadOnly) && shiftId) {
			dispatch(fetchShiftById(shiftId));
		}
	}, [dispatch, isEditMode, isReadOnly, shiftId]);

	// Populate form with fetched data
	useEffect(() => {
		if (currentShift && (isEditMode || isReadOnly)) {
			// Reset form with current shift data
			form.reset({
				...defaultValues,
				...currentShift,
				// Ensure arrays are properly set
				workLocation: currentShift.workLocation || [],
				OfficeWifiIPAddress: currentShift.OfficeWifiIPAddress || [],
			});
		}
	}, [currentShift, defaultValues, form, isEditMode, isReadOnly]);

	// Generate random code only for add mode
	useEffect(() => {
		if (isAddMode) {
			const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
			const code = Array.from({ length: 6 }, () =>
				charset.charAt(Math.floor(Math.random() * charset.length))
			).join('');
			form.setValue('code', code, { shouldDirty: true });
		}
	}, [form, isAddMode]);

	const fetchCurrentLocation = useCallback(() => {
		if (isReadOnly) return;

		if (!navigator.geolocation) {
			return toast.error('Geolocation is not supported by this browser');
		}

		navigator.geolocation.getCurrentPosition(
			(position) => {
				const { latitude, longitude } = position.coords;
				setLocationInput({
					latitude: latitude.toString(),
					longitude: longitude.toString(),
				});
				toast.success('Location fetched successfully!');
			},
			(error) => {
				let errorMessage = 'Unable to retrieve your location';
				switch (error.code) {
					case error.PERMISSION_DENIED:
						errorMessage = 'Location access denied by user';
						break;
					case error.POSITION_UNAVAILABLE:
						errorMessage = 'Location information is unavailable';
						break;
					case error.TIMEOUT:
						errorMessage = 'Location request timed out';
						break;
				}
				toast.error(errorMessage);
			},
			{
				enableHighAccuracy: true,
				timeout: 10000,
				maximumAge: 60000,
			}
		);
	}, [isReadOnly]);

	const addCurrentLocation = () => {
		if (isReadOnly || !locationInput.latitude || !locationInput.longitude)
			return;

		appendLocation({
			latitude: Number(locationInput.latitude),
			longitude: Number(locationInput.longitude),
			radiusInMeters: 100,
		});
		setLocationInput({ latitude: '', longitude: '' });
		toast.success('Current location added to work locations');
	};

	const fetchPublicIp = useCallback(async () => {
		if (isReadOnly) return;

		try {
			const { data } = await axios.get('https://api.ipify.org?format=json');
			setWifiInput(data?.ip);
		} catch (error) {
			return toast.error(
				'Something went wrong while fetching your ip, try entering manually'
			);
		}
	}, [isReadOnly]);

	const addWifiAddress = () => {
		if (isReadOnly || !wifiInput.trim()) return;

		const currentAddresses = form.getValues('OfficeWifiIPAddress') || [];
		form.setValue('OfficeWifiIPAddress', [
			...currentAddresses,
			wifiInput.trim(),
		]);
		setWifiInput('');
	};

	const removeWifiAddress = (index) => {
		if (isReadOnly) return;

		const currentAddresses = form.getValues('OfficeWifiIPAddress') || [];
		form.setValue(
			'OfficeWifiIPAddress',
			currentAddresses.filter((_, i) => i !== index)
		);
	};

	function onSubmit(data) {
		if (isReadOnly) return;

		if (isEditMode) {
			dispatch(updateShift({ shiftId, shiftData: data }));
		} else if (isAddMode) {
			dispatch(createShift(data));
		}
	}

	// Get appropriate title and button text
	const getTitle = () => {
		switch (mode) {
			case 'edit':
				return 'Edit Shift Setting';
			case 'view':
				return 'View Shift Setting';
			default:
				return 'Create Shift Setting';
		}
	};

	const getButtonText = () => {
		switch (mode) {
			case 'edit':
				return 'Update Shift';
			default:
				return 'Create Shift';
		}
	};

	const getLoadingText = () => {
		switch (mode) {
			case 'edit':
				return 'Updating Shift';
			default:
				return 'Creating Shift';
		}
	};

	// useEffect(() => {
	// 	const subscription = form.watch((value) => {
	// 		console.log(value);
	// 	});
	// 	return () => subscription.unsubscribe();
	// }, [form]);

	// useEffect(() => {
	// 	console.log(form.formState.errors);
	// }, [form.formState.errors]);

	return (
		<div className="space-y-6">
			{/* <div className="flex items-center justify-between">
				<h1 className="text-2xl font-bold">{getTitle()}</h1>
				{isReadOnly && (
					<Badge variant="secondary" className="text-sm">
						View Only
					</Badge>
				)}
			</div> */}

			<Form {...form}>
				<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
					{/* Basic Information */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								Basic Information
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-6">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								{!isEditMode && (
									<FormField
										control={form.control}
										name="type"
										render={({ field }) => (
											<FormItem className="space-y-3">
												<FormLabel>Shift Type</FormLabel>
												<FormControl>
													<RadioGroup
														onValueChange={field.onChange}
														value={field.value}
														className="flex flex-col space-y-1"
														disabled={isReadOnly}
													>
														<FormItem className="flex items-center space-x-3 space-y-0">
															<FormControl>
																<RadioGroupItem value="generic" />
															</FormControl>
															<FormLabel className="font-normal">
																Generic
															</FormLabel>
														</FormItem>
														<FormItem className="flex items-center space-x-3 space-y-0">
															<FormControl>
																<RadioGroupItem
																	value="roster"
																	disabled={isReadOnly}
																/>
															</FormControl>
															<FormLabel className="font-normal">
																Roster
															</FormLabel>
														</FormItem>
													</RadioGroup>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}

								<FormField
									control={form.control}
									name="code"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Attendance Code</FormLabel>
											<FormControl>
												<Input
													placeholder="Enter attendance code"
													{...field}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormDescription>
												{isAddMode
													? 'This code is randomly generated but can be edited once according to your preference.'
													: 'Unique code for this shift setting.'}
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Shift Name</FormLabel>
											<FormControl>
												<Input
													placeholder="Enter shift name"
													{...field}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<FormField
								control={form.control}
								name="isApprovalStatusEnabled"
								render={({ field }) => (
									<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
										<div className="space-y-0.5">
											<FormLabel className="text-base">
												Approval Required
											</FormLabel>
											<FormDescription>
												Enable post-attendance approval by a reporting manager
											</FormDescription>
										</div>
										<FormControl>
											<Switch
												checked={field.value}
												onCheckedChange={field.onChange}
												disabled={isReadOnly}
											/>
										</FormControl>
									</FormItem>
								)}
							/>
						</CardContent>
					</Card>

					{/* Time Settings */}
					<Card>
						<CardHeader>
							<CardTitle>Time Settings</CardTitle>
						</CardHeader>
						<CardContent className="space-y-6">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<FormField
									control={form.control}
									name="startTime"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Start Time</FormLabel>
											<FormControl>
												<Input
													type="time"
													{...field}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="endTime"
									render={({ field }) => (
										<FormItem>
											<FormLabel>End Time</FormLabel>
											<FormControl>
												<Input
													type="time"
													{...field}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<FormField
								control={form.control}
								name="halfDayMark"
								render={({ field }) => (
									<FormItem className="w-full md:w-1/2">
										<FormLabel>Half Day Mark (Optional)</FormLabel>
										<FormControl>
											<Input
												type="time"
												{...field}
												readOnly={isReadOnly}
												disabled={isReadOnly}
											/>
										</FormControl>
										<FormDescription>
											Time threshold for half-day calculation
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
								<FormField
									control={form.control}
									name="minLoginHours"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Minimum Login Hours</FormLabel>
											<FormControl>
												<Input
													type="number"
													min="0"
													step="0.5"
													placeholder="Enter minimum hours"
													value={field.value || ''}
													onChange={(e) =>
														field.onChange(
															e.target.value
																? Number(e.target.value)
																: undefined
														)
													}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="maxLoginHours"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Maximum Login Hours</FormLabel>
											<FormControl>
												<Input
													type="number"
													min="0"
													step="0.5"
													placeholder="Enter maximum hours"
													value={field.value || ''}
													onChange={(e) =>
														field.onChange(
															e.target.value
																? Number(e.target.value)
																: undefined
														)
													}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="maxClockInTime"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Max Clock In Time (minutes)</FormLabel>
											<FormControl>
												<Input
													type="number"
													min="0"
													placeholder="Enter minutes"
													value={field.value || ''}
													onChange={(e) =>
														field.onChange(
															e.target.value
																? Number(e.target.value)
																: undefined
														)
													}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormDescription>
												Maximum allowed minutes for late clock-in. Exceeding
												this will result in leave for the day.
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</CardContent>
					</Card>

					{/* Clock Settings */}
					<Card>
						<CardHeader>
							<CardTitle>Clock & Break Settings</CardTitle>
						</CardHeader>
						<CardContent className="space-y-6">
							<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
								<FormField
									control={form.control}
									name="clockInLimit"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Clock In Limit</FormLabel>
											<FormControl>
												<Input
													type="number"
													min="0"
													{...field}
													onChange={(e) =>
														field.onChange(Number(e.target.value))
													}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormDescription>
												Set how many times an employee can clock in for the day
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="clockOutLimit"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Clock Out Limit</FormLabel>
											<FormControl>
												<Input
													type="number"
													min="0"
													{...field}
													onChange={(e) =>
														field.onChange(Number(e.target.value))
													}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormDescription>
												Set how many times an employee can clock out for the day
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="breakLimit"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Max break time per day (minutes)</FormLabel>
											<FormControl>
												<Input
													type="number"
													min="0"
													{...field}
													onChange={(e) =>
														field.onChange(Number(e.target.value))
													}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormDescription>
												Set how many breaks an employee can have for the day
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<FormField
									control={form.control}
									name="clockInDelay"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Clock In Delay (minutes)</FormLabel>
											<FormControl>
												<Input
													type="number"
													min="0"
													{...field}
													onChange={(e) =>
														field.onChange(Number(e.target.value))
													}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormDescription>
												This restricts employee to clock out for above mentioned
												minutes after clocking in
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="clockOutDelay"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Clock Out Delay (minutes)</FormLabel>
											<FormControl>
												<Input
													type="number"
													min="0"
													{...field}
													onChange={(e) =>
														field.onChange(Number(e.target.value))
													}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormDescription>
												This restricts employee to clock in for above mentioned
												minutes after clocking out
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<FormField
									control={form.control}
									name="tardinessMinutes"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Tardiness Minutes Per Day</FormLabel>
											<FormControl>
												<Input
													type="number"
													min="0"
													{...field}
													onChange={(e) =>
														field.onChange(Number(e.target.value))
													}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormDescription>
												Late clock-in or early clock-out minutes are considered
												as tardiness
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="tardinessMinutesPerMonth"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Tardiness Minutes Per Month</FormLabel>
											<FormControl>
												<Input
													type="number"
													min="0"
													{...field}
													onChange={(e) =>
														field.onChange(Number(e.target.value))
													}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormDescription>
												Monthly maximum tardiness limit
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</CardContent>
					</Card>

					{/* Work Schedule */}
					<Card>
						<CardHeader>
							<CardTitle>Work Schedule</CardTitle>
						</CardHeader>
						<CardContent className="space-y-6">
							<FormField
								control={form.control}
								name="weekStartsFrom"
								render={({ field }) => (
									<FormItem className="w-full md:w-1/2">
										<FormLabel>Week Starts From</FormLabel>
										<Select
											onValueChange={field.onChange}
											value={field.value}
											disabled={isReadOnly}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select day" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{weekDays.map((day) => (
													<SelectItem key={day.value} value={day.value}>
														{day.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="allowedAttendanceDays"
								render={() => (
									<FormItem>
										<div className="mb-4">
											<FormLabel className="text-base">
												Allowed Attendance Days
											</FormLabel>
											<FormDescription>
												Select the days when attendance is allowed
											</FormDescription>
										</div>
										<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
											{weekDays.map((day) => (
												<FormField
													key={day.value}
													control={form.control}
													name="allowedAttendanceDays"
													render={({ field }) => {
														return (
															<FormItem
																key={day.value}
																className="flex flex-row items-start space-x-3 space-y-0"
															>
																<FormControl>
																	<Checkbox
																		checked={field.value?.includes(day.value)}
																		onCheckedChange={(checked) => {
																			return checked
																				? field.onChange([
																						...field.value,
																						day.value,
																					])
																				: field.onChange(
																						field.value?.filter(
																							(value) => value !== day.value
																						)
																					);
																		}}
																		disabled={isReadOnly}
																	/>
																</FormControl>
																<FormLabel className="font-normal">
																	{day.label}
																</FormLabel>
															</FormItem>
														);
													}}
												/>
											))}
										</div>
										<FormMessage />
									</FormItem>
								)}
							/>
						</CardContent>
					</Card>

					{/* Location & Security Settings */}
					<Card>
						<CardHeader>
							<CardTitle>Location & Security Settings</CardTitle>
						</CardHeader>
						<CardContent className="space-y-6">
							{/* Work Location Check */}
							<FormField
								control={form.control}
								name="workLocationCheckEnabled"
								render={({ field }) => (
									<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
										<div className="space-y-0.5">
											<FormLabel className="text-base flex items-center gap-2">
												<MapPin className="h-4 w-4" />
												Work Location Check
											</FormLabel>
											<FormDescription>
												Enable location-based attendance verification
											</FormDescription>
										</div>
										<FormControl>
											<Switch
												checked={field.value}
												onCheckedChange={field.onChange}
												disabled={isReadOnly}
											/>
										</FormControl>
									</FormItem>
								)}
							/>

							{workLocationCheckEnabled && (
								<div className="space-y-4">
									<div className="flex justify-between items-center flex-wrap gap-2">
										<h4 className="text-sm font-medium">Work Locations</h4>
										{!isReadOnly && (
											<div className="flex gap-2 flex-wrap">
												<Button
													type="button"
													variant="outline"
													size="sm"
													onClick={() =>
														appendLocation({
															latitude: 0,
															longitude: 0,
															radiusInMeters: 100,
														})
													}
												>
													<PlusCircle className="mr-2 h-4 w-4" />
													Add Location
												</Button>
												<Button
													type="button"
													variant="secondary"
													size="sm"
													onClick={fetchCurrentLocation}
												>
													<MapPin className="mr-2 h-4 w-4" />
													Get Current Location
												</Button>
												{locationInput.latitude && locationInput.longitude && (
													<Button
														type="button"
														variant="default"
														size="sm"
														onClick={addCurrentLocation}
													>
														Add Current Location
													</Button>
												)}
											</div>
										)}
									</div>
									{locationInput.latitude &&
										locationInput.longitude &&
										!isReadOnly && (
											<div className="bg-green-50 border border-green-200 rounded-lg p-4">
												<p className="text-sm text-green-800 mb-2">
													Current Location Detected:
												</p>
												<div className="grid grid-cols-2 gap-4 text-sm">
													<div>
														<span className="font-medium">Latitude:</span>{' '}
														{locationInput.latitude}
													</div>
													<div>
														<span className="font-medium">Longitude:</span>{' '}
														{locationInput.longitude}
													</div>
												</div>
											</div>
										)}

									{locationFields.map((field, index) => (
										<Card key={field.id} className="relative">
											<CardContent className="pt-6">
												<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
													<FormField
														control={form.control}
														name={`workLocation.${index}.latitude`}
														render={({ field }) => (
															<FormItem>
																<FormLabel>Latitude</FormLabel>
																<FormControl>
																	<Input
																		type="number"
																		step="any"
																		placeholder="Enter latitude"
																		{...field}
																		onChange={(e) =>
																			field.onChange(Number(e.target.value))
																		}
																		readOnly={isReadOnly}
																		disabled={isReadOnly}
																	/>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>
													<FormField
														control={form.control}
														name={`workLocation.${index}.longitude`}
														render={({ field }) => (
															<FormItem>
																<FormLabel>Longitude</FormLabel>
																<FormControl>
																	<Input
																		type="number"
																		step="any"
																		placeholder="Enter longitude"
																		{...field}
																		onChange={(e) =>
																			field.onChange(Number(e.target.value))
																		}
																		readOnly={isReadOnly}
																		disabled={isReadOnly}
																	/>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>
													<FormField
														control={form.control}
														name={`workLocation.${index}.radiusInMeters`}
														render={({ field }) => (
															<FormItem>
																<FormLabel>Radius (meters)</FormLabel>
																<FormControl>
																	<Input
																		type="number"
																		min="1"
																		placeholder="Enter radius"
																		{...field}
																		onChange={(e) =>
																			field.onChange(Number(e.target.value))
																		}
																		readOnly={isReadOnly}
																		disabled={isReadOnly}
																	/>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>
												</div>
												{!isReadOnly && (
													<Button
														type="button"
														variant="destructive"
														size="sm"
														className="absolute top-2 right-2"
														onClick={() => removeLocation(index)}
													>
														<Trash2 className="h-4 w-4" />
													</Button>
												)}
											</CardContent>
										</Card>
									))}
								</div>
							)}

							{/* WiFi Check */}
							<FormField
								control={form.control}
								name="wifiCheckEnabled"
								render={({ field }) => (
									<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
										<div className="space-y-0.5">
											<FormLabel className="text-base flex items-center gap-2">
												<Wifi className="h-4 w-4" />
												WiFi Check
											</FormLabel>
											<FormDescription>
												Enable WiFi-based attendance verification
											</FormDescription>
										</div>
										<FormControl>
											<Switch
												checked={field.value}
												onCheckedChange={field.onChange}
												disabled={isReadOnly}
											/>
										</FormControl>
									</FormItem>
								)}
							/>

							{wifiCheckEnabled && (
								<div className="space-y-4">
									<div className="flex justify-between items-center flex-wrap gap-2">
										<h4 className="text-sm font-medium">
											Office WiFi IP Addresses
										</h4>
										{!isReadOnly && (
											<div className="flex gap-2 flex-wrap">
												<Button
													type="button"
													variant="secondary"
													size="sm"
													onClick={fetchPublicIp}
												>
													<EthernetPort className="mr-2 h-4 w-4" />
													Get Current IP
												</Button>
											</div>
										)}
									</div>

									{!isReadOnly && (
										<div className="flex gap-2">
											<Input
												placeholder="Enter IP address"
												value={wifiInput}
												onChange={(e) => setWifiInput(e.target.value)}
											/>
											<Button
												type="button"
												onClick={addWifiAddress}
												disabled={!wifiInput.trim()}
											>
												Add
											</Button>
										</div>
									)}

									<div className="space-y-2">
										{wifiAddresses.map((address, index) => (
											<div
												key={index}
												className="flex items-center justify-between border p-3 rounded-lg"
											>
												<span className="font-mono text-sm">{address}</span>
												{!isReadOnly && (
													<Button
														type="button"
														variant="ghost"
														size="sm"
														onClick={() => removeWifiAddress(index)}
													>
														<Trash2 className="h-4 w-4" />
													</Button>
												)}
											</div>
										))}
									</div>
								</div>
							)}

							{/* QR Code Check */}
							<FormField
								control={form.control}
								name="qrClockInCheckEnabled"
								render={({ field }) => (
									<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
										<div className="space-y-0.5">
											<FormLabel className="text-base flex items-center gap-2">
												<QrCode className="h-4 w-4" />
												QR Code Check
											</FormLabel>
											<FormDescription>
												Enable QR code-based attendance verification
											</FormDescription>
										</div>
										<FormControl>
											<Switch
												checked={field.value}
												onCheckedChange={field.onChange}
												disabled={isReadOnly}
											/>
										</FormControl>
									</FormItem>
								)}
							/>
							{/* 
							{qrClockInCheckEnabled && (
								<FormField
									control={form.control}
									name="qrClockInToken"
									render={({ field }) => (
										<FormItem>
											<FormLabel>QR Code Token</FormLabel>
											<FormControl>
												<Input
													placeholder="Enter QR code token"
													{...field}
													readOnly={isReadOnly}
													disabled={isReadOnly}
												/>
											</FormControl>
											<FormDescription>
												Unique token for QR code verification
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							)} */}

							{/* Facial Recognition Check */}
							<FormField
								control={form.control}
								name="facialCheckEnabled"
								render={({ field }) => (
									<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
										<div className="space-y-0.5">
											<FormLabel className="text-base flex items-center gap-2">
												<Eye className="h-4 w-4" />
												Facial Recognition Check
											</FormLabel>
											<FormDescription>
												Enable facial recognition-based attendance verification
											</FormDescription>
										</div>
										<FormControl>
											<Switch
												checked={field.value}
												onCheckedChange={field.onChange}
												disabled={isReadOnly}
											/>
										</FormControl>
									</FormItem>
								)}
							/>
						</CardContent>
					</Card>

					{/* Submit Button */}
					{!isReadOnly && (
						<div className="flex justify-end">
							<LoadingSubmitButton
								loading={isLoading}
								loadingText={getLoadingText()}
								buttonText={getButtonText()}
							/>
						</div>
					)}
				</form>
			</Form>
		</div>
	);
}
