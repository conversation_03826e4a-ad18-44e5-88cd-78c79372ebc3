'use client';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from './ui/form';
import Link from 'next/link';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { Loader2 } from 'lucide-react';
import { registerEmailSchema } from '@/lib/schemas/authenticationSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import {
	registerEmail,
	storeEmailInState,
} from '@/lib/features/auth/authSlice';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { LoadingSubmitButton } from './loading-component';

export default function SignUpVerifyEmailForm({ className, ...props }) {
	const form = useForm({
		resolver: zodResolver(registerEmailSchema),
		defaultValues: {
			email: '',
		},
	});

	const { isLoading } = useAppSelector((store) => store.auth);

	const dispatch = useAppDispatch();

	const onSubmit = (data) => {
		// console.log(`onSubmit - data:`, data);
		dispatch(storeEmailInState(data.email));
		dispatch(registerEmail(data));
	};
	return (
		<Form>
			<Form {...form}>
				<form
					className={cn('flex flex-col gap-6', className)}
					{...props}
					onSubmit={form.handleSubmit(onSubmit)}
				>
					<div className="flex flex-col items-center gap-2 text-center">
						<h1 className="text-2xl font-bold">Sign up to your account</h1>
						<p className="text-balance text-sm text-muted-foreground">
							Enter your email below to verify
						</p>
					</div>
					<div className="grid gap-6">
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Email</FormLabel>
									<FormControl>
										<Input
											{...field}
											type="email"
											autoComplete="email"
											autoFocus
											placeholder="Email"
										/>
									</FormControl>
									<FormDescription>Enter your company email</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<LoadingSubmitButton
							isLoading={isLoading}
							buttonLoadingText={'Registering...'}
							buttonText={'Register'}
						/>
					</div>
					<div className="text-center text-sm">
						Already have an account?{' '}
						<Link href="/login" className="underline underline-offset-4">
							Login
						</Link>
					</div>
				</form>
			</Form>
		</Form>
	);
}
