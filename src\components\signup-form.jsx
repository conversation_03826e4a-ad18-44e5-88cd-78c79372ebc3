'use client';

import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/navigation';
import {
	registerEmail,
	verifyEmail,
	registerUser,
} from '@/lib/features/auth/authSlice';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

export function SignUpForm({ className, ...props }) {
	const dispatch = useDispatch();
	const router = useRouter();
	const { isLoading, user, authenticatedUser, isVerified, enterToken } =
		useSelector((store) => store.auth);
	const [password, setPassword] = useState('');
	const [passwordCriteria, setPasswordCriteria] = useState({
		length: false,
		lowercase: false,
		uppercase: false,
		number: false,
		special: false,
		nonNumericEnds: false,
		notUsername: true,
	});

	// Debug: Log authentication state changes
	useEffect(() => {
		console.log('Auth State Changed:', {
			isLoading,
			user,
			authenticatedUser,
			isVerified,
			enterToken,
		});
	}, [isLoading, user, authenticatedUser, isVerified, enterToken]);

	const checkPasswordCriteria = (password) => {
		console.log('Checking password criteria for:', password);
		const criteria = {
			length: password.length >= 8,
			lowercase: /[a-z]/.test(password),
			uppercase: /[A-Z]/.test(password),
			number: /\d/.test(password),
			special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
			nonNumericEnds: /^[^0-9].*[^0-9]$/.test(password),
			notUsername: true,
		};
		console.log('Password criteria results:', criteria);
		setPasswordCriteria(criteria);
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		try {
			const formData = new FormData(e.currentTarget);
			const userDetails = Object.fromEntries(formData);
			const { email, password, verificationToken } = userDetails;

			console.log('Form submission state:', {
				enterToken,
				isVerified,
				email: email || user?.email,
				hasPassword: !!password,
				hasToken: !!verificationToken,
			});

			if (enterToken) {
				console.log('Verifying email with token:', verificationToken);
				const result = await dispatch(
					verifyEmail({
						email: user.email,
						verificationToken,
					})
				).unwrap();
				console.log('Email verification result:', result);
				return;
			}

			if (isVerified) {
				if (Object.values(passwordCriteria).every(Boolean)) {
					console.log('Registering user with password');
					const result = await dispatch(
						registerUser({
							password,
							email: user.email,
						})
					).unwrap();
					console.log('User registration result:', result);

					if (authenticatedUser) {
						console.log('Redirecting to onboarding');
						router.push('/onboarding');
					}
					e.currentTarget.reset();
				} else {
					console.error('Password criteria not met:', passwordCriteria);
					toast.error(
						'Please ensure all password criteria are met before submitting.'
					);
				}
				return;
			}

			console.log('Registering email:', email);
			const emailResult = await dispatch(registerEmail({ email })).unwrap();
			console.log('Email registration result:', emailResult);
		} catch (error) {
			console.error('Signup process error:', error);
			toast.error(
				error.message || 'An error occurred during the signup process'
			);
		}
	};

	return (
		<div className={cn('grid gap-6', className)} {...props}>
			<form onSubmit={handleSubmit}>
				<div className="grid gap-2">
					<div className="grid gap-1">
						<h1 className="text-2xl font-semibold tracking-tight">
							{enterToken
								? 'Verify Email'
								: isVerified
									? 'Set Password'
									: 'Create an account'}
						</h1>
						<p className="text-sm text-muted-foreground">
							{enterToken
								? 'Enter the verification code sent to your email'
								: isVerified
									? 'Create a secure password for your account'
									: 'Enter your email below to create your account'}
						</p>
					</div>
					<div className="grid gap-4">
						{!isVerified && !enterToken && (
							<div className="grid gap-2">
								<Label htmlFor="email">Email</Label>
								<Input
									id="email"
									name="email"
									placeholder="<EMAIL>"
									type="email"
									autoCapitalize="none"
									autoComplete="email"
									autoCorrect="off"
									disabled={isLoading}
									required
								/>
							</div>
						)}
						{enterToken && (
							<div className="grid gap-2">
								<Label htmlFor="verificationToken">Verification Code</Label>
								<Input
									id="verificationToken"
									name="verificationToken"
									placeholder="Enter verification code"
									type="text"
									disabled={isLoading}
									required
								/>
							</div>
						)}
						{isVerified && (
							<div className="grid gap-2">
								<Label htmlFor="password">Password</Label>
								<Input
									id="password"
									name="password"
									placeholder="Enter your password"
									type="password"
									disabled={isLoading}
									required
									value={password}
									onChange={(e) => {
										setPassword(e.target.value);
										checkPasswordCriteria(e.target.value);
									}}
								/>
								<div className="text-sm text-muted-foreground">
									<p>Password must:</p>
									<ul className="list-disc pl-4">
										<li
											className={
												passwordCriteria.length ? 'text-green-500' : ''
											}
										>
											Be at least 8 characters long
										</li>
										<li
											className={
												passwordCriteria.lowercase ? 'text-green-500' : ''
											}
										>
											Contain at least one lowercase letter
										</li>
										<li
											className={
												passwordCriteria.uppercase ? 'text-green-500' : ''
											}
										>
											Contain at least one uppercase letter
										</li>
										<li
											className={
												passwordCriteria.number ? 'text-green-500' : ''
											}
										>
											Contain at least one number
										</li>
										<li
											className={
												passwordCriteria.special ? 'text-green-500' : ''
											}
										>
											Contain at least one special character
										</li>
										<li
											className={
												passwordCriteria.nonNumericEnds ? 'text-green-500' : ''
											}
										>
											Not start or end with a number
										</li>
									</ul>
								</div>
							</div>
						)}
					</div>
					<Button className="mt-4" disabled={isLoading}>
						{isLoading ? (
							<div className="flex items-center gap-2">
								<div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
								Please wait
							</div>
						) : enterToken ? (
							'Verify Email'
						) : isVerified ? (
							'Create Account'
						) : (
							'Sign Up'
						)}
					</Button>
				</div>
			</form>
		</div>
	);
}
