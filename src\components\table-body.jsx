import { TableBody, TableCell, TableRow } from '@/components/ui/table';
import { flexRender } from '@tanstack/react-table';
import { SimpleLoader } from './loading-component';
import { Loader2 } from 'lucide-react';
export function TBody({ table, isLoading }) {
	// console.log('Table rows:', table.getRowModel().rows);
	// console.log('Is loading:', isLoading);

	return (
		<TableBody>
			{table.getRowModel().rows?.length ? (
				table.getRowModel().rows.map((row) => {
					// console.log('Rendering row:', row);
					return (
						<TableRow
							key={row.id}
							data-state={row.getIsSelected() && 'selected'}
						>
							{row.getVisibleCells().map((cell) => (
								<TableCell key={cell.id}>
									{flexRender(cell.column.columnDef.cell, cell.getContext())}
								</TableCell>
							))}
						</TableRow>
					);
				})
			) : isLoading ? (
				<TableRow>
					<TableCell
						colSpan={table.getAllColumns().length}
						className="h-24 text-center"
					>
						<div className="w-full h-full flex items-center justify-center">
							<Loader2 className="animate-spin" />
						</div>
					</TableCell>
				</TableRow>
			) : (
				<TableRow>
					<TableCell
						colSpan={table.getAllColumns().length}
						className="h-24 text-center"
					>
						No results.
					</TableCell>
				</TableRow>
			)}
		</TableBody>
	);
}
