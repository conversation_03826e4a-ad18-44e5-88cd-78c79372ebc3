'use client';

import { useState, useEffect } from 'react';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';
const defaultColors = [
	'FFFFFF', // White (default)
	'FFADAD', // Light Red
	'FFD6A5', // Light Orange
	'FDFFB6', // Light Yellow
	'CAFFBF', // Light Green
	'9BF6FF', // Light Cyan
	'A0C4FF', // Light Blue
	'BDB2FF', // Light Purple
	'FFC6FF', // Light Pink
	'E0E0E0', // Light Gray
];

export default function ColorPickerInput({
	value,
	onChange,
	availableColors = defaultColors,
}) {
	const [selectedColor, setSelectedColor] = useState(value || 'FFFFFF');

	useEffect(() => {
		setSelectedColor(value || 'FFFFFF');
	}, [value]);

	const handleColorSelect = (color) => {
		setSelectedColor(color);
		if (onChange) {
			onChange(color);
		}
	};

	return (
		<div className="space-y-2">
			<div className="flex flex-wrap gap-2">
				{availableColors.map((color) => (
					<button
						key={color}
						type="button"
						title={`#${color}`}
						className={cn(
							'w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all',
							selectedColor.toUpperCase() === color.toUpperCase()
								? 'border-primary ring-2 ring-primary ring-offset-1'
								: 'border-muted',
							'hover:opacity-80'
						)}
						style={{ backgroundColor: `#${color}` }}
						onClick={() => handleColorSelect(color)}
					>
						{selectedColor.toUpperCase() === color.toUpperCase() && (
							<Check
								className="w-4 h-4"
								style={{
									color:
										Number.parseInt(color, 16) > 0xffffff / 2
											? '#000000'
											: '#FFFFFF',
								}} // Contrast check
							/>
						)}
						<span className="sr-only">Select color #{color}</span>
					</button>
				))}
			</div>
			<div className="flex items-center gap-2 text-sm">
				<span className="text-muted-foreground">Selected:</span>
				<div
					className="w-5 h-5 rounded-sm border"
					style={{ backgroundColor: `#${selectedColor}` }}
				></div>
				<span>#{selectedColor.toUpperCase()}</span>
			</div>
		</div>
	);
}
