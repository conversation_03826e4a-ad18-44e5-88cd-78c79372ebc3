'use client';
import { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver as rhfResolver } from '@hookform/resolvers/zod';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogDescription,
	DialogFooter,
	DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { CalendarIcon } from 'lucide-react';
import MediaUploader from './media-uploader';
import ColorPickerInput from './color-picker-input';
import { formatDate } from '@/lib/utils';
import { useAppDispatch } from '@/lib/hooks';
import { createTask } from '@/lib/features/tasks/tasksSlice';

// Zod schema based on user input
const createTaskSchema = z.object({
	name: z
		.string()
		.nonempty('Task name is required')
		.max(50, 'Task name must be at most 50 characters long'),
	code: z
		.string()
		.nonempty('Project code is required')
		.max(6, 'Project code must be at most 6 characters long'),
	description: z.string().optional(),
	status: z
		.enum(['pending', 'completed', 'in-progress', 'cancelled', 'overdue'])
		.default('pending'),
	assignedTo: z.string().optional(), // Assuming ID string
	assignedBy: z.string().optional(), // Assuming ID string, could be pre-filled with current user
	projectId: z.string().nonempty('Project is required'), // Assuming ID string
	dueDate: z.date({ required_error: 'Due date is required.' }),
	priority: z.enum(['low', 'medium', 'high']).default('low'),
	color: z
		.string()
		.regex(/^([A-Fa-f0-9]{6})$/, 'Color must be a valid 6-digit hex color code') // Enforce 6 digits
		.default('ffffff'),
	media: z.array(z.instanceof(File)).optional().default([]),
});

export default function CreateTaskDialog({
	isOpen,
	onClose,
	onTaskCreated,
	projects,
	users,
}) {
	const dispatch = useAppDispatch();
	const form = useForm({
		resolver: rhfResolver(createTaskSchema),
		defaultValues: {
			name: '',
			code: '',
			description: '',
			status: 'pending',
			assignedTo: '',
			projectId: '',
			dueDate: undefined,
			priority: 'low',
			color: 'ffffff',
			media: [],
		},
	});

	const onSubmit = (data) => {
		// Convert dueDate to ISO string if it's a Date object
		const submissionData = {
			...data,
			dueDate: data.dueDate ? data.dueDate.toISOString().split('T')[0] : null, // YYYY-MM-DD
			// 'media' files are already in the data from MediaUploader
		};
		console.log('Form data submitted:', submissionData);
		dispatch(createTask(submissionData));
		onTaskCreated(submissionData);
		form.reset();
		onClose();
	};

	const handleMediaUpdate = useCallback(
		(files) => {
			form.setValue('media', files, { shouldValidate: true });
		},
		[form]
	);

	return (
		<Dialog
			open={isOpen}
			onOpenChange={(open) => {
				if (!open) form.reset();
				onClose();
			}}
		>
			<DialogContent className="sm:max-w-lg md:max-w-xl max-h-[90vh] flex flex-col">
				<DialogHeader>
					<DialogTitle>Create New Task</DialogTitle>
					<DialogDescription>
						Fill in the details below to create a new task.
					</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-4 flex-grow overflow-y-auto pr-2 py-4"
					>
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Task Name</FormLabel>
									<FormControl>
										<Input placeholder="Enter task name" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="code"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Task Code</FormLabel>
									<FormControl>
										<Input placeholder="e.g., PROJ-123" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Description (Optional)</FormLabel>
									<FormControl>
										<Textarea placeholder="Enter task description" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="status"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Status</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select status" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{[
													'pending',
													'in-progress',
													'completed',
													'cancelled',
													'overdue',
												].map((s) => (
													<SelectItem key={s} value={s} className="capitalize">
														{s.replace('-', ' ')}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="priority"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Priority</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select priority" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{['low', 'medium', 'high'].map((p) => (
													<SelectItem key={p} value={p} className="capitalize">
														{p}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="projectId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Project</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select project" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{projects.map((p) => (
													<SelectItem key={p.id} value={p.id}>
														{p.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="assignedTo"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Assign To (Optional)</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select user" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{users.map((u) => (
													<SelectItem key={u.id} value={u.id}>
														{u.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<FormField
							control={form.control}
							name="dueDate"
							render={({ field }) => (
								<FormItem className="flex flex-col">
									<FormLabel>Due Date</FormLabel>
									<Popover>
										<PopoverTrigger asChild>
											<FormControl>
												<Button
													variant={'outline'}
													className={`w-full pl-3 text-left font-normal ${!field.value && 'text-muted-foreground'}`}
												>
													{field.value ? (
														formatDate(field.value)
													) : (
														<span>Pick a date</span>
													)}
													<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
												</Button>
											</FormControl>
										</PopoverTrigger>
										<PopoverContent className="w-auto p-0" align="start">
											<Calendar
												mode="single"
												selected={field.value}
												onSelect={field.onChange}
												disabled={(date) =>
													date <
													new Date(new Date().setDate(new Date().getDate() - 1))
												} // Disable past dates
												initialFocus
											/>
										</PopoverContent>
									</Popover>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="color"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Color Tag</FormLabel>
									{/* Using field.value and field.onChange directly */}
									<ColorPickerInput
										value={field.value}
										onChange={field.onChange}
									/>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="media"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Attachments</FormLabel>
									<MediaUploader onFilesChange={handleMediaUpdate} />
									<FormMessage />
								</FormItem>
							)}
						/>
					</form>
				</Form>
				<DialogFooter className="pt-4 border-t">
					<DialogClose asChild>
						<Button variant="ghost" onClick={() => form.reset()}>
							Cancel
						</Button>
					</DialogClose>
					<Button
						type="submit"
						onClick={form.handleSubmit(onSubmit)}
						disabled={form.formState.isSubmitting}
					>
						{form.formState.isSubmitting ? 'Creating...' : 'Create Task'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
