'use client';
import { useState, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Paperclip, X, FileText, ImageIcon, VideoIcon } from 'lucide-react';
import Image from 'next/image';

export default function MediaUploader({ onFilesChange }) {
	const [selectedFiles, setSelectedFiles] = useState([]);
	const [filePreviews, setFilePreviews] = useState([]);

	const handleFileChange = useCallback(
		(event) => {
			const files = Array.from(event.target.files);
			const newFiles = [...selectedFiles, ...files];
			setSelectedFiles(newFiles);
			onFilesChange(newFiles); // Propagate File objects

			const newPreviews = files.map((file) => {
				const reader = new FileReader();
				return new Promise((resolve) => {
					reader.onloadend = () => {
						resolve({
							name: file.name,
							type: file.type,
							previewUrl: reader.result,
							file: file,
						});
					};
					if (
						file.type.startsWith('image/') ||
						file.type.startsWith('video/')
					) {
						reader.readAsDataURL(file);
					} else {
						// For non-image/video, just resolve with name and type
						resolve({ name: file.name, type: file.type, file: file });
					}
				});
			});

			Promise.all(newPreviews).then((previews) => {
				setFilePreviews((prev) => [...prev, ...previews]);
			});
		},
		[selectedFiles, onFilesChange]
	);

	const removeFile = useCallback(
		(fileToRemove) => {
			const updatedFiles = selectedFiles.filter((f) => f !== fileToRemove.file);
			setSelectedFiles(updatedFiles);
			onFilesChange(updatedFiles);

			setFilePreviews((prev) =>
				prev.filter((p) => p.file !== fileToRemove.file)
			);
		},
		[selectedFiles, onFilesChange]
	);

	const getFileIcon = (fileType) => {
		if (fileType.startsWith('image/'))
			return <ImageIcon className="w-6 h-6 text-blue-500" />;
		if (fileType.startsWith('video/'))
			return <VideoIcon className="w-6 h-6 text-purple-500" />;
		return <FileText className="w-6 h-6 text-gray-500" />;
	};

	return (
		<div className="space-y-3">
			<div className="flex items-center gap-2">
				<Paperclip className="w-5 h-5 text-muted-foreground" />
				<Input
					type="file"
					multiple
					onChange={handleFileChange}
					className="hidden"
					id="file-upload-input"
				/>
				<Button
					type="button"
					variant="outline"
					size="sm"
					onClick={() => document.getElementById('file-upload-input').click()}
				>
					Add Files
				</Button>
			</div>
			{filePreviews.length > 0 && (
				<ScrollArea className="h-40 w-full rounded-md border p-2">
					<div className="space-y-2">
						{filePreviews.map((item, index) => (
							<div
								key={index}
								className="flex items-center justify-between p-2 bg-muted/50 rounded-md text-sm"
							>
								<div className="flex items-center gap-2 overflow-hidden">
									{item.type.startsWith('image/') && item.previewUrl ? (
										<Image
											width={32}
											height={32}
											src={item.previewUrl || '/placeholder.svg'}
											alt={item.name}
											className="w-8 h-8 object-cover rounded"
										/>
									) : (
										getFileIcon(item.type)
									)}
									<span className="truncate" title={item.name}>
										{item.name}
									</span>
								</div>
								<Button
									variant="ghost"
									size="icon"
									onClick={() => removeFile(item)}
									className="w-6 h-6"
								>
									<X className="w-4 h-4" />
									<span className="sr-only">Remove {item.name}</span>
								</Button>
							</div>
						))}
					</div>
				</ScrollArea>
			)}
		</div>
	);
}
