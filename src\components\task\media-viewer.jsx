import { Card, CardContent } from '@/components/ui/card';
import { FileText } from 'lucide-react';
import Image from 'next/image';

export default function MediaViewer({ mediaItems }) {
	if (!mediaItems || mediaItems.length === 0) {
		return null;
	}

	const renderMediaItem = (item) => {
		const type = item.resource_type;
		// Use a placeholder if URL is missing, which can happen with new uploads before they are processed
		const url =
			item.url ||
			(item.file
				? URL.createObjectURL(item.file)
				: '/placeholder.svg?width=100&height=100&text=Media');
		const name = item.public_id || item.file?.name || 'attachment';

		if (
			type === 'image' ||
			(item.file && item.file.type.startsWith('image/'))
		) {
			return (
				<a
					href={url}
					target="_blank"
					rel="noopener noreferrer"
					className="block group"
				>
					<Image
						width={100}
						height={100}
						src={url || '/placeholder.svg'}
						alt={name}
						className="w-full h-32 object-cover rounded-md group-hover:opacity-80 transition-opacity"
					/>
					<p className="text-xs mt-1 truncate text-center text-muted-foreground group-hover:text-primary">
						{name}
					</p>
				</a>
			);
		}
		if (
			type === 'video' ||
			(item.file && item.file.type.startsWith('video/'))
		) {
			return (
				<div className="group">
					<video
						controls
						src={url}
						className="w-full h-32 object-cover rounded-md"
					/>
					<p className="text-xs mt-1 truncate text-center text-muted-foreground group-hover:text-primary">
						{name}
					</p>
				</div>
			);
		}
		// Fallback for documents or other file types
		return (
			<a
				href={url}
				target="_blank"
				rel="noopener noreferrer"
				className="block group p-2 border rounded-md hover:border-primary transition-colors"
			>
				<div className="flex flex-col items-center justify-center h-32">
					<FileText className="w-12 h-12 text-muted-foreground group-hover:text-primary transition-colors" />
					<p className="text-xs mt-2 truncate text-center text-muted-foreground group-hover:text-primary">
						{name}
					</p>
				</div>
			</a>
		);
	};

	return (
		<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
			{mediaItems.map((item, index) => (
				<Card
					key={item._id?.$oid || item.public_id || index}
					className="overflow-hidden"
				>
					<CardContent className="p-0">{renderMediaItem(item)}</CardContent>
				</Card>
			))}
		</div>
	);
}
