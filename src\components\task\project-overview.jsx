'use client';

import { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle2, Clock } from 'lucide-react';
import { isPast, isFuture, differenceInDays, parseISO } from 'date-fns';

const ProjectOverview = ({ tasks, projects }) => {
	const projectStats = useMemo(() => {
		if (!tasks || tasks.length === 0 || !projects || projects.length === 0) {
			return [];
		}

		const stats = projects.map((project) => {
			const projectTasks = tasks.filter(
				(task) => (task.projectId?.$oid || task.projectId) === project.id
			);
			if (projectTasks.length === 0) {
				return {
					...project,
					totalTasks: 0,
					completedTasks: 0,
					overdueTasks: 0,
					dueSoonTasks: 0,
					completionPercentage: 0,
					urgencyScore: 0, // Lower is less urgent
				};
			}

			const completedTasks = projectTasks.filter(
				(task) => task.status === 'completed'
			).length;
			const overdueTasks = projectTasks.filter((task) => {
				const dueDate = task.dueDate?.$date || task.dueDate;
				return (
					dueDate &&
					isPast(parseISO(dueDate)) &&
					task.status !== 'completed' &&
					task.status !== 'cancelled'
				);
			}).length;
			const dueSoonTasks = projectTasks.filter((task) => {
				const dueDate = task.dueDate?.$date || task.dueDate;
				return (
					dueDate &&
					isFuture(parseISO(dueDate)) &&
					differenceInDays(parseISO(dueDate), new Date()) <= 7 &&
					task.status !== 'completed' &&
					task.status !== 'cancelled'
				);
			}).length;

			const completionPercentage = (completedTasks / projectTasks.length) * 100;

			// Simple urgency score: overdue tasks have high weight, due soon have medium weight
			const urgencyScore = overdueTasks * 10 + dueSoonTasks * 5;

			return {
				...project,
				totalTasks: projectTasks.length,
				completedTasks,
				overdueTasks,
				dueSoonTasks,
				completionPercentage,
				urgencyScore,
			};
		});

		// Sort projects by urgency score (descending) then by name
		return stats.sort((a, b) => {
			if (b.urgencyScore !== a.urgencyScore) {
				return b.urgencyScore - a.urgencyScore;
			}
			return a.name.localeCompare(b.name);
		});
	}, [tasks, projects]);

	if (projectStats.length === 0) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<FolderKanban className="w-6 h-6 text-primary" />
						Project Overview
					</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-muted-foreground">
						No project data available to display.
					</p>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<FolderKanban className="w-6 h-6 text-primary" />
					Project Overview
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{projectStats.map((project) => (
						<Card key={project.id} className="flex flex-col">
							<CardHeader className="pb-2">
								<CardTitle className="text-lg">{project.name}</CardTitle>
								<p className="text-sm text-muted-foreground">
									{project.totalTasks} task(s)
								</p>
							</CardHeader>
							<CardContent className="flex-grow space-y-3">
								<div>
									<div className="flex justify-between items-center mb-1 text-sm">
										<span>Completion</span>
										<span className="font-semibold">
											{project.completionPercentage.toFixed(0)}%
										</span>
									</div>
									<Progress
										value={project.completionPercentage}
										aria-label={`${project.name} completion progress`}
									/>
								</div>
								<div className="flex flex-wrap gap-2 text-xs">
									<Badge variant="outline" className="flex items-center gap-1">
										<CheckCircle2 className="w-3 h-3 text-green-500" />
										{project.completedTasks} Completed
									</Badge>
									{project.overdueTasks > 0 && (
										<Badge
											variant="destructive"
											className="flex items-center gap-1"
										>
											<AlertTriangle className="w-3 h-3" />
											{project.overdueTasks} Overdue
										</Badge>
									)}
									{project.dueSoonTasks > 0 && (
										<Badge
											variant="secondary"
											className="flex items-center gap-1 border border-yellow-500 text-yellow-600"
										>
											<Clock className="w-3 h-3" />
											{project.dueSoonTasks} Due Soon
										</Badge>
									)}
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</CardContent>
		</Card>
	);
};

export default ProjectOverview;
