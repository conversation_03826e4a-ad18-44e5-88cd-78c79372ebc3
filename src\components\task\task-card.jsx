'use client';

import {
	<PERSON>,
	CardHeader,
	CardTitle,
	CardDescription,
	CardContent,
	CardFooter,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Paperclip,
	MessageSquare,
	CalendarDays,
	UserCircle,
	Tag,
} from 'lucide-react';
import { formatDate, getInitials } from '../../lib/utils';
import { mockUsers } from '../../lib/mock-data';

const priorityColors = {
	low: 'bg-green-500 hover:bg-green-600',
	medium: 'bg-yellow-500 hover:bg-yellow-600',
	high: 'bg-red-500 hover:bg-red-600',
};

const statusColors = {
	pending: 'bg-slate-500',
	'in-progress': 'bg-blue-500',
	completed: 'bg-green-700',
	cancelled: 'bg-gray-400',
	overdue: 'bg-orange-600',
};

export default function TaskCard({ task, onClick, viewMode }) {
	const assignedUser = mockUsers.find((u) => u.id === task.assignedTo);

	const cardStyle =
		task.color && task.color !== 'ffffff'
			? { borderLeft: `5px solid #${task.color}` }
			: {};

	if (viewMode === 'list') {
		return (
			<Card
				className="hover:shadow-lg transition-shadow cursor-pointer flex flex-col sm:flex-row justify-between items-start sm:items-center p-4"
				onClick={onClick}
				style={cardStyle}
			>
				<div className="flex-grow mb-2 sm:mb-0">
					<CardTitle className="text-lg font-semibold mb-1 capitalize">
						{task.name}
					</CardTitle>
					<CardDescription className="text-sm text-muted-foreground flex items-center gap-2">
						<Tag className="w-3 h-3" /> {task.code}
					</CardDescription>
				</div>
				<div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 text-sm w-full sm:w-auto">
					<div className="flex items-center gap-1 text-muted-foreground">
						<CalendarDays className="w-4 h-4" />
						<span>{formatDate(task.dueDate)}</span>
					</div>
					<Badge
						className={`${priorityColors[task.priority?.toLowerCase()] || priorityColors.medium} text-white`}
					>
						{task.priority || 'Medium'}
					</Badge>
					<Badge
						className={`${statusColors[task.status?.toLowerCase()]} text-white`}
					>
						{task.status || 'Pending'}
					</Badge>
					{assignedUser && (
						<div
							className="flex items-center gap-1"
							title={`Assigned to ${assignedUser.name}`}
						>
							<Avatar className="h-6 w-6">
								<AvatarImage
									src={assignedUser.avatar || '/placeholder.svg'}
									alt={assignedUser.name}
								/>
								<AvatarFallback>
									{getInitials(assignedUser.name)}
								</AvatarFallback>
							</Avatar>
							<span className="hidden sm:inline">{assignedUser.name}</span>
						</div>
					)}
				</div>
			</Card>
		);
	}

	// Grid View
	return (
		<Card
			className="hover:shadow-lg transition-shadow cursor-pointer flex flex-col h-full"
			onClick={onClick}
			style={cardStyle}
		>
			<CardHeader>
				<div className="flex justify-between items-start">
					<CardTitle className="text-lg font-semibold">{task.name}</CardTitle>
					<Badge
						className={`${priorityColors[task.priority?.toLowerCase()] || priorityColors.medium} text-white capitalize`}
					>
						{task.priority || 'Medium'}
					</Badge>
				</div>
				<CardDescription className="text-xs text-muted-foreground flex items-center gap-1 pt-1">
					<Tag className="w-3 h-3" /> {task.code}
				</CardDescription>
			</CardHeader>
			<CardContent className="flex-grow space-y-2 text-sm">
				{task.description && (
					<p className="text-muted-foreground line-clamp-2">
						{task.description}
					</p>
				)}
				<div className="flex items-center text-muted-foreground">
					<CalendarDays className="w-4 h-4 mr-2" />
					<span>Due: {formatDate(task.dueDate)}</span>
				</div>
				{assignedUser && (
					<div
						className="flex items-center text-muted-foreground"
						title={`Assigned to ${assignedUser.name}`}
					>
						<UserCircle className="w-4 h-4 mr-2" />
						<span>{assignedUser.name}</span>
					</div>
				)}
				<Badge
					className={`${statusColors[task.status?.toLowerCase()]} text-white capitalize`}
				>
					{task.status || 'Pending'}
				</Badge>
			</CardContent>
			<CardFooter className="text-xs text-muted-foreground flex justify-between items-center">
				<div className="flex items-center gap-2">
					{task.media && task.media.length > 0 && (
						<div
							className="flex items-center"
							title={`${task.media.length} attachments`}
						>
							<Paperclip className="w-3 h-3 mr-1" />
							<span>{task.media.length}</span>
						</div>
					)}
					{task.comments && task.comments.length > 0 && (
						<div
							className="flex items-center"
							title={`${task.comments.length} comments`}
						>
							<MessageSquare className="w-3 h-3 mr-1" />
							<span>{task.comments.length}</span>
						</div>
					)}
				</div>
				{assignedUser && (
					<Avatar className="h-6 w-6">
						<AvatarImage
							src={assignedUser.avatar || '/placeholder.svg'}
							alt={assignedUser.name}
						/>
						<AvatarFallback>{getInitials(assignedUser.name)}</AvatarFallback>
					</Avatar>
				)}
			</CardFooter>
		</Card>
	);
}
