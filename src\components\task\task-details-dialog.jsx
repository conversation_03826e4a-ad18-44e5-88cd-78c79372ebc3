'use client';
import { useState, useEffect } from 'react';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogDescription,
	DialogFooter,
	DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	CalendarDays,
	Tag,
	Paperclip,
	MessageSquare,
	Edit3,
	Trash2,
	CheckCircle,
	XCircle,
	Clock,
	AlertTriangle,
	Palette,
} from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { formatDate, getInitials, formatRelativeDate } from '../../lib/utils';
import MediaViewer from './media-viewer';
import EditTaskDialog from './edit-task-dialog'; // We'll create this

const priorityColors = {
	low: 'bg-green-500 hover:bg-green-600',
	medium: 'bg-yellow-500 hover:bg-yellow-600',
	high: 'bg-red-500 hover:bg-red-600',
};

const statusDetails = {
	pending: {
		icon: <Clock className="w-4 h-4 mr-2" />,
		color: 'bg-slate-500',
		label: 'Pending',
	},
	'in-progress': {
		icon: <Clock className="w-4 h-4 mr-2" />,
		color: 'bg-blue-500',
		label: 'In Progress',
	},
	completed: {
		icon: <CheckCircle className="w-4 h-4 mr-2" />,
		color: 'bg-green-700',
		label: 'Completed',
	},
	cancelled: {
		icon: <XCircle className="w-4 h-4 mr-2" />,
		color: 'bg-gray-400',
		label: 'Cancelled',
	},
	overdue: {
		icon: <AlertTriangle className="w-4 h-4 mr-2" />,
		color: 'bg-orange-600',
		label: 'Overdue',
	},
};

export default function TaskDetailsDialog({
	isOpen,
	onClose,
	task,
	projects,
	users,
	onTaskUpdated,
	onCommentAdded,
	onStatusChange,
	onDeleteTask,
}) {
	const [newComment, setNewComment] = useState('');
	const [isEditing, setIsEditing] = useState(false);
	const [currentTask, setCurrentTask] = useState(task);

	useEffect(() => {
		setCurrentTask(task);
	}, [task]);

	if (!currentTask) return null;

	const project = projects.find(
		(p) => p.id === (currentTask.projectId?.$oid || currentTask.projectId)
	);
	const assignedToUser = users.find(
		(u) => u.id === (currentTask.assignedTo?.$oid || currentTask.assignedTo)
	);
	const assignedByUser = users.find(
		(u) => u.id === (currentTask.assignedBy?.$oid || currentTask.assignedBy)
	);

	const handleAddComment = () => {
		if (newComment.trim() === '') return;
		const comment = {
			_id: { $oid: `comment-${Date.now()}` },
			comment: newComment,
			createdAt: { $date: new Date().toISOString() },
			userId: { $oid: 'currentUserMockId' }, // Mock current user ID
			user: users.find((u) => u.id === 'currentUserMockId') || {
				_id: { $oid: 'currentUserMockId' },
				name: 'Current User',
				profilePhoto: '/placeholder.svg?width=40&height=40&text=CU',
			},
		};
		onCommentAdded(currentTask._id.$oid, comment);
		setNewComment('');
	};

	const handleLocalStatusChange = (newStatus) => {
		onStatusChange(currentTask._id.$oid, newStatus);
		setCurrentTask((prev) => ({ ...prev, status: newStatus }));
	};

	const handleLocalTaskUpdate = (updatedData) => {
		const fullyUpdatedTask = { ...currentTask, ...updatedData };
		onTaskUpdated(fullyUpdatedTask); // Propagate to parent
		setCurrentTask(fullyUpdatedTask); // Update local state
		setIsEditing(false); // Close edit dialog
	};

	const handleDelete = () => {
		if (window.confirm('Are you sure you want to delete this task?')) {
			onDeleteTask(currentTask._id.$oid);
		}
	};

	const currentStatusDetail =
		statusDetails[currentTask.status?.toLowerCase()] || statusDetails.pending;

	return (
		<>
			<Dialog open={isOpen} onOpenChange={onClose}>
				<DialogContent className="sm:max-w-2xl md:max-w-3xl max-h-[90vh] flex flex-col">
					<DialogHeader>
						<DialogTitle className="text-2xl font-bold">
							{currentTask.name}
						</DialogTitle>
						<DialogDescription className="flex items-center gap-2">
							<Tag className="w-4 h-4" /> {currentTask.code}
							{project && (
								<span className="text-muted-foreground">
									in Project: {project.name}
								</span>
							)}
						</DialogDescription>
					</DialogHeader>

					<div className="flex-grow overflow-y-auto pr-2 space-y-6 py-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
							<div>
								<strong className="font-medium text-muted-foreground">
									Status:
								</strong>
								<Select
									value={currentTask.status}
									onValueChange={handleLocalStatusChange}
								>
									<SelectTrigger
										className={`w-full mt-1 ${currentStatusDetail.color} text-white`}
									>
										<SelectValue placeholder="Select status" />
									</SelectTrigger>
									<SelectContent>
										{Object.entries(statusDetails).map(([key, value]) => (
											<SelectItem
												key={key}
												value={key}
												className="capitalize flex items-center"
											>
												{value.icon} {value.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
							<div>
								<strong className="font-medium text-muted-foreground">
									Priority:
								</strong>
								<Badge
									className={`${priorityColors[currentTask.priority?.toLowerCase()] || priorityColors.medium} text-white capitalize block w-fit mt-1`}
								>
									{currentTask.priority || 'Medium'}
								</Badge>
							</div>
							<div>
								<strong className="font-medium text-muted-foreground">
									Due Date:
								</strong>
								<p className="flex items-center mt-1">
									<CalendarDays className="w-4 h-4 mr-2 text-primary" />{' '}
									{formatDate(
										currentTask.dueDate?.$date || currentTask.dueDate
									)}
								</p>
							</div>
							{assignedToUser && (
								<div>
									<strong className="font-medium text-muted-foreground">
										Assigned To:
									</strong>
									<div className="flex items-center gap-2 mt-1">
										<Avatar className="h-6 w-6">
											<AvatarImage
												src={assignedToUser.avatar || '/placeholder.svg'}
											/>
											<AvatarFallback>
												{getInitials(assignedToUser.name)}
											</AvatarFallback>
										</Avatar>
										<span>{assignedToUser.name}</span>
									</div>
								</div>
							)}
							{assignedByUser && (
								<div>
									<strong className="font-medium text-muted-foreground">
										Assigned By:
									</strong>
									<div className="flex items-center gap-2 mt-1">
										<Avatar className="h-6 w-6">
											<AvatarImage
												src={assignedByUser.avatar || '/placeholder.svg'}
											/>
											<AvatarFallback>
												{getInitials(assignedByUser.name)}
											</AvatarFallback>
										</Avatar>
										<span>{assignedByUser.name}</span>
									</div>
								</div>
							)}
							{currentTask.color && currentTask.color !== 'ffffff' && (
								<div>
									<strong className="font-medium text-muted-foreground">
										Color Tag:
									</strong>
									<div className="flex items-center gap-2 mt-1">
										<Palette
											className="w-4 h-4 mr-1"
											style={{ color: `#${currentTask.color}` }}
										/>
										<span style={{ color: `#${currentTask.color}` }}>
											#{currentTask.color}
										</span>
										<div
											className="w-4 h-4 rounded-full"
											style={{ backgroundColor: `#${currentTask.color}` }}
										></div>
									</div>
								</div>
							)}
						</div>

						{currentTask.description && (
							<div>
								<strong className="font-medium text-muted-foreground">
									Description:
								</strong>
								<p className="mt-1 whitespace-pre-wrap bg-muted p-3 rounded-md">
									{currentTask.description}
								</p>
							</div>
						)}

						{currentTask.media && currentTask.media.length > 0 && (
							<div>
								<strong className="font-medium text-muted-foreground flex items-center mb-2">
									<Paperclip className="w-4 h-4 mr-2 text-primary" />{' '}
									Attachments:
								</strong>
								<MediaViewer mediaItems={currentTask.media} />
							</div>
						)}

						<div>
							<strong className="font-medium text-muted-foreground flex items-center mb-2">
								<MessageSquare className="w-4 h-4 mr-2 text-primary" />{' '}
								Comments:
							</strong>
							<div className="space-y-3 max-h-60 overflow-y-auto bg-muted p-3 rounded-md">
								{currentTask.comments && currentTask.comments.length > 0 ? (
									currentTask.comments.map((comment) => (
										<div
											key={comment._id.$oid}
											className="flex items-start gap-2 text-sm p-2 bg-background rounded shadow-sm"
										>
											<Avatar className="h-8 w-8">
												<AvatarImage
													src={comment.user?.profilePhoto || '/placeholder.svg'}
												/>
												<AvatarFallback>
													{getInitials(comment.user?.name || 'U')}
												</AvatarFallback>
											</Avatar>
											<div>
												<p className="font-semibold">
													{comment.user?.name || 'Anonymous'}
												</p>
												<p className="text-muted-foreground whitespace-pre-wrap">
													{comment.comment}
												</p>
												<p className="text-xs text-muted-foreground/70 mt-1">
													{formatRelativeDate(comment.createdAt.$date)}
												</p>
											</div>
										</div>
									))
								) : (
									<p className="text-sm text-muted-foreground">
										No comments yet.
									</p>
								)}
							</div>
							<div className="mt-4">
								<Textarea
									placeholder="Add a comment..."
									value={newComment}
									onChange={(e) => setNewComment(e.target.value)}
									className="mb-2"
								/>
								<Button
									onClick={handleAddComment}
									size="sm"
									disabled={!newComment.trim()}
								>
									Add Comment
								</Button>
							</div>
						</div>

						<p className="text-xs text-muted-foreground">
							Created:{' '}
							{formatDate(
								currentTask.createdAt?.$date || currentTask.createdAt
							)}{' '}
							| Last updated:{' '}
							{formatRelativeDate(
								currentTask.updatedAt?.$date || currentTask.updatedAt
							)}
						</p>
					</div>

					<DialogFooter className="pt-4 border-t">
						<Button variant="outline" onClick={() => setIsEditing(true)}>
							<Edit3 className="w-4 h-4 mr-2" /> Edit Task
						</Button>
						<Button variant="destructive" onClick={handleDelete}>
							<Trash2 className="w-4 h-4 mr-2" /> Delete Task
						</Button>
						<DialogClose asChild>
							<Button variant="ghost">Close</Button>
						</DialogClose>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{isEditing && (
				<EditTaskDialog
					isOpen={isEditing}
					onClose={() => setIsEditing(false)}
					task={currentTask}
					projects={projects}
					users={users}
					onTaskUpdated={handleLocalTaskUpdate}
				/>
			)}
		</>
	);
}
