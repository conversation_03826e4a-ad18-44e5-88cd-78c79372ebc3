'use client';
import { useEffect, useRef, useState } from 'react';
import { Color, Scene, Fog, PerspectiveCamera, Vector3 } from 'three';
import dynamic from 'next/dynamic';
import { useThree, Canvas, extend } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import countries from '@/data/globe.json';
import { Loader2 } from 'lucide-react';

const RING_PROPAGATION_SPEED = 3;
const aspect = 1.2;
const cameraZ = 350;

let numbersOfRings = [0];

// Dynamically import ThreeGlobe to avoid SSR issues
const loadThreeGlobe = async () => {
	if (typeof window !== 'undefined') {
		const threeGlobeModule = await import('three-globe');
		extend({ ThreeGlobe: threeGlobeModule.default });
	}
};

export function Globe({ globeConfig, data }) {
	const [globeData, setGlobeData] = useState(null);
	const [isClient, setIsClient] = useState(false);
	const globeRef = useRef(null);

	useEffect(() => {
		setIsClient(true);
		loadThreeGlobe();
	}, []);

	const defaultProps = {
		pointSize: 1,
		atmosphereColor: '#ffffff',
		showAtmosphere: true,
		atmosphereAltitude: 0.1,
		polygonColor: 'rgba(255,255,255,0.7)',
		globeColor: '#1d072e',
		emissive: '#000000',
		emissiveIntensity: 0.1,
		shininess: 0.9,
		arcTime: 2000,
		arcLength: 0.9,
		rings: 1,
		maxRings: 3,
		...globeConfig,
	};

	useEffect(() => {
		if (globeRef.current) {
			_buildData();
			_buildMaterial();
		}
	}, []);

	const _buildMaterial = () => {
		if (!globeRef.current) return;
		const globeMaterial = globeRef.current.globeMaterial();
		globeMaterial.color = new Color(defaultProps.globeColor);
		globeMaterial.emissive = new Color(defaultProps.emissive);
		globeMaterial.emissiveIntensity = defaultProps.emissiveIntensity;
		globeMaterial.shininess = defaultProps.shininess;
	};

	const _buildData = () => {
		const arcs = data;
		let points = [];
		for (let arc of arcs) {
			const rgb = hexToRgb(arc.color);
			points.push({
				size: defaultProps.pointSize,
				order: arc.order,
				color: (t) => `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${1 - t})`,
				lat: arc.startLat,
				lng: arc.startLng,
			});
			points.push({
				size: defaultProps.pointSize,
				order: arc.order,
				color: (t) => `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${1 - t})`,
				lat: arc.endLat,
				lng: arc.endLng,
			});
		}

		// Remove duplicate points
		const filteredPoints = points.filter(
			(v, i, a) =>
				a.findIndex((v2) => ['lat', 'lng'].every((k) => v2[k] === v[k])) === i
		);

		setGlobeData(filteredPoints);
	};

	useEffect(() => {
		if (globeRef.current && globeData) {
			globeRef.current
				.hexPolygonsData(countries.features)
				.hexPolygonResolution(3)
				.hexPolygonMargin(0.7)
				.showAtmosphere(defaultProps.showAtmosphere)
				.atmosphereColor(defaultProps.atmosphereColor)
				.atmosphereAltitude(defaultProps.atmosphereAltitude)
				.hexPolygonColor(() => defaultProps.polygonColor);
			startAnimation();
		}
	}, [globeData]);

	const startAnimation = () => {
		if (!globeRef.current || !globeData) return;

		globeRef.current
			.arcsData(data)
			.arcStartLat((d) => d.startLat * 1)
			.arcStartLng((d) => d.startLng * 1)
			.arcEndLat((d) => d.endLat * 1)
			.arcEndLng((d) => d.endLng * 1)
			.arcColor((e) => e.color)
			.arcAltitude((e) => e.arcAlt * 1)
			.arcStroke(() => [0.32, 0.28, 0.3][Math.round(Math.random() * 2)])
			.arcDashLength(defaultProps.arcLength)
			.arcDashInitialGap((e) => e.order * 1)
			.arcDashGap(15)
			.arcDashAnimateTime(defaultProps.arcTime);

		globeRef.current
			.pointsData(data)
			.pointColor((e) => e.color)
			.pointsMerge(true)
			.pointAltitude(0.0)
			.pointRadius(2);
	};

	useEffect(() => {
		if (!globeRef.current || !globeData) return;

		const interval = setInterval(() => {
			numbersOfRings = genRandomNumbers(
				0,
				data.length,
				Math.floor((data.length * 4) / 5)
			);
			globeRef.current.ringsData(
				globeData.filter((_, i) => numbersOfRings.includes(i))
			);
		}, 2000);

		return () => clearInterval(interval);
	}, [globeData]);

	if (!isClient) {
		return (
			<div className="h-screen flex justify-center items-center">
				<Loader2 className="animate-spin" />
			</div>
		);
	}

	return <threeGlobe ref={globeRef} />;
}

export function WebGLRendererConfig() {
	const { gl, size } = useThree();

	useEffect(() => {
		if (typeof window !== 'undefined') {
			gl.setPixelRatio(window.devicePixelRatio);
			gl.setSize(size.width, size.height);
			gl.setClearColor(0xffaaff, 0);
		}
	}, []);

	return null;
}

export function World(props) {
	const { globeConfig } = props;
	const scene = new Scene();
	scene.fog = new Fog(0xffffff, 400, 2000);

	return (
		<Canvas scene={scene} camera={new PerspectiveCamera(50, aspect, 180, 1800)}>
			<WebGLRendererConfig />
			<ambientLight color={globeConfig.ambientLight} intensity={0.6} />
			<directionalLight
				color={globeConfig.directionalLeftLight}
				position={new Vector3(-400, 100, 400)}
			/>
			<directionalLight
				color={globeConfig.directionalTopLight}
				position={new Vector3(-200, 500, 200)}
			/>
			<pointLight
				color={globeConfig.pointLight}
				position={new Vector3(-200, 500, 200)}
				intensity={0.8}
			/>
			<Globe {...props} />
			<OrbitControls
				enablePan={false}
				enableZoom={false}
				minDistance={cameraZ}
				maxDistance={cameraZ}
				autoRotate
				autoRotateSpeed={1}
				minPolarAngle={Math.PI / 3.5}
				maxPolarAngle={Math.PI - Math.PI / 3}
			/>
		</Canvas>
	);
}

export function hexToRgb(hex) {
	let result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
	return result
		? {
				r: parseInt(result[1], 16),
				g: parseInt(result[2], 16),
				b: parseInt(result[3], 16),
			}
		: null;
}

export function genRandomNumbers(min, max, count) {
	const arr = new Set();
	while (arr.size < count)
		arr.add(Math.floor(Math.random() * (max - min)) + min);
	return [...arr];
}
