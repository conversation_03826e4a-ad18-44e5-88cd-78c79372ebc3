'use client';

import { useState, useRef } from 'react';
import { Camera, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Image from 'next/image';

export default function ProfilePhotoUpload({
	onImageChange,
	size = 'md',
	initialImage = null,
	description,
}) {
	const [photoPreview, setPhotoPreview] = useState(initialImage);
	const fileInputRef = useRef(null);

	// Size mapping
	const sizeMap = {
		sm: 'w-16 h-16',
		md: 'w-24 h-24',
		lg: 'w-32 h-32',
	};

	const handleFileChange = (e) => {
		if (e.target.files && e.target.files[0]) {
			const file = e.target.files[0];

			// Call the callback if provided
			if (onImageChange) {
				onImageChange(file);
			}

			// Create preview
			const reader = new FileReader();
			reader.onload = (event) => {
				setPhotoPreview(event.target?.result);
			};
			reader.readAsDataURL(file);
		}
	};

	const handleRemoveImage = () => {
		setPhotoPreview(null);
		if (fileInputRef.current) {
			fileInputRef.current.value = '';
		}
		if (onImageChange) {
			onImageChange(null);
		}
	};

	return (
		<div className="flex flex-col items-center gap-3">
			<div className="relative">
				<div
					className={`${sizeMap[size]} rounded-full border-2 border-dashed border-primary/50 flex items-center justify-center cursor-pointer overflow-hidden hover:border-primary transition-colors`}
					onClick={() => fileInputRef.current?.click()}
				>
					{photoPreview ? (
						<Image
							src={photoPreview || '/placeholder.svg'}
							width={sizeMap[size].split(' ')[0].replace('w-', '')}
							height={sizeMap[size].split(' ')[1].replace('h-', '')}
							alt="Profile Preview"
							className="w-full h-full object-cover"
						/>
					) : (
						<div className="text-muted-foreground flex flex-col items-center justify-center">
							<Camera className="h-6 w-6 mb-1" />
							<span className="text-xs text-center">Upload</span>
						</div>
					)}
				</div>

				{photoPreview && (
					<Button
						variant="secondary"
						size="icon"
						className="absolute -top-2 -right-2 h-6 w-6 rounded-full"
						onClick={handleRemoveImage}
					>
						<X className="h-3 w-3" />
					</Button>
				)}
			</div>

			<input
				type="file"
				accept="image/*"
				ref={fileInputRef}
				className="hidden"
				onChange={handleFileChange}
			/>
		</div>
	);
}
