import { Construction, Clock } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function WorkInProgress({
	title = 'This Module',
	message = "We're working hard to bring you this feature.",
}) {
	return (
		<div className="flex items-center justify-center min-h-[400px] p-8">
			<Card className="w-full max-w-md text-center border-dashed border-2 border-gray-300">
				<CardContent className="p-8">
					<div className="mb-4">
						<Construction className="w-16 h-16 text-gray-400 mx-auto mb-4" />
						<Badge variant="secondary" className="mb-4">
							<Clock className="w-3 h-3 mr-1" />
							Work in Progress
						</Badge>
					</div>

					<h3 className="text-xl font-semibold text-gray-700 dark:text-gray-100 dark:text-white mb-2">
						{title} is Coming Soon
					</h3>

					<p className="text-gray-500 dark:text-white text-sm">{message}</p>
				</CardContent>
			</Card>
		</div>
	);
}
