export const currencyData = [
	{
		country: 'Afghanistan',
		currency_code: 'AFN',
		currency_symbol: '؋',
		major_cities: ['Kabul', 'Kandahar', 'Herat'],
		initials: 'AF',
	},
	{
		country: 'Albania',
		currency_code: 'ALL',
		currency_symbol: 'L',
		major_cities: ['Tirana', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
		initials: 'AL',
	},
	{
		country: 'Algeria',
		currency_code: 'DZD',
		currency_symbol: 'د.ج',
		major_cities: ['Algiers', 'Oran', 'Constantine'],
		initials: 'DZ',
	},
	{
		country: 'Andorra',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Andorra la Vella', 'Escaldes-Engordany', 'Encamp'],
		initials: 'AD',
	},
	{
		country: 'Angola',
		currency_code: 'AOA',
		currency_symbol: 'Kz',
		major_cities: ['Luanda', 'Huambo', 'Lobito'],
		initials: 'AO',
	},
	{
		country: 'Antigua and Barbuda',
		currency_code: 'XCD',
		currency_symbol: '$',
		major_cities: ["Saint John's", 'All Saints', 'Liberta'],
		initials: 'AG',
	},
	{
		country: 'Argentina',
		currency_code: 'ARS',
		currency_symbol: '$',
		major_cities: ['Buenos Aires', 'Córdoba', 'Rosario'],
		initials: 'AR',
	},
	{
		country: 'Armenia',
		currency_code: 'AMD',
		currency_symbol: '֏',
		major_cities: ['Yerevan', 'Gyumri', 'Vanadzor'],
		initials: 'AM',
	},
	{
		country: 'Australia',
		currency_code: 'AUD',
		currency_symbol: '$',
		major_cities: ['Sydney', 'Melbourne', 'Brisbane'],
		initials: 'AU',
	},
	{
		country: 'Austria',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Vienna', 'Graz', 'Linz'],
		initials: 'AT',
	},
	{
		country: 'Azerbaijan',
		currency_code: 'AZN',
		currency_symbol: '₼',
		major_cities: ['Baku', 'Ganja', 'Sumqayit'],
		initials: 'AZ',
	},
	{
		country: 'Bahamas',
		currency_code: 'BSD',
		currency_symbol: '$',
		major_cities: ['Nassau', 'Freeport', 'West End'],
		initials: 'BS',
	},
	{
		country: 'Bahrain',
		currency_code: 'BHD',
		currency_symbol: '.د.ب',
		major_cities: ['Manama', 'Riffa', 'Muharraq'],
		initials: 'BH',
	},
	{
		country: 'Bangladesh',
		currency_code: 'BDT',
		currency_symbol: '৳',
		major_cities: ['Dhaka', 'Chittagong', 'Khulna'],
		initials: 'BD',
	},
	{
		country: 'Barbados',
		currency_code: 'BBD',
		currency_symbol: '$',
		major_cities: ['Bridgetown', 'Speightstown', 'Oistins'],
		initials: 'BB',
	},
	{
		country: 'Belarus',
		currency_code: 'BYN',
		currency_symbol: 'Br',
		major_cities: ['Minsk', 'Gomel', 'Mogilev'],
		initials: 'BY',
	},
	{
		country: 'Belgium',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Brussels', 'Antwerp', 'Ghent'],
		initials: 'BE',
	},
	{
		country: 'Belize',
		currency_code: 'BZD',
		currency_symbol: '$',
		major_cities: ['Belize City', 'Belmopan', 'San Ignacio'],
		initials: 'BZ',
	},
	{
		country: 'Benin',
		currency_code: 'XOF',
		currency_symbol: 'CFA',
		major_cities: ['Porto-Novo', 'Cotonou', 'Parakou'],
		initials: 'BJ',
	},
	{
		country: 'Bhutan',
		currency_code: 'BTN',
		currency_symbol: 'Nu.',
		major_cities: ['Thimphu', 'Phuntsholing', 'Paro'],
		initials: 'BT',
	},
	{
		country: 'Bolivia',
		currency_code: 'BOB',
		currency_symbol: 'Bs.',
		major_cities: ['La Paz', 'Santa Cruz de la Sierra', 'Cochabamba'],
		initials: 'BO',
	},
	{
		country: 'Bosnia and Herzegovina',
		currency_code: 'BAM',
		currency_symbol: 'KM',
		major_cities: ['Sarajevo', 'Banja Luka', 'Tuzla'],
		initials: 'BA',
	},
	{
		country: 'Botswana',
		currency_code: 'BWP',
		currency_symbol: 'P',
		major_cities: ['Gaborone', 'Francistown', 'Molepolole'],
		initials: 'BW',
	},
	{
		country: 'Brazil',
		currency_code: 'BRL',
		currency_symbol: 'R$',
		major_cities: ['São Paulo', 'Rio de Janeiro', 'Salvador'],
		initials: 'BR',
	},
	{
		country: 'Brunei',
		currency_code: 'BND',
		currency_symbol: '$',
		major_cities: ['Bandar Seri Begawan', 'Kuala Belait', 'Seria'],
		initials: 'BN',
	},
	{
		country: 'Bulgaria',
		currency_code: 'BGN',
		currency_symbol: 'лв',
		major_cities: ['Sofia', 'Plovdiv', 'Varna'],
		initials: 'BG',
	},
	{
		country: 'Burkina Faso',
		currency_code: 'XOF',
		currency_symbol: 'CFA',
		major_cities: ['Ouagadougou', 'Bobo-Dioulasso', 'Koudougou'],
		initials: 'BF',
	},
	{
		country: 'Burundi',
		currency_code: 'BIF',
		currency_symbol: 'FBu',
		major_cities: ['Bujumbura', 'Gitega', 'Muyinga'],
		initials: 'BI',
	},
	{
		country: 'Cambodia',
		currency_code: 'KHR',
		currency_symbol: '៛',
		major_cities: ['Phnom Penh', 'Siem Reap', 'Battambang'],
		initials: 'KH',
	},
	{
		country: 'Cameroon',
		currency_code: 'XAF',
		currency_symbol: 'FCFA',
		major_cities: ['Yaoundé', 'Douala', 'Garoua'],
		initials: 'CM',
	},
	{
		country: 'Canada',
		currency_code: 'CAD',
		currency_symbol: '$',
		major_cities: ['Toronto', 'Montreal', 'Vancouver'],
		initials: 'CA',
	},
	{
		country: 'Cape Verde',
		currency_code: 'CVE',
		currency_symbol: '$',
		major_cities: ['Praia', 'Mindelo', 'Santa Maria'],
		initials: 'CV',
	},
	{
		country: 'Central African Republic',
		currency_code: 'XAF',
		currency_symbol: 'FCFA',
		major_cities: ['Bangui', 'Bimbo', 'Berbérati'],
		initials: 'CF',
	},
	{
		country: 'Chad',
		currency_code: 'XAF',
		currency_symbol: 'FCFA',
		major_cities: ["N'Djamena", 'Moundou', 'Sarh'],
		initials: 'TD',
	},
	{
		country: 'Chile',
		currency_code: 'CLP',
		currency_symbol: '$',
		major_cities: ['Santiago', 'Valparaíso', 'Concepción'],
		initials: 'CL',
	},
	{
		country: 'China',
		currency_code: 'CNY',
		currency_symbol: '¥',
		major_cities: ['Shanghai', 'Beijing', 'Guangzhou'],
		initials: 'CN',
	},
	{
		country: 'Colombia',
		currency_code: 'COP',
		currency_symbol: '$',
		major_cities: ['Bogotá', 'Medellín', 'Cali'],
		initials: 'CO',
	},
	{
		country: 'Comoros',
		currency_code: 'KMF',
		currency_symbol: 'CF',
		major_cities: ['Moroni', 'Mutsamudu', 'Fomboni'],
		initials: 'KM',
	},
	{
		country: 'Congo',
		currency_code: 'XAF',
		currency_symbol: 'FCFA',
		major_cities: ['Brazzaville', 'Pointe-Noire', 'Dolisie'],
		initials: 'CG',
	},
	{
		country: 'Costa Rica',
		currency_code: 'CRC',
		currency_symbol: '₡',
		major_cities: ['San José', 'Limón', 'Alajuela'],
		initials: 'CR',
	},
	{
		country: 'Croatia',
		currency_code: 'HRK',
		currency_symbol: 'kn',
		major_cities: ['Zagreb', 'Split', 'Rijeka'],
		initials: 'HR',
	},
	{
		country: 'Cuba',
		currency_code: 'CUP',
		currency_symbol: '$',
		major_cities: ['Havana', 'Santiago de Cuba', 'Camagüey'],
		initials: 'CU',
	},
	{
		country: 'Cyprus',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Nicosia', 'Limassol', 'Larnaca'],
		initials: 'CY',
	},
	{
		country: 'Czech Republic',
		currency_code: 'CZK',
		currency_symbol: 'Kč',
		major_cities: ['Prague', 'Brno', 'Ostrava'],
		initials: 'CZ',
	},
	{
		country: 'Denmark',
		currency_code: 'DKK',
		currency_symbol: 'kr',
		major_cities: ['Copenhagen', 'Aarhus', 'Odense'],
		initials: 'DK',
	},
	{
		country: 'Djibouti',
		currency_code: 'DJF',
		currency_symbol: 'Fdj',
		major_cities: ['Djibouti City', 'Ali Sabieh', 'Tadjoura'],
		initials: 'DJ',
	},
	{
		country: 'Dominica',
		currency_code: 'XCD',
		currency_symbol: '$',
		major_cities: ['Roseau', 'Portsmouth', 'Marigot'],
		initials: 'DM',
	},
	{
		country: 'Dominican Republic',
		currency_code: 'DOP',
		currency_symbol: 'RD$',
		major_cities: [
			'Santo Domingo',
			'Santiago de los Caballeros',
			'San Pedro de Macorís',
		],
		initials: 'DO',
	},
	{
		country: 'East Timor',
		currency_code: 'USD',
		currency_symbol: '$',
		major_cities: ['Dili', 'Baucau', 'Maliana'],
		initials: 'TL',
	},
	{
		country: 'Ecuador',
		currency_code: 'USD',
		currency_symbol: '$',
		major_cities: ['Quito', 'Guayaquil', 'Cuenca'],
		initials: 'EC',
	},
	{
		country: 'Egypt',
		currency_code: 'EGP',
		currency_symbol: '£',
		major_cities: ['Cairo', 'Alexandria', 'Giza'],
		initials: 'EG',
	},
	{
		country: 'El Salvador',
		currency_code: 'USD',
		currency_symbol: '$',
		major_cities: ['San Salvador', 'Santa Ana', 'San Miguel'],
		initials: 'SV',
	},
	{
		country: 'Equatorial Guinea',
		currency_code: 'XAF',
		currency_symbol: 'FCFA',
		major_cities: ['Malabo', 'Bata', 'Ebebiyin'],
		initials: 'GQ',
	},
	{
		country: 'Eritrea',
		currency_code: 'ERN',
		currency_symbol: 'Nfk',
		major_cities: ['Asmara', 'Keren', 'Massawa'],
		initials: 'ER',
	},
	{
		country: 'Estonia',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Tallinn', 'Tartu', 'Narva'],
		initials: 'EE',
	},
	{
		country: 'Eswatini',
		currency_code: 'SZL',
		currency_symbol: 'E',
		major_cities: ['Mbabane', 'Manzini', 'Lobamba'],
		initials: 'SZ',
	},
	{
		country: 'Ethiopia',
		currency_code: 'ETB',
		currency_symbol: 'Br',
		major_cities: ['Addis Ababa', 'Dire Dawa', "Mek'ele"],
		initials: 'ET',
	},
	{
		country: 'Fiji',
		currency_code: 'FJD',
		currency_symbol: '$',
		major_cities: ['Suva', 'Lautoka', 'Nadi'],
		initials: 'FJ',
	},
	{
		country: 'Finland',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Helsinki', 'Espoo', 'Tampere'],
		initials: 'FI',
	},
	{
		country: 'France',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Paris', 'Marseille', 'Lyon'],
		initials: 'FR',
	},
	{
		country: 'Gabon',
		currency_code: 'XAF',
		currency_symbol: 'FCFA',
		major_cities: ['Libreville', 'Port-Gentil', 'Franceville'],
		initials: 'GA',
	},
	{
		country: 'Gambia',
		currency_code: 'GMD',
		currency_symbol: 'D',
		major_cities: ['Banjul', 'Serekunda', 'Brikama'],
		initials: 'GM',
	},
	{
		country: 'Georgia',
		currency_code: 'GEL',
		currency_symbol: '₾',
		major_cities: ['Tbilisi', 'Batumi', 'Kutaisi'],
		initials: 'GE',
	},
	{
		country: 'Germany',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Berlin', 'Hamburg', 'Munich'],
		initials: 'DE',
	},
	{
		country: 'Ghana',
		currency_code: 'GHS',
		currency_symbol: '₵',
		major_cities: ['Accra', 'Kumasi', 'Tamale'],
		initials: 'GH',
	},
	{
		country: 'Greece',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Athens', 'Thessaloniki', 'Patras'],
		initials: 'GR',
	},
	{
		country: 'Grenada',
		currency_code: 'XCD',
		currency_symbol: '$',
		major_cities: ["St. George's", 'Grenville', 'Gouyave'],
		initials: 'GD',
	},
	{
		country: 'Guatemala',
		currency_code: 'GTQ',
		currency_symbol: 'Q',
		major_cities: ['Guatemala City', 'Mixco', 'Villa Nueva'],
		initials: 'GT',
	},
	{
		country: 'Guinea',
		currency_code: 'GNF',
		currency_symbol: 'FG',
		major_cities: ['Conakry', 'Nzérékoré', 'Kankan'],
		initials: 'GN',
	},
	{
		country: 'Guinea-Bissau',
		currency_code: 'XOF',
		currency_symbol: 'CFA',
		major_cities: ['Bissau', 'Bafatá', 'Gabú'],
		initials: 'GW',
	},
	{
		country: 'Guyana',
		currency_code: 'GYD',
		currency_symbol: '$',
		major_cities: ['Georgetown', 'Linden', 'New Amsterdam'],
		initials: 'GY',
	},
	{
		country: 'Haiti',
		currency_code: 'HTG',
		currency_symbol: 'G',
		major_cities: ['Port-au-Prince', 'Cap-Haïtien', 'Gonaïves'],
		initials: 'HT',
	},
	{
		country: 'Honduras',
		currency_code: 'HNL',
		currency_symbol: 'L',
		major_cities: ['Tegucigalpa', 'San Pedro Sula', 'Choloma'],
		initials: 'HN',
	},
	{
		country: 'Hungary',
		currency_code: 'HUF',
		currency_symbol: 'Ft',
		major_cities: ['Budapest', 'Debrecen', 'Szeged'],
		initials: 'HU',
	},
	{
		country: 'Iceland',
		currency_code: 'ISK',
		currency_symbol: 'kr',
		major_cities: ['Reykjavík', 'Kópavogur', 'Hafnarfjörður'],
		initials: 'IS',
	},
	{
		country: 'India',
		currency_code: 'INR',
		currency_symbol: '₹',
		major_cities: ['Mumbai', 'Delhi', 'Bangalore'],
		initials: 'IN',
	},
	{
		country: 'Indonesia',
		currency_code: 'IDR',
		currency_symbol: 'Rp',
		major_cities: ['Jakarta', 'Surabaya', 'Bandung'],
		initials: 'ID',
	},
	{
		country: 'Iran',
		currency_code: 'IRR',
		currency_symbol: '﷼',
		major_cities: ['Tehran', 'Mashhad', 'Isfahan'],
		initials: 'IR',
	},
	{
		country: 'Iraq',
		currency_code: 'IQD',
		currency_symbol: 'ع.د',
		major_cities: ['Baghdad', 'Basra', 'Mosul'],
		initials: 'IQ',
	},
	{
		country: 'Ireland',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Dublin', 'Cork', 'Limerick'],
		initials: 'IE',
	},
	{
		country: 'Israel',
		currency_code: 'ILS',
		currency_symbol: '₪',
		major_cities: ['Jerusalem', 'Tel Aviv', 'Haifa'],
		initials: 'IL',
	},
	{
		country: 'Italy',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Rome', 'Milan', 'Naples'],
		initials: 'IT',
	},
	{
		country: 'Jamaica',
		currency_code: 'JMD',
		currency_symbol: '$',
		major_cities: ['Kingston', 'Montego Bay', 'Spanish Town'],
		initials: 'JM',
	},
	{
		country: 'Japan',
		currency_code: 'JPY',
		currency_symbol: '¥',
		major_cities: ['Tokyo', 'Yokohama', 'Osaka'],
		initials: 'JP',
	},
	{
		country: 'Jordan',
		currency_code: 'JOD',
		currency_symbol: 'د.ا',
		major_cities: ['Amman', 'Zarqa', 'Irbid'],
		initials: 'JO',
	},
	{
		country: 'Kazakhstan',
		currency_code: 'KZT',
		currency_symbol: '₸',
		major_cities: ['Almaty', 'Nur-Sultan', 'Shymkent'],
		initials: 'KZ',
	},
	{
		country: 'Kenya',
		currency_code: 'KES',
		currency_symbol: 'Ksh',
		major_cities: ['Nairobi', 'Mombasa', 'Kisumu'],
		initials: 'KE',
	},
	{
		country: 'Kiribati',
		currency_code: 'AUD',
		currency_symbol: '$',
		major_cities: ['South Tarawa', 'Betio', 'Bikenibeu'],
		initials: 'KI',
	},
	{
		country: 'Kuwait',
		currency_code: 'KWD',
		currency_symbol: 'د.ك',
		major_cities: ['Kuwait City', 'Al Ahmadi', 'Hawalli'],
		initials: 'KW',
	},
	{
		country: 'Kyrgyzstan',
		currency_code: 'KGS',
		currency_symbol: 'с',
		major_cities: ['Bishkek', 'Osh', 'Jalal-Abad'],
		initials: 'KG',
	},
	{
		country: 'Laos',
		currency_code: 'LAK',
		currency_symbol: '₭',
		major_cities: ['Vientiane', 'Pakse', 'Luang Prabang'],
		initials: 'LA',
	},
	{
		country: 'Latvia',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Riga', 'Daugavpils', 'Liepāja'],
		initials: 'LV',
	},
	{
		country: 'Lebanon',
		currency_code: 'LBP',
		currency_symbol: 'ل.ل',
		major_cities: ['Beirut', 'Tripoli', 'Sidon'],
		initials: 'LB',
	},
	{
		country: 'Lesotho',
		currency_code: 'LSL',
		currency_symbol: 'L',
		major_cities: ['Maseru', 'Teyateyaneng', 'Mafeteng'],
		initials: 'LS',
	},
	{
		country: 'Liberia',
		currency_code: 'LRD',
		currency_symbol: '$',
		major_cities: ['Monrovia', 'Gbarnga', 'Buchanan'],
		initials: 'LR',
	},
	{
		country: 'Libya',
		currency_code: 'LYD',
		currency_symbol: 'ل.د',
		major_cities: ['Tripoli', 'Benghazi', 'Misrata'],
		initials: 'LY',
	},
	{
		country: 'Liechtenstein',
		currency_code: 'CHF',
		currency_symbol: 'Fr',
		major_cities: ['Vaduz', 'Schaan', 'Triesen'],
		initials: 'LI',
	},
	{
		country: 'Lithuania',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Vilnius', 'Kaunas', 'Klaipėda'],
		initials: 'LT',
	},
	{
		country: 'Luxembourg',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Luxembourg City', 'Esch-sur-Alzette', 'Dudelange'],
		initials: 'LU',
	},
	{
		country: 'Madagascar',
		currency_code: 'MGA',
		currency_symbol: 'Ar',
		major_cities: ['Antananarivo', 'Toamasina', 'Antsirabe'],
		initials: 'MG',
	},
	{
		country: 'Malawi',
		currency_code: 'MWK',
		currency_symbol: 'MK',
		major_cities: ['Lilongwe', 'Blantyre', 'Mzuzu'],
		initials: 'MW',
	},
	{
		country: 'Malaysia',
		currency_code: 'MYR',
		currency_symbol: 'RM',
		major_cities: ['Kuala Lumpur', 'George Town', 'Johor Bahru'],
		initials: 'MY',
	},
	{
		country: 'Maldives',
		currency_code: 'MVR',
		currency_symbol: 'Rf',
		major_cities: ['Malé', 'Addu City', 'Fuvahmulah'],
		initials: 'MV',
	},
	{
		country: 'Mali',
		currency_code: 'XOF',
		currency_symbol: 'CFA',
		major_cities: ['Bamako', 'Sikasso', 'Mopti'],
		initials: 'ML',
	},
	{
		country: 'Malta',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Valletta', 'Birkirkara', 'Mosta'],
		initials: 'MT',
	},
	{
		country: 'Marshall Islands',
		currency_code: 'USD',
		currency_symbol: '$',
		major_cities: ['Majuro', 'Ebeye', 'Arno'],
		initials: 'MH',
	},
	{
		country: 'Mauritania',
		currency_code: 'MRU',
		currency_symbol: 'UM',
		major_cities: ['Nouakchott', 'Nouadhibou', 'Kiffa'],
		initials: 'MR',
	},
	{
		country: 'Mauritius',
		currency_code: 'MUR',
		currency_symbol: '₨',
		major_cities: ['Port Louis', 'Beau Bassin-Rose Hill', 'Vacoas-Phoenix'],
		initials: 'MU',
	},
	{
		country: 'Mexico',
		currency_code: 'MXN',
		currency_symbol: '$',
		major_cities: ['Mexico City', 'Guadalajara', 'Monterrey'],
		initials: 'MX',
	},
	{
		country: 'Micronesia',
		currency_code: 'USD',
		currency_symbol: '$',
		major_cities: ['Palikir', 'Colonia', 'Weno'],
		initials: 'FM',
	},
	{
		country: 'Moldova',
		currency_code: 'MDL',
		currency_symbol: 'L',
		major_cities: ['Chișinău', 'Bălți', 'Comrat'],
		initials: 'MD',
	},
	{
		country: 'Monaco',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Monaco', 'Monte Carlo', 'La Condamine'],
		initials: 'MC',
	},
	{
		country: 'Mongolia',
		currency_code: 'MNT',
		currency_symbol: '₮',
		major_cities: ['Ulaanbaatar', 'Erdenet', 'Darkhan'],
		initials: 'MN',
	},
	{
		country: 'Montenegro',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Podgorica', 'Nikšić', 'Herceg Novi'],
		initials: 'ME',
	},
	{
		country: 'Morocco',
		currency_code: 'MAD',
		currency_symbol: 'د.م.',
		major_cities: ['Casablanca', 'Rabat', 'Fez'],
		initials: 'MA',
	},
	{
		country: 'Mozambique',
		currency_code: 'MZN',
		currency_symbol: 'MT',
		major_cities: ['Maputo', 'Matola', 'Beira'],
		initials: 'MZ',
	},
	{
		country: 'Myanmar',
		currency_code: 'MMK',
		currency_symbol: 'Ks',
		major_cities: ['Yangon', 'Mandalay', 'Naypyidaw'],
		initials: 'MM',
	},
	{
		country: 'Namibia',
		currency_code: 'NAD',
		currency_symbol: '$',
		major_cities: ['Windhoek', 'Swakopmund', 'Walvis Bay'],
		initials: 'NA',
	},
	{
		country: 'Nauru',
		currency_code: 'AUD',
		currency_symbol: '$',
		major_cities: ['Yaren'],
		initials: 'NR',
	},
	{
		country: 'Nepal',
		currency_code: 'NPR',
		currency_symbol: '₨',
		major_cities: ['Kathmandu', 'Pokhara', 'Lalitpur'],
		initials: 'NP',
	},
	{
		country: 'Netherlands',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Amsterdam', 'Rotterdam', 'The Hague'],
		initials: 'NL',
	},
	{
		country: 'New Zealand',
		currency_code: 'NZD',
		currency_symbol: '$',
		major_cities: ['Auckland', 'Wellington', 'Christchurch'],
		initials: 'NZ',
	},
	{
		country: 'Nicaragua',
		currency_code: 'NIO',
		currency_symbol: 'C$',
		major_cities: ['Managua', 'León', 'Masaya'],
		initials: 'NI',
	},
	{
		country: 'Niger',
		currency_code: 'XOF',
		currency_symbol: 'CFA',
		major_cities: ['Niamey', 'Zinder', 'Maradi'],
		initials: 'NE',
	},
	{
		country: 'Nigeria',
		currency_code: 'NGN',
		currency_symbol: '₦',
		major_cities: ['Lagos', 'Kano', 'Ibadan'],
		initials: 'NG',
	},
	{
		country: 'North Korea',
		currency_code: 'KPW',
		currency_symbol: '₩',
		major_cities: ['Pyongyang', 'Hamhung', 'Chongjin'],
		initials: 'KP',
	},
	{
		country: 'North Macedonia',
		currency_code: 'MKD',
		currency_symbol: 'ден',
		major_cities: ['Skopje', 'Bitola', 'Kumanovo'],
		initials: 'MK',
	},
	{
		country: 'Norway',
		currency_code: 'NOK',
		currency_symbol: 'kr',
		major_cities: ['Oslo', 'Bergen', 'Trondheim'],
		initials: 'NO',
	},
	{
		country: 'Oman',
		currency_code: 'OMR',
		currency_symbol: 'ر.ع.',
		major_cities: ['Muscat', 'Seeb', 'Salalah'],
		initials: 'OM',
	},
	{
		country: 'Pakistan',
		currency_code: 'PKR',
		currency_symbol: '₨',
		major_cities: ['Karachi', 'Lahore', 'Islamabad'],
		initials: 'PK',
	},
	{
		country: 'Palau',
		currency_code: 'USD',
		currency_symbol: '$',
		major_cities: ['Ngerulmud', 'Koror', 'Melekeok'],
		initials: 'PW',
	},
	{
		country: 'Palestine',
		currency_code: 'ILS',
		currency_symbol: '₪',
		major_cities: ['East Jerusalem', 'Gaza', 'Hebron'],
		initials: 'PS',
	},
	{
		country: 'Panama',
		currency_code: 'PAB',
		currency_symbol: 'B/.',
		major_cities: ['Panama City', 'San Miguelito', 'David'],
		initials: 'PA',
	},
	{
		country: 'Papua New Guinea',
		currency_code: 'PGK',
		currency_symbol: 'K',
		major_cities: ['Port Moresby', 'Lae', 'Mount Hagen'],
		initials: 'PG',
	},
	{
		country: 'Paraguay',
		currency_code: 'PYG',
		currency_symbol: '₲',
		major_cities: ['Asunción', 'Ciudad del Este', 'San Lorenzo'],
		initials: 'PY',
	},
	{
		country: 'Peru',
		currency_code: 'PEN',
		currency_symbol: 'S/.',
		major_cities: ['Lima', 'Arequipa', 'Trujillo'],
		initials: 'PE',
	},
	{
		country: 'Philippines',
		currency_code: 'PHP',
		currency_symbol: '₱',
		major_cities: ['Manila', 'Quezon City', 'Davao City'],
		initials: 'PH',
	},
	{
		country: 'Poland',
		currency_code: 'PLN',
		currency_symbol: 'zł',
		major_cities: ['Warsaw', 'Kraków', 'Łódź'],
		initials: 'PL',
	},
	{
		country: 'Portugal',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Lisbon', 'Porto', 'Amadora'],
		initials: 'PT',
	},
	{
		country: 'Qatar',
		currency_code: 'QAR',
		currency_symbol: 'ر.ق',
		major_cities: ['Doha', 'Al Rayyan', 'Umm Salal'],
		initials: 'QA',
	},
	{
		country: 'Romania',
		currency_code: 'RON',
		currency_symbol: 'lei',
		major_cities: ['Bucharest', 'Cluj-Napoca', 'Timișoara'],
		initials: 'RO',
	},
	{
		country: 'Russia',
		currency_code: 'RUB',
		currency_symbol: '₽',
		major_cities: ['Moscow', 'Saint Petersburg', 'Novosibirsk'],
		initials: 'RU',
	},
	{
		country: 'Rwanda',
		currency_code: 'RWF',
		currency_symbol: 'FRw',
		major_cities: ['Kigali', 'Butare', 'Gitarama'],
		initials: 'RW',
	},
	{
		country: 'Saint Kitts and Nevis',
		currency_code: 'XCD',
		currency_symbol: '$',
		major_cities: ['Basseterre', 'Charlestown', 'Sandy Point Town'],
		initials: 'KN',
	},
	{
		country: 'Saint Lucia',
		currency_code: 'XCD',
		currency_symbol: '$',
		major_cities: ['Castries', 'Gros Islet', 'Vieux Fort'],
		initials: 'LC',
	},
	{
		country: 'Saint Vincent and the Grenadines',
		currency_code: 'XCD',
		currency_symbol: '$',
		major_cities: ['Kingstown', 'Arnos Vale', 'Layou'],
		initials: 'VC',
	},
	{
		country: 'Samoa',
		currency_code: 'WST',
		currency_symbol: 'T',
		major_cities: ['Apia', 'Vaitele', 'Faleula'],
		initials: 'WS',
	},
	{
		country: 'San Marino',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['San Marino', 'Serravalle', 'Borgo Maggiore'],
		initials: 'SM',
	},
	{
		country: 'Sao Tome and Principe',
		currency_code: 'STN',
		currency_symbol: 'Db',
		major_cities: ['São Tomé', 'Santo António', 'Neves'],
		initials: 'ST',
	},
	{
		country: 'Saudi Arabia',
		currency_code: 'SAR',
		currency_symbol: 'ر.س',
		major_cities: ['Riyadh', 'Jeddah', 'Mecca'],
		initials: 'SA',
	},
	{
		country: 'Senegal',
		currency_code: 'XOF',
		currency_symbol: 'CFA',
		major_cities: ['Dakar', 'Touba', 'Thiès'],
		initials: 'SN',
	},
	{
		country: 'Serbia',
		currency_code: 'RSD',
		currency_symbol: 'дин.',
		major_cities: ['Belgrade', 'Novi Sad', 'Niš'],
		initials: 'RS',
	},
	{
		country: 'Seychelles',
		currency_code: 'SCR',
		currency_symbol: '₨',
		major_cities: ['Victoria', 'Anse Boileau', 'Beau Vallon'],
		initials: 'SC',
	},
	{
		country: 'Sierra Leone',
		currency_code: 'SLL',
		currency_symbol: 'Le',
		major_cities: ['Freetown', 'Bo', 'Kenema'],
		initials: 'SL',
	},
	{
		country: 'Singapore',
		currency_code: 'SGD',
		currency_symbol: '$',
		major_cities: ['Singapore'],
		initials: 'SG',
	},
	{
		country: 'Slovakia',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Bratislava', 'Košice', 'Prešov'],
		initials: 'SK',
	},
	{
		country: 'Slovenia',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Ljubljana', 'Maribor', 'Celje'],
		initials: 'SI',
	},
	{
		country: 'Solomon Islands',
		currency_code: 'SBD',
		currency_symbol: '$',
		major_cities: ['Honiara', 'Auki', 'Gizo'],
		initials: 'SB',
	},
	{
		country: 'Somalia',
		currency_code: 'SOS',
		currency_symbol: 'Sh',
		major_cities: ['Mogadishu', 'Hargeisa', 'Bosaso'],
		initials: 'SO',
	},
	{
		country: 'South Africa',
		currency_code: 'ZAR',
		currency_symbol: 'R',
		major_cities: ['Johannesburg', 'Cape Town', 'Durban'],
		initials: 'ZA',
	},
	{
		country: 'South Korea',
		currency_code: 'KRW',
		currency_symbol: '₩',
		major_cities: ['Seoul', 'Busan', 'Incheon'],
		initials: 'KR',
	},
	{
		country: 'South Sudan',
		currency_code: 'SSP',
		currency_symbol: '£',
		major_cities: ['Juba', 'Wau', 'Malakal'],
		initials: 'SS',
	},
	{
		country: 'Spain',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Madrid', 'Barcelona', 'Valencia'],
		initials: 'ES',
	},
	{
		country: 'Sri Lanka',
		currency_code: 'LKR',
		currency_symbol: 'Rs',
		major_cities: ['Colombo', 'Kandy', 'Galle'],
		initials: 'LK',
	},
	{
		country: 'Sudan',
		currency_code: 'SDG',
		currency_symbol: 'ج.س.',
		major_cities: ['Khartoum', 'Omdurman', 'Nyala'],
		initials: 'SD',
	},
	{
		country: 'Suriname',
		currency_code: 'SRD',
		currency_symbol: '$',
		major_cities: ['Paramaribo', 'Lelydorp', 'Nieuw Nickerie'],
		initials: 'SR',
	},
	{
		country: 'Sweden',
		currency_code: 'SEK',
		currency_symbol: 'kr',
		major_cities: ['Stockholm', 'Gothenburg', 'Malmö'],
		initials: 'SE',
	},
	{
		country: 'Switzerland',
		currency_code: 'CHF',
		currency_symbol: 'Fr',
		major_cities: ['Zürich', 'Geneva', 'Basel'],
		initials: 'CH',
	},
	{
		country: 'Syria',
		currency_code: 'SYP',
		currency_symbol: '£',
		major_cities: ['Damascus', 'Aleppo', 'Homs'],
		initials: 'SY',
	},
	{
		country: 'Taiwan',
		currency_code: 'TWD',
		currency_symbol: 'NT$',
		major_cities: ['Taipei', 'Kaohsiung', 'Taichung'],
		initials: 'TW',
	},
	{
		country: 'Tajikistan',
		currency_code: 'TJS',
		currency_symbol: 'ЅМ',
		major_cities: ['Dushanbe', 'Khujand', 'Kulob'],
		initials: 'TJ',
	},
	{
		country: 'Tanzania',
		currency_code: 'TZS',
		currency_symbol: 'Sh',
		major_cities: ['Dar es Salaam', 'Mwanza', 'Zanzibar City'],
		initials: 'TZ',
	},
	{
		country: 'Thailand',
		currency_code: 'THB',
		currency_symbol: '฿',
		major_cities: ['Bangkok', 'Nonthaburi', 'Nakhon Ratchasima'],
		initials: 'TH',
	},
	{
		country: 'Timor-Leste',
		currency_code: 'USD',
		currency_symbol: '$',
		major_cities: ['Dili', 'Baucau', 'Maliana'],
		initials: 'TL',
	},
	{
		country: 'Togo',
		currency_code: 'XOF',
		currency_symbol: 'CFA',
		major_cities: ['Lomé', 'Sokodé', 'Kara'],
		initials: 'TG',
	},
	{
		country: 'Tonga',
		currency_code: 'TOP',
		currency_symbol: 'T$',
		major_cities: ["Nuku'alofa", 'Neiafu', 'Pangai'],
		initials: 'TO',
	},
	{
		country: 'Trinidad and Tobago',
		currency_code: 'TTD',
		currency_symbol: 'TT$',
		major_cities: ['Port of Spain', 'San Fernando', 'Chaguanas'],
		initials: 'TT',
	},
	{
		country: 'Tunisia',
		currency_code: 'TND',
		currency_symbol: 'د.ت',
		major_cities: ['Tunis', 'Sfax', 'Sousse'],
		initials: 'TN',
	},
	{
		country: 'Turkey',
		currency_code: 'TRY',
		currency_symbol: '₺',
		major_cities: ['Istanbul', 'Ankara', 'Izmir'],
		initials: 'TR',
	},
	{
		country: 'Turkmenistan',
		currency_code: 'TMT',
		currency_symbol: 'm',
		major_cities: ['Ashgabat', 'Türkmenabat', 'Daşoguz'],
		initials: 'TM',
	},
	{
		country: 'Tuvalu',
		currency_code: 'AUD',
		currency_symbol: '$',
		major_cities: ['Funafuti', 'Vaiaku', 'Asau'],
		initials: 'TV',
	},
	{
		country: 'Uganda',
		currency_code: 'UGX',
		currency_symbol: 'USh',
		major_cities: ['Kampala', 'Gulu', 'Lira'],
		initials: 'UG',
	},
	{
		country: 'Ukraine',
		currency_code: 'UAH',
		currency_symbol: '₴',
		major_cities: ['Kyiv', 'Kharkiv', 'Odessa'],
		initials: 'UA',
	},
	{
		country: 'United Arab Emirates',
		currency_code: 'AED',
		currency_symbol: 'د.إ',
		major_cities: ['Dubai', 'Abu Dhabi', 'Sharjah'],
		initials: 'AE',
	},
	{
		country: 'United Kingdom',
		currency_code: 'GBP',
		currency_symbol: '£',
		major_cities: ['London', 'Birmingham', 'Manchester'],
		initials: 'GB',
	},
	{
		country: 'United States',
		currency_code: 'USD',
		currency_symbol: '$',
		major_cities: ['New York City', 'Los Angeles', 'Chicago'],
		initials: 'US',
	},
	{
		country: 'Uruguay',
		currency_code: 'UYU',
		currency_symbol: '$',
		major_cities: ['Montevideo', 'Salto', 'Ciudad de la Costa'],
		initials: 'UY',
	},
	{
		country: 'Uzbekistan',
		currency_code: 'UZS',
		currency_symbol: "so'm",
		major_cities: ['Tashkent', 'Namangan', 'Samarkand'],
		initials: 'UZ',
	},
	{
		country: 'Vanuatu',
		currency_code: 'VUV',
		currency_symbol: 'VT',
		major_cities: ['Port Vila', 'Luganville', 'Norsup'],
		initials: 'VU',
	},
	{
		country: 'Vatican City',
		currency_code: 'EUR',
		currency_symbol: '€',
		major_cities: ['Vatican City'],
		initials: 'VA',
	},
	{
		country: 'Venezuela',
		currency_code: 'VES',
		currency_symbol: 'Bs.S',
		major_cities: ['Caracas', 'Maracaibo', 'Valencia'],
		initials: 'VE',
	},
	{
		country: 'Vietnam',
		currency_code: 'VND',
		currency_symbol: '₫',
		major_cities: ['Ho Chi Minh City', 'Hanoi', 'Da Nang'],
		initials: 'VN',
	},
	{
		country: 'Yemen',
		currency_code: 'YER',
		currency_symbol: '﷼',
		major_cities: ["Sana'a", 'Aden', 'Taiz'],
		initials: 'YE',
	},
	{
		country: 'Zambia',
		currency_code: 'ZMW',
		currency_symbol: 'ZK',
		major_cities: ['Lusaka', 'Kitwe', 'Ndola'],
		initials: 'ZM',
	},
	{
		country: 'Zimbabwe',
		currency_code: 'ZWL',
		currency_symbol: '$',
		major_cities: ['Harare', 'Bulawayo', 'Chitungwiza'],
		initials: 'ZW',
	},
];
