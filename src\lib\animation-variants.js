// Animation variants for Framer Motion
export const containerVariants = {
	hidden: { opacity: 0 },
	visible: {
		opacity: 1,
		transition: {
			staggerChildren: 0.1,
			delayChildren: 0.2,
			duration: 0.5,
		},
	},
	exit: {
		opacity: 0,
		y: 20,
		transition: {
			staggerChildren: 0.05,
			staggerDirection: -1,
			duration: 0.3,
		},
	},
};

export const itemVariants = {
	hidden: { opacity: 0, y: 20 },
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			type: 'spring',
			stiffness: 300,
			damping: 24,
		},
	},
	exit: {
		opacity: 0,
		y: -20,
		transition: {
			duration: 0.2,
		},
	},
};

export const slideVariants = {
	enter: (direction) => ({
		x: direction > 0 ? 100 : -100,
		opacity: 0,
	}),
	center: {
		x: 0,
		opacity: 1,
		transition: {
			x: { type: 'spring', stiffness: 300, damping: 30 },
			opacity: { duration: 0.4 },
		},
	},
	exit: (direction) => ({
		x: direction < 0 ? 100 : -100,
		opacity: 0,
		transition: {
			x: { type: 'spring', stiffness: 300, damping: 30 },
			opacity: { duration: 0.2 },
		},
	}),
};

export const fadeInUp = {
	hidden: { opacity: 0, y: 20 },
	show: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.5,
			ease: [0.22, 1, 0.36, 1],
		},
	},
};

export const staggerContainer = {
	hidden: { opacity: 0 },
	show: {
		opacity: 1,
		transition: {
			staggerChildren: 0.1,
		},
	},
};
