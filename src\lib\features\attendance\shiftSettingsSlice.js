import { customFetch, showErrors } from '@/lib/utils';
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { toast } from 'sonner';

const initialState = {
	shifts: [],
	currentShift: null,
	availableEmployees: [],
	isLoading: false,
	rosterShifts: [],
	genericShifts: [],
};

export const createShift = createAsyncThunk(
	'shiftSettings/createShift',
	async (shiftData, thunkAPI) => {
		try {
			const { data } = await customFetch.post('/shifts', shiftData);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchShifts = createAsyncThunk(
	'shiftSettings/fetchShifts',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/shifts');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchShiftById = createAsyncThunk(
	'shiftSettings/fetchShiftById',
	async (shiftId, thunkAPI) => {
		try {
			const { data } = await customFetch.get(`/shifts/${shiftId}`);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const filterAvailableEmployees = createAsyncThunk(
	'shiftSettings/filterAvailableEmployees',
	async ({ businessUnitIds, departmentIds, designationIds } = {}, thunkAPI) => {
		try {
			const { data } = await customFetch.get(`/shifts/assign-shift/employees`, {
				params: {
					businessUnitIds,
					departmentIds,
					designationIds,
				},
			});
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateShift = createAsyncThunk(
	'shiftSettings/updateShift',
	async ({ shiftId, shiftData }, thunkAPI) => {
		try {
			shiftData.shiftId = shiftId;
			delete shiftData.type;
			const { data } = await customFetch.patch(`/shifts`, shiftData);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const assignShift = createAsyncThunk(
	'shiftSettings/assignShift',
	async (payload, thunkAPI) => {
		try {
			// payload = {
			//   shiftId: '12345',
			//   employeeIds: ['12345', '12346']
			// }
			const { data } = await customFetch.post('/shifts/assign-shift', payload);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const getGenericShifts = createAsyncThunk(
	'shiftSettings/getGenericShifts',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/shifts/generic-shifts');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const getRosterShifts = createAsyncThunk(
	'shiftSettings/getRosterShifts',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/shifts/roster-shifts');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const shiftSettingsSlice = createSlice({
	name: 'shiftSettings',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(createShift.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(createShift.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(createShift.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchShifts.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchShifts.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.shifts = payload.data.shifts;
			})
			.addCase(fetchShifts.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchShiftById.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchShiftById.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.currentShift = payload?.data[0];
			})
			.addCase(fetchShiftById.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(filterAvailableEmployees.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(filterAvailableEmployees.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.availableEmployees = payload.data;
			})
			.addCase(filterAvailableEmployees.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateShift.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(updateShift.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateShift.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(assignShift.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(assignShift.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(assignShift.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(getGenericShifts.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getGenericShifts.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.genericShifts = payload.data;
			})
			.addCase(getGenericShifts.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(getRosterShifts.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getRosterShifts.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.rosterShifts = payload.data;
			})
			.addCase(getRosterShifts.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

export const {} = shiftSettingsSlice.actions;
export default shiftSettingsSlice.reducer;
