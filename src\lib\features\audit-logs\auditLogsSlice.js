import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { customFetch } from '@/lib/utils';

const initialState = {
	isLoading: false,
	auditLogs: [],
};

export const fetchAuditLogs = createAsyncThunk(
	'auditLogs/fetchAuditLogs',
	async (module, thunkAPI) => {
		try {
			const { data } = await customFetch.get(`/audit-logs/${module}`);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const auditLogsSlice = createSlice({
	name: 'auditLogs',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchAuditLogs.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchAuditLogs.fulfilled, (state, action) => {
				state.isLoading = false;
				state.auditLogs = action.payload.data;
			})
			.addCase(fetchAuditLogs.rejected, (state) => {
				state.isLoading = false;
			});
	},
});

export const {} = auditLogsSlice.actions;
export default auditLogsSlice.reducer;
