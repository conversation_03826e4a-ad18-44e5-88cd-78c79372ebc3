import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';

const initialState = {
	isLoading: false,
	user: null,
	authenticatedUser: null,
	isVerified: false,
	enterToken: false,
	isLoggedOut: false,
	userEmail: null,
	registrationCompleted: false,
};

export const showUser = createAsyncThunk(
	'auth/showUser',
	async (user, thunkAPI) => {
		try {
			const { data } = await customFetch('/auth/me', {
				withCredentials: true,
			});
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const registerUser = createAsyncThunk(
	'auth/registerUser',
	async (password, thunkAPI) => {
		try {
			const { data } = await customFetch.patch('/auth/register', password, {
				withCredentials: true,
			});
			thunkAPI.dispatch(showUser());
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const verifyEmail = createAsyncThunk(
	'auth/verifyEmail',
	async (tokenEmail, thunkAPI) => {
		try {
			const { data } = await customFetch.post('auth/verify-email', tokenEmail, {
				withCredentials: true,
			});
			if (data.success) {
				thunkAPI.dispatch(showUser());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const logoutUser = createAsyncThunk(
	'auth/logoutUser',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.delete('/auth/logout', {
				withCredentials: true,
			});

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const registerEmail = createAsyncThunk(
	'auth/registerEmail',
	async (email, thunkAPI) => {
		try {
			const { data } = await customFetch.post('/auth/register', email, {
				withCredentials: true,
			});
			if (data?.data?.isPendingRegistration) {
				console.log('condition was met');
				thunkAPI.dispatch(showUser());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const loginUser = createAsyncThunk(
	'auth/login',
	async (user, thunkAPI) => {
		try {
			const { data } = await customFetch.post('/auth/login', user, {
				withCredentials: true,
			});
			if (data?.success) {
				thunkAPI.dispatch(showUser());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const requestResetLink = createAsyncThunk(
	'auth/requestResetLink',
	async (emailData, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/auth/forgot-password',
				emailData,
				{
					withCredentials: true,
				}
			);

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const resetPassword = createAsyncThunk(
	'auth/resetPassword',
	async (passwordData, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/auth/reset-password',
				passwordData,
				{
					withCredentials: true,
				}
			);

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const loginAfterQrScan = createAsyncThunk(
	'auth/loginAfterQrScan',
	async (credentials, thunkAPI) => {
		try {
			const { data } = await customFetch.post('/auth/confirm-qr', credentials);
			if (data?.success) {
				thunkAPI.dispatch(showUser());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const authSlice = createSlice({
	name: 'auth',
	initialState,
	reducers: {
		changeEmail: (state) => {
			state.isVerified = false;
			state.isLoading = false;
			state.enterToken = false;
			state.user = null;
			state.userEmail = null;
		},
		updateUser: (state, { payload }) => {
			state.user = { ...payload };
		},
		storeEmailInState: (state, { payload }) => {
			state.userEmail = payload;
		},
		updateLogout: (state) => {
			state.isLoggedOut = true;
			state.authenticatedUser = null;
			state.user = null;
			state.userEmail = null;
		},
	},
	extraReducers: (builder) => {
		builder
			.addCase(registerEmail.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(registerEmail.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				if (payload.data?.isPendingRegistration) {
					state.isVerified = true;
					state.enterToken = false;
					toast.success(payload.message);
					return;
				}
				state.enterToken = true;
				toast.success(payload.message);
			})
			.addCase(registerEmail.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(verifyEmail.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(verifyEmail.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.enterToken = false;
				state.isVerified = true;
				toast.success(payload.message);
			})
			.addCase(verifyEmail.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(registerUser.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(registerUser.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.user = null;
				state.enterToken = false;
				state.isVerified = false;
				state.registrationCompleted = true;
				toast.success(payload.message);
			})
			.addCase(registerUser.rejected, (state, { payload }) => {
				state.isLoading = false;
				state.authenticatedUser = null;
				showErrors(payload);
			})
			.addCase(loginUser.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(loginUser.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.isLoggedOut = false;
				toast.success(payload.message);
			})
			.addCase(loginUser.rejected, (state, { payload }) => {
				state.isLoading = false;
				state.authenticatedUser = null;
				showErrors(payload);
			})
			.addCase(logoutUser.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(logoutUser.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(logoutUser.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(showUser.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(showUser.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.authenticatedUser = payload.data.authenticatedUser;
				// toast.success(payload.message);
			})
			.addCase(showUser.rejected, (state, { payload }) => {
				state.isLoading = false;
				state.authenticatedUser = null;
			})
			.addCase(requestResetLink.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(requestResetLink.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(requestResetLink.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(resetPassword.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(resetPassword.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(resetPassword.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(loginAfterQrScan.pending, (state) => {
				state.isLoading = true;
				toast.info('Authenticating...');
			})
			.addCase(loginAfterQrScan.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(loginAfterQrScan.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

export const { changeEmail, updateUser, storeEmailInState, updateLogout } =
	authSlice.actions;
export default authSlice.reducer;
