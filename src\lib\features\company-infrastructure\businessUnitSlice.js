import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';

const initialState = {
	populatedBusinessUnits: [],
	businessUnits: [],
	departments: [],
	isLoading: false,
};

export const fetchPopulatedBusinessUnits = createAsyncThunk(
	'businessUnit/fetchPopulatedBusinessUnits',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch('/business-units/populate', {
				withCredentials: true,
			});
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchBusinessUnits = createAsyncThunk(
	'businessUnit/fetchBusinessUnits',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch('/business-units');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateBusinessUnit = createAsyncThunk(
	'businessUnit/updateBusinessUnit',
	async (businessUnitDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/business-units',
				businessUnitDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchBusinessUnits());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const addBusinessUnit = createAsyncThunk(
	'businessUnit/addBusinessUnit',
	async (businessUnitDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/business-units',
				businessUnitDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchBusinessUnits());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const deleteBusinessUnit = createAsyncThunk(
	'businessUnit/deleteBusinessUnit',
	async (businessUnitIds, thunkAPI) => {
		try {
			const { data } = await customFetch.patch('/business-units/remove', {
				businessUnitIds,
			});

			if (data?.success) {
				thunkAPI.dispatch(fetchBusinessUnits());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchBusinessUnitDepartments = createAsyncThunk(
	'businessUnit/fetchBusinessUnitDepartments',
	async (businessUnitId, thunkAPI) => {
		try {
			const { data } = await customFetch(
				`/business-units/${businessUnitId}/departments`
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const businessUnitSlice = createSlice({
	name: 'businessUnit',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchBusinessUnits.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchBusinessUnits.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.businessUnits = payload.data.businessUnits;
			})
			.addCase(fetchBusinessUnits.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateBusinessUnit.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Business Unit...');
			})
			.addCase(updateBusinessUnit.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateBusinessUnit.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(addBusinessUnit.pending, (state) => {
				state.isLoading = true;
				toast.info('Creating Business Unit...');
			})
			.addCase(addBusinessUnit.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(addBusinessUnit.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(deleteBusinessUnit.pending, (state) => {
				state.isLoading = true;
				toast.info('Deleting Business Unit...');
			})
			.addCase(deleteBusinessUnit.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(deleteBusinessUnit.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchBusinessUnitDepartments.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchBusinessUnitDepartments.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.departments = payload.data;
			})
			.addCase(fetchBusinessUnitDepartments.rejected, (state, { payload }) => {
				state.isLoading = false;
				state.departments = [];
				showErrors(payload);
			})
			.addCase(fetchPopulatedBusinessUnits.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchPopulatedBusinessUnits.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.populatedBusinessUnits = payload.data;
			})
			.addCase(fetchPopulatedBusinessUnits.rejected, (state, { payload }) => {
				state.isLoading = false;
				toast.error(payload);
			});
	},
});

// export const {} = businessUnitSlice.actions;
export default businessUnitSlice.reducer;
