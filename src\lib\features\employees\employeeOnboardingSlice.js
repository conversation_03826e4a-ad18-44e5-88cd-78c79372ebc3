import { customFetch, showErrors } from '@/lib/utils';
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'sonner';

const initialState = {
	isLoading: false,
	step: 2,
	onBoardingEmployeeDetails: null,
};

export const verifyOnboardingLink = createAsyncThunk(
	'employee/verifyOnboardingLink',
	async (employeeDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.get(
				`/employees/verify-link/${employeeDetails.token}`,
				employeeDetails
			);

			if (data.success) {
				if (
					data.data.isContactDetailsComplete &&
					data.data.isQualificationDetailsComplete &&
					data.data.isPersonalDetailsComplete
				) {
					thunkAPI.dispatch(setOnboardingEmployeeLinkStep(4));
				}
				if (!data.data.isContactDetailsComplete) {
					thunkAPI.dispatch(setOnboardingEmployeeLinkStep(3));
				}
				if (!data.data.isQualificationDetailsComplete) {
					thunkAPI.dispatch(setOnboardingEmployeeLinkStep(2));
				}
				if (!data.data.isPersonalDetailsComplete) {
					thunkAPI.dispatch(setOnboardingEmployeeLinkStep(1));
				}
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const registerOnboardingLinkPersonalDetails = createAsyncThunk(
	'employee/registerOnboardingLinkPersonalDetails',
	async (employeeDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/employees/onboarding-link/personal-details',
				employeeDetails,
				{
					headers: {
						'Content-Type': 'multipart/form-data',
					},
				}
			);

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const registerOnboardingLinkQualificationDetails = createAsyncThunk(
	'employee/registerOnboardingLinkQualificationDetails',
	async (employeeDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/employees/onboarding-link/qualifications',
				employeeDetails
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const registerOnboardingLinkContactDetails = createAsyncThunk(
	'employee/registerOnboardingLinkContactDetails',
	async (employeeDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/employees/onboarding-link/contacts',
				employeeDetails
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const employeeOnboardingSlice = createSlice({
	name: 'employeeOnboarding',
	initialState,
	reducers: {
		setOnboardingEmployeeDetails: (state, action) => {
			state.onBoardingEmployeeDetails = action.payload;
		},
		setOnboardingEmployeeLinkStep: (state, action) => {
			state.step = action.payload;
		},
	},
	extraReducers: (builder) => {
		builder
			.addCase(verifyOnboardingLink.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(verifyOnboardingLink.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.onBoardingEmployeeDetails = payload.data;
			})
			.addCase(verifyOnboardingLink.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(registerOnboardingLinkPersonalDetails.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(
				registerOnboardingLinkPersonalDetails.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					state.step = 2;
					toast.success(payload.message);
				}
			)
			.addCase(
				registerOnboardingLinkPersonalDetails.rejected,
				(state, { payload }) => {
					state.isLoading = false;
					showErrors(payload);
				}
			)
			.addCase(registerOnboardingLinkQualificationDetails.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(
				registerOnboardingLinkQualificationDetails.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					state.step = 3;
					toast.success(payload.message);
				}
			)
			.addCase(
				registerOnboardingLinkQualificationDetails.rejected,
				(state, { payload }) => {
					state.isLoading = false;
					showErrors(payload);
				}
			)
			.addCase(registerOnboardingLinkContactDetails.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(
				registerOnboardingLinkContactDetails.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					state.step = 4;
					toast.success(payload.message);
				}
			)
			.addCase(
				registerOnboardingLinkContactDetails.rejected,
				(state, { payload }) => {
					state.isLoading = false;
					showErrors(payload);
				}
			);
	},
});

export const { setOnboardingEmployeeLinkStep, setOnboardingEmployeeDetails } =
	employeeOnboardingSlice.actions;

export default employeeOnboardingSlice.reducer;
