import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';

const initialState = {
	holidays: [],
	holidayGroupHolidays: [],
	isLoading: false,
};

export const fetchHolidays = createAsyncThunk(
	'holiday/fetchHolidays',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch('/holidays');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchHolidaysByHolidayGroupId = createAsyncThunk(
	'holiday/fetchHolidaysByHolidayGroupId',
	async (holidayGroupId, thunkAPI) => {
		try {
			const { data } = await customFetch(`/holidays/${holidayGroupId}`);

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const addHoliday = createAsyncThunk(
	'holiday/addHoliday',
	async (holidayDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post('/holidays', holidayDetails);

			if (data?.success) {
				thunkAPI.dispatch(fetchHolidays());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateHoliday = createAsyncThunk(
	'holiday/updateHoliday',
	async (holidayDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch('/holidays', holidayDetails);

			if (data?.success) {
				thunkAPI.dispatch(fetchHolidays());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const deleteHoliday = createAsyncThunk(
	'holiday/deleteHoliday',
	async (holidayIds, thunkAPI) => {
		try {
			const { data } = await customFetch.patch('/holidays/remove', {
				holidayIds,
			});

			if (data?.success) {
				thunkAPI.dispatch(fetchHolidays());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const holidaySlice = createSlice({
	name: 'holiday',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchHolidays.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchHolidays.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.holidays = payload.data.holidays;
			})
			.addCase(fetchHolidays.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchHolidaysByHolidayGroupId.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(
				fetchHolidaysByHolidayGroupId.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					state.holidayGroupHolidays = payload.data;
				}
			)
			.addCase(fetchHolidaysByHolidayGroupId.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(addHoliday.pending, (state) => {
				state.isLoading = true;
				toast.info('Creating Holiday...');
			})
			.addCase(addHoliday.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(addHoliday.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateHoliday.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Holiday...');
			})
			.addCase(updateHoliday.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateHoliday.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(deleteHoliday.pending, (state) => {
				state.isLoading = true;
				toast.info('Deleting Holiday...');
			})
			.addCase(deleteHoliday.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(deleteHoliday.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

// export const {} = holidaySlice.actions;
export default holidaySlice.reducer;
