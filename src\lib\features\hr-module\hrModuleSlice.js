import { customFetch, showErrors } from '@/lib/utils';
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { toast } from 'sonner';

const initialState = {
	genderData: null,
	educationData: null,
	skillsData: null,
	totalBusinessUnits: null,
	totalDepartments: null,
	totalDesignations: null,
	totalEmployees: null,
	isLoading: false,
};

export const fetchGenderAndEducationData = createAsyncThunk(
	'hrModule/fetchGenderAndEducationData',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('hr-module/gender-education');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const hrModuleSlice = createSlice({
	name: 'hrModule',
	initialState,
	reducers: {
		// define your reducers here
	},
	extraReducers: (builder) => {
		builder
			.addCase(fetchGenderAndEducationData.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchGenderAndEducationData.fulfilled, (state, { payload }) => {
				console.log(` .addCase - payload:`, payload);
				state.isLoading = false;
				state.genderData = payload?.data?.genderCounts;
				state.educationData = payload?.data?.educationCounts;
				state.skillsData = payload?.data?.skillsCounts;
				state.totalBusinessUnits = payload?.data?.totalBusinessUnits;
				state.totalDepartments = payload?.data?.totalDepartments;
				state.totalDesignations = payload?.data?.totalDesignations;
				state.totalEmployees = payload?.data?.totalEmployees;
			})
			.addCase(fetchGenderAndEducationData.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

// export const {} = hrModuleSlice.actions;
export default hrModuleSlice.reducer;
