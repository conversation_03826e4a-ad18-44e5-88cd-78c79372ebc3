import { customFetch } from '@/lib/utils';
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'sonner';

const initialState = {
	timezones: [],
	countryCities: [],
	countries: [],
	currencies: [],
	dialCodes: [],
	isLoading: false,
	cities: [],
	isLoadingCities: false,
};

export const fetchCountryCities = createAsyncThunk(
	'location/fetchCountryCities',
	async (countryName, thunkAPI) => {
		try {
			const { data } = await customFetch.get(
				`/countries/cities/${countryName}`
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchCountries = createAsyncThunk(
	'location/fetchCountries',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/countries');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchCurrencies = createAsyncThunk(
	'location/fetchCurrencies',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/countries/currencies');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchDialCodes = createAsyncThunk(
	'location/fetchDialCodes',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/countries/dial-codes');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchCities = createAsyncThunk(
	'location/fetchCities',
	async (country, thunkAPI) => {
		try {
			const { data } = await customFetch.get(
				`/countries/search/cities?countryId=${country.countryId}&citySearchTerm=${country.citySearchTerm}`
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchTimezones = createAsyncThunk(
	'location/fetchTimezones',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/countries/timezones');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const locationSlice = createSlice({
	name: 'location',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchCountries.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchCountries.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.countries = payload.data;
			})
			.addCase(fetchCountries.rejected, (state) => {
				state.isLoading = false;
				toast.error('Failed to fetch cities');
			})
			.addCase(fetchCountryCities.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchCountryCities.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.countryCities = payload.data;
			})
			.addCase(fetchCountryCities.rejected, (state) => {
				state.isLoading = false;
				toast.error('Failed to fetch countries');
			})
			.addCase(fetchCurrencies.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchCurrencies.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.currencies = payload.data;
			})
			.addCase(fetchCurrencies.rejected, (state) => {
				state.isLoading = false;
				toast.error('Failed to fetch currencies');
			})
			.addCase(fetchDialCodes.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchDialCodes.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.dialCodes = payload.data[0].phoneCodes;
			})
			.addCase(fetchDialCodes.rejected, (state) => {
				state.isLoading = false;
				toast.error('Failed to fetch dial codes');
			})
			.addCase(fetchCities.pending, (state) => {
				state.isLoadingCities = true;
			})
			.addCase(fetchCities.fulfilled, (state, { payload }) => {
				state.isLoadingCities = false;
				state.cities = payload.data;
			})
			.addCase(fetchCities.rejected, (state) => {
				state.isLoadingCities = false;
			})
			.addCase(fetchTimezones.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchTimezones.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.timezones = payload.data;
			})
			.addCase(fetchTimezones.rejected, (state) => {
				state.isLoading = false;
				toast.error('Failed to fetch timezones');
			});
	},
});

export default locationSlice.reducer;
