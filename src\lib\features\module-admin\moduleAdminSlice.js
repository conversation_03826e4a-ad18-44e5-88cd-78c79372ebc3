import { customFetch, showErrors } from '@/lib/utils';
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { toast } from 'sonner';

const initialState = {
	moduleAdmins: [],
	isLoading: false,
};

export const fetchModuleAdmins = createAsyncThunk(
	'moduleAdmin/fetchModuleAdmins',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/module-admins');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const createModuleAdmin = createAsyncThunk(
	'moduleAdmin/createModuleAdmin',
	async (moduleAdminData, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/module-admins',
				moduleAdminData
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateModuleAdmin = createAsyncThunk(
	'moduleAdmin/updateModuleAdmin',
	async (moduleAdminData, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/module-admins',
				moduleAdminData
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const deleteModuleAdmin = createAsyncThunk(
	'moduleAdmin/deleteModuleAdmin',
	async (ids, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(`/module-admins`, {
				moduleAdminIds: ids,
			});
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const moduleAdminSlice = createSlice({
	name: 'moduleAdmin',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchModuleAdmins.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchModuleAdmins.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				console.log(payload.data);
				state.moduleAdmins = payload.data.moduleAdmins;
			})
			.addCase(fetchModuleAdmins.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(createModuleAdmin.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(createModuleAdmin.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(createModuleAdmin.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateModuleAdmin.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(updateModuleAdmin.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateModuleAdmin.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(deleteModuleAdmin.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(deleteModuleAdmin.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(deleteModuleAdmin.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

// export const {} = moduleAdminSlice.actions;
export default moduleAdminSlice.reducer;
