import { customFetch, showErrors } from '@/lib/utils';
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'sonner';

const initialState = {
	clients: [],
	isLoading: false,
};

export const fetchClients = createAsyncThunk(
	'superAdmin/fetchClients',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('super-admin/clients');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const promoteClientToGlorifiedAdmin = createAsyncThunk(
	'superAdmin/promoteClientToGlorifiedAdmin',
	async (clientId, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				`super-admin/promote-client`,
				clientId
			);
			if (data.success === true) {
				thunkAPI.dispatch(fetchClients());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const superAdminSlice = createSlice({
	name: 'superAdmin',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchClients.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchClients.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.clients = payload.data.clients;
			})
			.addCase(fetchClients.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(promoteClientToGlorifiedAdmin.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(
				promoteClientToGlorifiedAdmin.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					toast.success(payload.message);
				}
			)
			.addCase(promoteClientToGlorifiedAdmin.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

export default superAdminSlice.reducer;
