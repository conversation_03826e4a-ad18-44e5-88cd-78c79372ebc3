// Using the provided API response for tasks
export const mockTasks = {
	status: 200,
	message: 'Success',
	data: [
		{
			_id: '6851b3e5480ebfdaa2d4129e',
			name: 'Design new homepage',
			code: 'WEB-01',
			description:
				'Create mockups and prototypes for the new homepage design. Focus on user experience and modern aesthetics.',
			status: 'in-progress',
			assignedTo: '68497292eb94be586b395caa', // <PERSON>
			assignedBy: '68481c4dbc227ae73d4bf3c9', // <PERSON>
			projectId: '684d51ba6795ad5611e6d016', // Website Redesign
			dueDate: '2025-07-15T00:00:00.000Z',
			priority: 'high',
			media: [
				{
					url: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?ixlib=rb-1.2.1&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=400&fit=max&ixid=eyJhcHBfaWQiOjE0NTg5fQ',
					public_id: 'landscape.jpg',
					resource_type: 'image',
					_id: '6851b3e5480ebfdaa2d4129f',
				},
				{
					url: 'https://file-examples-com.github.io/uploads/2017/02/file_example_CSV_5000.csv',
					public_id: 'data.csv',
					resource_type: 'document',
					_id: 'media002',
				},
			],
			color: '4A90E2', // Blue
			comments: [
				{
					_id: { $oid: 'comment001' },
					comment: 'Initial designs look promising!',
					createdAt: { $date: '2025-06-18T10:00:00.000Z' },
					userId: { $oid: '68481c4dbc227ae73d4bf3c9' },
					user: {
						_id: { $oid: '68481c4dbc227ae73d4bf3c9' },
						name: 'John Smith',
						profilePhoto: '/placeholder.svg?width=40&height=40&text=JS',
					},
				},
			],
			createdAt: '2025-06-17T18:28:53.740Z',
			updatedAt: '2025-06-18T10:00:00.000Z',
		},
		{
			_id: '6851b5ce480ebfdaa2d412bb',
			name: 'Develop API endpoints',
			code: 'API-03',
			description:
				"Implement CRUD operations for the new 'products' resource. Ensure proper authentication and validation.",
			status: 'pending',
			assignedTo: '68497292eb94be586b395cad', // Mike Brown
			assignedBy: '68481c4dbc227ae73d4bf3c9', // John Smith
			projectId: '684d51ba6795ad5611e6d017', // Backend Refactor
			dueDate: '2025-08-01T00:00:00.000Z',
			priority: 'medium',
			media: [],
			color: 'F5A623', // Orange
			comments: [],
			createdAt: '2025-06-17T18:37:02.665Z',
			updatedAt: '2025-06-17T18:37:02.665Z',
		},
		{
			_id: '6851b63450cc3610d4c15503',
			name: 'User testing session',
			code: 'UX-05',
			description:
				'Conduct user testing for the new checkout flow. Gather feedback and identify pain points.',
			status: 'completed',
			assignedTo: '68497292eb94be586b395cae', // Sarah Lee
			assignedBy: '68481c4dbc227ae73d4bf3c9', // John Smith
			projectId: '684d51ba6795ad5611e6d016', // Website Redesign
			dueDate: '2025-06-10T00:00:00.000Z',
			priority: 'high',
			media: [
				{
					url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
					public_id: 'report.pdf',
					resource_type: 'document',
					_id: 'media003',
				},
			],
			color: '7ED321', // Green
			comments: [],
			createdAt: '2025-06-01T09:00:00.000Z',
			updatedAt: '2025-06-10T17:00:00.000Z',
		},
		{
			_id: '6851b8ecc9ab4b3d6554edb8',
			name: 'Fix login bug',
			code: 'BUG-102',
			description:
				'Users are reporting issues when trying to reset their password. Investigate and deploy a fix.',
			status: 'overdue',
			assignedTo: '68497292eb94be586b395caa', // Jane Doe
			assignedBy: '68481c4dbc227ae73d4bf3c9', // John Smith
			projectId: '684d51ba6795ad5611e6d017', // Backend Refactor
			dueDate: '2025-06-15T00:00:00.000Z',
			priority: 'high',
			media: [],
			color: 'D0021B', // Red
			comments: [],
			createdAt: '2025-06-12T14:30:00.000Z',
			updatedAt: '2025-06-12T14:30:00.000Z',
		},
		{
			_id: '6851bbc455b86bd8bc1bf7f7',
			name: 'Marketing Campaign Setup',
			code: 'MKT-007',
			description:
				'Prepare assets and copy for the new summer marketing campaign.',
			status: 'cancelled',
			assignedTo: '68497292eb94be586b395cae', // Sarah Lee
			assignedBy: '68481c4dbc227ae73d4bf3c9', // John Smith
			projectId: '684d51ba6795ad5611e6d018', // Marketing Q3
			dueDate: '2025-07-01T00:00:00.000Z',
			priority: 'medium',
			media: [
				{
					url: 'https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4',
					public_id: 'campaign-video.mp4',
					resource_type: 'video',
					_id: '6851bbc455b86bd8bc1bf7f8',
				},
			],
			color: 'BD10E0', // Purple
			comments: [],
			createdAt: '2025-06-10T19:02:28.056Z',
			updatedAt: '2025-06-11T19:02:28.056Z',
		},
	],
	success: true,
};

// Using the provided task detail response structure for one task
export const mockTaskDetails = [
	{
		_id: { $oid: '6851b3e5480ebfdaa2d4129e' },
		name: 'Design new homepage',
		code: 'WEB-01',
		description:
			'Create mockups and prototypes for the new homepage design. Focus on user experience and modern aesthetics.',
		status: 'in-progress',
		assignedTo: { $oid: '68497292eb94be586b395caa' }, // Jane Doe
		assignedBy: { $oid: '68481c4dbc227ae73d4bf3c9' }, // John Smith
		projectId: { $oid: '684d51ba6795ad5611e6d016' }, // Website Redesign
		dueDate: { $date: '2025-07-15T00:00:00.000Z' },
		priority: 'high',
		media: [
			{
				url: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?ixlib=rb-1.2.1&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=400&fit=max&ixid=eyJhcHBfaWQiOjE0NTg5fQ',
				public_id: 'landscape.jpg',
				resource_type: 'image',
				_id: { $oid: '6851b3e5480ebfdaa2d4129f' },
			},
			{
				url: 'https://file-examples-com.github.io/uploads/2017/02/file_example_CSV_5000.csv',
				public_id: 'data.csv',
				resource_type: 'document',
				_id: { $oid: 'media002' },
			},
		],
		color: '4A90E2',
		comments: [
			{
				_id: { $oid: 'comment001' },
				comment: 'Initial designs look promising!',
				createdAt: { $date: '2025-06-18T10:00:00.000Z' },
				userId: { $oid: '68481c4dbc227ae73d4bf3c9' },
				user: {
					_id: { $oid: '68481c4dbc227ae73d4bf3c9' },
					name: 'John Smith',
					profilePhoto: '/placeholder.svg?width=40&height=40&text=JS',
				},
			},
			{
				_id: { $oid: 'comment002' },
				comment: 'Can we try a version with a darker color palette?',
				createdAt: { $date: '2025-06-19T14:20:00.000Z' },
				userId: { $oid: '68497292eb94be586b395caa' },
				user: {
					_id: { $oid: '68497292eb94be586b395caa' },
					name: 'Jane Doe',
					profilePhoto: '/placeholder.svg?width=40&height=40&text=JD',
				},
			},
		],
		createdAt: { $date: '2025-06-17T18:28:53.740Z' },
		updatedAt: { $date: '2025-06-19T14:20:00.000Z' },
	},
	// Add more mock details if needed, matching IDs from mockTasks.data
	{
		_id: { $oid: '6851b5ce480ebfdaa2d412bb' },
		name: 'Develop API endpoints',
		code: 'API-03',
		description:
			"Implement CRUD operations for the new 'products' resource. Ensure proper authentication and validation.",
		status: 'pending',
		assignedTo: { $oid: '68497292eb94be586b395cad' },
		assignedBy: { $oid: '68481c4dbc227ae73d4bf3c9' },
		projectId: { $oid: '684d51ba6795ad5611e6d017' },
		dueDate: { $date: '2025-08-01T00:00:00.000Z' },
		priority: 'medium',
		media: [],
		color: 'F5A623',
		comments: [],
		createdAt: { $date: '2025-06-17T18:37:02.665Z' },
		updatedAt: { $date: '2025-06-17T18:37:02.665Z' },
	},
];

export const mockUsers = [
	{
		id: '68481c4dbc227ae73d4bf3c9',
		name: 'John Smith',
		avatar: '/placeholder.svg?width=40&height=40&text=JS',
	},
	{
		id: '68497292eb94be586b395caa',
		name: 'Jane Doe',
		avatar: '/placeholder.svg?width=40&height=40&text=JD',
	},
	{
		id: '68497292eb94be586b395cad',
		name: 'Mike Brown',
		avatar: '/placeholder.svg?width=40&height=40&text=MB',
	},
	{
		id: '68497292eb94be586b395cae',
		name: 'Sarah Lee',
		avatar: '/placeholder.svg?width=40&height=40&text=SL',
	},
	{
		id: 'currentUserMockId',
		name: 'Current User',
		avatar: '/placeholder.svg?width=40&height=40&text=CU',
	},
];

export const mockProjects = [
	{ id: '684d51ba6795ad5611e6d016', name: 'Website Redesign' },
	{ id: '684d51ba6795ad5611e6d017', name: 'Backend Refactor' },
	{ id: '684d51ba6795ad5611e6d018', name: 'Marketing Q3' },
];
