import {
	calculatePerformanceScore,
	getAchievementDetails,
} from '../performanceCalculator';
import dayjs from 'dayjs';

describe('Performance Calculator', () => {
	// Mock data for testing
	const mockUserFlags = {
		isRegistrationComplete: true,
		isQualificationDetailsComplete: true,
		isFamilyDetailsComplete: true,
		isSkillsDetailsComplete: false,
		isContactDetailsComplete: true,
		isEmploymentDetailsComplete: true,
		isEarningsDetailsComplete: false,
		isBenefitsDetailsComplete: false,
		isPersonalDetailsComplete: true,
	};

	const mockAttendanceLogs = [
		{
			type: 'clockIn',
			time: dayjs().subtract(1, 'day').hour(9).minute(0).toISOString(),
		},
		{
			type: 'clockOut',
			time: dayjs().subtract(1, 'day').hour(17).minute(30).toISOString(),
		},
		{
			type: 'clockIn',
			time: dayjs().subtract(2, 'day').hour(8).minute(45).toISOString(),
		},
		{
			type: 'clockOut',
			time: dayjs().subtract(2, 'day').hour(17).minute(15).toISOString(),
		},
		{
			type: 'start-break',
			time: dayjs().subtract(1, 'day').hour(12).minute(0).toISOString(),
		},
		{
			type: 'end-break',
			time: dayjs().subtract(1, 'day').hour(13).minute(0).toISOString(),
		},
	];

	test('calculates performance score correctly', () => {
		const data = {
			attendanceLogs: mockAttendanceLogs,
			userFlags: mockUserFlags,
			personalDetails: {},
		};

		const result = calculatePerformanceScore(data);

		expect(result).toHaveProperty('totalScore');
		expect(result).toHaveProperty('achievementLevel');
		expect(result).toHaveProperty('breakdown');
		expect(result).toHaveProperty('metrics');

		expect(result.totalScore).toBeGreaterThanOrEqual(0);
		expect(result.totalScore).toBeLessThanOrEqual(100);
		expect(['bronze', 'silver', 'gold', 'platinum']).toContain(
			result.achievementLevel
		);
	});

	test('calculates profile completion correctly', () => {
		const data = {
			attendanceLogs: [],
			userFlags: mockUserFlags,
			personalDetails: {},
		};

		const result = calculatePerformanceScore(data);

		// 6 out of 9 fields are complete = 67%
		expect(result.breakdown.profileCompletion).toBe(67);
	});

	test('handles empty attendance logs', () => {
		const data = {
			attendanceLogs: [],
			userFlags: mockUserFlags,
			personalDetails: {},
		};

		const result = calculatePerformanceScore(data);

		expect(result.breakdown.attendance).toBe(0);
		expect(result.breakdown.consistency).toBe(50); // Default for new users
		expect(result.breakdown.engagement).toBe(50); // Default
	});

	test('returns correct achievement details', () => {
		const bronzeDetails = getAchievementDetails('bronze');
		expect(bronzeDetails.name).toBe('Bronze');
		expect(bronzeDetails.color).toBe('#CD7F32');

		const platinumDetails = getAchievementDetails('platinum');
		expect(platinumDetails.name).toBe('Platinum');
		expect(platinumDetails.color).toBe('#E5E4E2');
	});

	test('determines achievement levels correctly', () => {
		// Test with high completion profile
		const highPerformanceData = {
			attendanceLogs: mockAttendanceLogs,
			userFlags: {
				...mockUserFlags,
				isSkillsDetailsComplete: true,
				isEarningsDetailsComplete: true,
				isBenefitsDetailsComplete: true,
			},
			personalDetails: {},
		};

		const result = calculatePerformanceScore(highPerformanceData);

		// With 100% profile completion and some attendance, should be at least silver
		expect(['silver', 'gold', 'platinum']).toContain(result.achievementLevel);
	});

	test('handles missing or invalid data gracefully', () => {
		const result = calculatePerformanceScore({});

		expect(result.totalScore).toBeGreaterThanOrEqual(0);
		expect(result.achievementLevel).toBe('bronze'); // Default for low scores
		expect(result.breakdown.profileCompletion).toBe(0);
	});
});
