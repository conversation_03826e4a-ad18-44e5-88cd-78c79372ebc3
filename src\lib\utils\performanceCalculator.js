import dayjs from 'dayjs';

/**
 * Calculate performance score based on various metrics
 * @param {Object} data - Contains attendance logs, user flags, and other metrics
 * @returns {Object} - Performance score and breakdown
 */
export const calculatePerformanceScore = (data) => {
	const { attendanceLogs = [], userFlags = {}, personalDetails = {} } = data;

	// Initialize scores
	let attendanceScore = 0;
	let profileCompletionScore = 0;
	let consistencyScore = 0;
	let engagementScore = 0;

	// 1. Profile Completion Score (30% weight)
	const profileFields = [
		userFlags.isRegistrationComplete,
		userFlags.isQualificationDetailsComplete,
		userFlags.isFamilyDetailsComplete,
		userFlags.isSkillsDetailsComplete,
		userFlags.isContactDetailsComplete,
		userFlags.isEmploymentDetailsComplete,
		userFlags.isEarningsDetailsComplete,
		userFlags.isBenefitsDetailsComplete,
		userFlags.isPersonalDetailsComplete,
	];

	const completedFields = profileFields.filter((field) => field).length;
	profileCompletionScore = Math.round(
		(completedFields / profileFields.length) * 100
	);

	// 2. Attendance Score (40% weight)
	if (attendanceLogs && attendanceLogs.length > 0) {
		attendanceScore = calculateAttendanceScore(attendanceLogs);
	}

	// 3. Consistency Score (20% weight)
	if (attendanceLogs && attendanceLogs.length > 0) {
		consistencyScore = calculateConsistencyScore(attendanceLogs);
	}

	// 4. Engagement Score (10% weight)
	if (attendanceLogs && attendanceLogs.length > 0) {
		engagementScore = calculateEngagementScore(attendanceLogs);
	}

	// Calculate weighted total score
	const totalScore = Math.round(
		attendanceScore * 0.4 +
			profileCompletionScore * 0.3 +
			consistencyScore * 0.2 +
			engagementScore * 0.1
	);

	// Determine achievement level
	const achievementLevel = getAchievementLevel(totalScore);

	return {
		totalScore,
		achievementLevel,
		breakdown: {
			attendance: attendanceScore,
			profileCompletion: profileCompletionScore,
			consistency: consistencyScore,
			engagement: engagementScore,
		},
		metrics: {
			attendanceRate: `${attendanceScore}%`,
			profileComplete: `${profileCompletionScore}%`,
			consistencyRate: `${consistencyScore}%`,
			engagementRate: `${engagementScore}%`,
		},
	};
};

/**
 * Calculate attendance score based on attendance logs
 */
const calculateAttendanceScore = (attendanceLogs) => {
	if (!attendanceLogs || attendanceLogs.length === 0) return 0;

	// Get last 30 days of logs
	const thirtyDaysAgo = dayjs().subtract(30, 'day');
	const recentLogs = attendanceLogs.filter((log) =>
		dayjs(log.time).isAfter(thirtyDaysAgo)
	);

	if (recentLogs.length === 0) return 0;

	// Count clock-in and clock-out pairs
	const clockIns = recentLogs.filter((log) => log.type === 'clockIn');
	const clockOuts = recentLogs.filter((log) => log.type === 'clockOut');

	// Calculate attendance rate (assuming 22 working days per month)
	const expectedWorkingDays = 22;
	const actualWorkingDays = Math.min(clockIns.length, clockOuts.length);
	const attendanceRate = (actualWorkingDays / expectedWorkingDays) * 100;

	// Cap at 100%
	return Math.min(Math.round(attendanceRate), 100);
};

/**
 * Calculate consistency score based on regular patterns
 */
const calculateConsistencyScore = (attendanceLogs) => {
	if (!attendanceLogs || attendanceLogs.length < 10) return 50; // Default for new users

	// Get last 30 days of clock-ins
	const thirtyDaysAgo = dayjs().subtract(30, 'day');
	const recentClockIns = attendanceLogs
		.filter(
			(log) => log.type === 'clockIn' && dayjs(log.time).isAfter(thirtyDaysAgo)
		)
		.map((log) => dayjs(log.time));

	if (recentClockIns.length < 5) return 50;

	// Calculate average clock-in time
	const clockInHours = recentClockIns.map(
		(time) => time.hour() + time.minute() / 60
	);
	const avgClockInTime =
		clockInHours.reduce((sum, hour) => sum + hour, 0) / clockInHours.length;

	// Calculate standard deviation
	const variance =
		clockInHours.reduce(
			(sum, hour) => sum + Math.pow(hour - avgClockInTime, 2),
			0
		) / clockInHours.length;
	const standardDeviation = Math.sqrt(variance);

	// Convert to consistency score (lower deviation = higher score)
	// Standard deviation of 0.5 hours (30 minutes) = 80% score
	// Standard deviation of 1 hour = 60% score
	// Standard deviation of 2+ hours = 20% score
	let consistencyScore = 100 - standardDeviation * 40;
	return Math.max(Math.min(Math.round(consistencyScore), 100), 20);
};

/**
 * Calculate engagement score based on break patterns and activity
 */
const calculateEngagementScore = (attendanceLogs) => {
	if (!attendanceLogs || attendanceLogs.length === 0) return 50;

	// Get last 30 days of logs
	const thirtyDaysAgo = dayjs().subtract(30, 'day');
	const recentLogs = attendanceLogs.filter((log) =>
		dayjs(log.time).isAfter(thirtyDaysAgo)
	);

	const breaks = recentLogs.filter(
		(log) => log.type === 'start-break' || log.type === 'end-break'
	);
	const workingDays = recentLogs.filter((log) => log.type === 'clockIn').length;

	if (workingDays === 0) return 50;

	// Calculate break frequency (healthy break pattern)
	const breakDays = new Set(
		breaks.map((log) => dayjs(log.time).format('YYYY-MM-DD'))
	).size;
	const breakFrequency = (breakDays / workingDays) * 100;

	// Optimal break frequency is 60-80% of working days
	let engagementScore;
	if (breakFrequency >= 60 && breakFrequency <= 80) {
		engagementScore = 100;
	} else if (breakFrequency >= 40 && breakFrequency < 60) {
		engagementScore = 80;
	} else if (breakFrequency >= 20 && breakFrequency < 40) {
		engagementScore = 60;
	} else {
		engagementScore = 40;
	}

	return Math.round(engagementScore);
};

/**
 * Determine achievement level based on total score
 */
const getAchievementLevel = (score) => {
	if (score >= 90) return 'platinum';
	if (score >= 75) return 'gold';
	if (score >= 60) return 'silver';
	return 'bronze';
};

/**
 * Get achievement level details
 */
export const getAchievementDetails = (level) => {
	const details = {
		bronze: {
			name: 'Bronze',
			color: '#CD7F32',
			bgColor: 'bg-amber-100',
			textColor: 'text-amber-800',
			description: 'Good foundation, room for growth',
		},
		silver: {
			name: 'Silver',
			color: '#C0C0C0',
			bgColor: 'bg-gray-100',
			textColor: 'text-gray-800',
			description: 'Solid performance, keep it up',
		},
		gold: {
			name: 'Gold',
			color: '#FFD700',
			bgColor: 'bg-yellow-100',
			textColor: 'text-yellow-800',
			description: 'Excellent performance, well done',
		},
		platinum: {
			name: 'Platinum',
			color: '#E5E4E2',
			bgColor: 'bg-slate-100',
			textColor: 'text-slate-800',
			description: 'Outstanding performance, exemplary',
		},
	};

	return details[level] || details.bronze;
};
